<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频处理测试页面</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .checkbox-group label {
            margin-left: 8px;
            margin-bottom: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .result-textarea {
            margin-top: 20px;
            width: 100%;
            height: 300px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .api-key-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #3498db;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>音频处理测试页面</h1>
        
        <div class="api-key-section">
            <div class="form-group">
                <label for="apiKey">API Key:</label>
                <input type="text" id="apiKey" placeholder="输入您的API Key">
            </div>
        </div>
        
        <div class="form-group">
            <label for="problem">问题描述:</label>
            <textarea id="problem" placeholder="请输入您的问题描述"></textarea>
        </div>
        
        <div class="checkbox-group">
            <input type="checkbox" id="isAccurate" checked>
            <label for="isAccurate">精确模式</label>
        </div>
        
        <div class="button-group">
            <button id="submitBtn">提交请求</button>
            <button id="clearBtn">清除结果</button>
            <button id="copyBtn">复制结果</button>
        </div>
        
        <div class="loading">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
        
        <div class="form-group">
            <label for="resultArea">结果:</label>
            <textarea id="resultArea" class="result-textarea" readonly></textarea>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const apiKeyInput = document.getElementById('apiKey');
            const problemInput = document.getElementById('problem');
            const isAccurateCheckbox = document.getElementById('isAccurate');
            const submitBtn = document.getElementById('submitBtn');
            const clearBtn = document.getElementById('clearBtn');
            const copyBtn = document.getElementById('copyBtn');
            const resultArea = document.getElementById('resultArea');
            const loadingDiv = document.querySelector('.loading');
            
            // 从localStorage加载API Key
            const savedApiKey = localStorage.getItem('audioTestApiKey');
            if (savedApiKey) {
                apiKeyInput.value = savedApiKey;
            }
            
            submitBtn.addEventListener('click', function() {
                const apiKey = apiKeyInput.value.trim();
                const problem = problemInput.value.trim();
                const isAccurate = isAccurateCheckbox.checked;
                
                if (!apiKey) {
                    alert('请输入API Key');
                    return;
                }
                
                if (!problem) {
                    alert('请输入问题描述');
                    return;
                }
                
                // 保存API Key到localStorage
                localStorage.setItem('audioTestApiKey', apiKey);
                
                // 清空之前的结果
                resultArea.value = '';
                
                // 显示加载状态
                loadingDiv.style.display = 'block';
                submitBtn.disabled = true;

                // 如果存在旧的EventSource，关闭它
                if (window.eventSource) {
                    window.eventSource.close();
                }
                
                try {
                    // 构建URL，包含查询参数
                    const url = new URL('/api/codeMoss/processAudio', window.location.origin);
                    url.searchParams.append('problem', problem);
                    url.searchParams.append('isAccurate', isAccurate);
                    url.searchParams.append('apiKey', apiKey);
                    
                    // 创建EventSource对象
                    const eventSource = new EventSource(url);
                    window.eventSource = eventSource; // 保存全局引用以便稍后关闭
                    
                    // EventSource不支持自定义头，所以不需要设置认证头
                    
                    // 处理消息事件
                    eventSource.addEventListener('message', function(event) {
                        const data = event.data;
                        if (data) {
                            resultArea.value += data;
                            // 自动滚动到底部
                            resultArea.scrollTop = resultArea.scrollHeight;
                            loadingDiv.style.display = 'none';
                        }
                    });
                    
                    // 处理错误
                    eventSource.addEventListener('error', function(event) {
                        console.error('EventSource错误:', event);
                        loadingDiv.style.display = 'none';
                        submitBtn.disabled = false;
                        
                        // 关闭连接
                        eventSource.close();
                        
                        // 回退到使用XHR的方式
                        fallbackToXHR(apiKey, problem, isAccurate);
                    });
                    
                    // 监听连接打开事件
                    eventSource.addEventListener('open', function() {
                        console.log('EventSource连接已建立');
                    });
                    
                    // 处理连接结束
                    eventSource.onclose = function() {
                        console.log('EventSource连接已关闭');
                        submitBtn.disabled = false;
                        loadingDiv.style.display = 'none';
                    };
                } catch (e) {
                    console.error('创建EventSource失败，回退到XHR:', e);
                    fallbackToXHR(apiKey, problem, isAccurate);
                }
            });
            
            // XHR回退方法
            function fallbackToXHR(apiKey, problem, isAccurate) {
                console.log('使用XMLHttpRequest作为回退');
                
                // 使用XMLHttpRequest发送POST请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'http://localhost:8080/api/codeMoss/processAudio?problem=' + encodeURIComponent(problem) + '&isAccurate=' + isAccurate, true);
                xhr.setRequestHeader('Authorization', apiKey);
                xhr.setRequestHeader('Accept', 'text/event-stream');
                xhr.setRequestHeader('Cache-Control', 'no-cache');
                xhr.responseType = 'text';
                xhr.withCredentials = true; // 允许跨域请求携带凭证
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 3) { // 数据接收中
                        // 获取新接收的数据
                        const newData = xhr.response.substring(xhr.seenBytes || 0);
                        xhr.seenBytes = xhr.response.length;
                        
                        // 处理SSE格式数据
                        const eventChunks = newData.split("\n\n");
                        eventChunks.forEach(chunk => {
                            if (!chunk.trim()) return;

                            console.log('接收到数据块:', chunk);
                            
                            // 解析SSE格式
                            const lines = chunk.split("\n");
                            let data = "";
                            
                            lines.forEach(line => {
                                if (line.startsWith("data:")) {
                                    data = line.substring(5).trim();
                                    if (data) {
                                        const item = JSON.parse(data); // 尝试解析JSON，确保数据格式正确
                                        if(item.choices && item.choices.length > 0) {
                                            data = item.choices[0].delta.content; // 获取第一个选择的文本
                                        } else {
                                            data = ''; // 如果没有有效数据，清空
                                        }
                                        if (data) {
                                            resultArea.value += data;
                                            // 自动滚动到底部
                                            resultArea.scrollTop = resultArea.scrollHeight;
                                            loadingDiv.style.display = 'none';
                                        }
                                    }
                                }
                            });
                        });
                    } else if (xhr.readyState === 4) { // 请求完成
                        loadingDiv.style.display = 'none';
                        submitBtn.disabled = false;
                        
                        if (xhr.status !== 200) {
                            console.error('请求失败:', xhr.status, xhr.statusText);
                            resultArea.value += '\n请求失败: ' + xhr.status + ' ' + xhr.statusText;
                            resultArea.scrollTop = resultArea.scrollHeight;
                        }
                    }
                };
                
                xhr.onerror = function() {
                    loadingDiv.style.display = 'none';
                    submitBtn.disabled = false;
                    console.error('网络错误');
                    resultArea.value += '\n网络错误，请求失败';
                    resultArea.scrollTop = resultArea.scrollHeight;
                };
                
                xhr.send();
            }
            
            clearBtn.addEventListener('click', function() {
                resultArea.value = '';
            });
            
            copyBtn.addEventListener('click', function() {
                resultArea.select();
                // 使用现代的Clipboard API
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(resultArea.value)
                        .then(() => {
                            // 显示临时提示
                            const originalText = copyBtn.textContent;
                            copyBtn.textContent = '已复制!';
                            setTimeout(() => {
                                copyBtn.textContent = originalText;
                            }, 1500);
                        })
                        .catch(err => {
                            console.error('复制失败:', err);
                            // 回退到旧方法
                            document.execCommand('copy');
                            alert('结果已复制到剪贴板');
                        });
                } else {
                    // 回退到旧方法
                    document.execCommand('copy');
                    alert('结果已复制到剪贴板');
                }
            });
        });
    </script>
</body>
</html> 