#!/bin/bash

# MySQL GPG密钥修复工具
# 专门用于解决MySQL安装时的GPG密钥问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    exit 1
fi

# 检测系统版本
if [[ -f /etc/os-release ]]; then
    . /etc/os-release
    VER=$VERSION_ID
else
    log_error "无法检测系统版本"
    exit 1
fi

log_info "检测到系统版本: $VER"

# 检测包管理器
if command -v dnf >/dev/null 2>&1; then
    PKG_MANAGER="dnf"
elif command -v yum >/dev/null 2>&1; then
    PKG_MANAGER="yum"
else
    log_error "未找到包管理器 (yum/dnf)"
    exit 1
fi

log_info "使用包管理器: $PKG_MANAGER"

# 备份现有配置
log_info "备份现有MySQL仓库配置..."
if [[ -d /etc/yum.repos.d ]]; then
    mkdir -p /root/mysql_backup_$(date +%Y%m%d_%H%M%S)
    cp /etc/yum.repos.d/mysql* /root/mysql_backup_$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true
fi

# 清理现有MySQL仓库和密钥
log_info "清理现有MySQL仓库和GPG密钥..."

# 移除MySQL仓库包
rpm -qa | grep mysql.*community.*release | xargs -r rpm -e 2>/dev/null || true

# 清理MySQL相关的GPG密钥
log_info "清理MySQL GPG密钥..."
rpm -q gpg-pubkey --qf '%{NAME}-%{VERSION}-%{RELEASE}\t%{SUMMARY}\n' | grep -i mysql | awk '{print $1}' | xargs -r rpm -e 2>/dev/null || true

# 清理仓库文件
rm -f /etc/yum.repos.d/mysql*.repo

# 清理缓存
$PKG_MANAGER clean all

log_info "清理完成"

# 重新安装MySQL仓库
log_info "重新安装MySQL仓库..."

cd /tmp

# 根据系统版本选择合适的仓库包
if [[ "$VER" =~ ^9 ]]; then
    REPO_PKG="mysql80-community-release-el9-4.noarch.rpm"
elif [[ "$VER" =~ ^8 ]]; then
    REPO_PKG="mysql80-community-release-el8-4.noarch.rpm"
elif [[ "$VER" =~ ^7 ]]; then
    REPO_PKG="mysql80-community-release-el7-7.noarch.rpm"
else
    REPO_PKG="mysql80-community-release-el8-4.noarch.rpm"
fi

log_info "下载MySQL仓库包: $REPO_PKG"
if wget https://dev.mysql.com/get/$REPO_PKG; then
    log_info "安装MySQL仓库包..."
    rpm -ivh $REPO_PKG
    rm -f $REPO_PKG
    log_success "MySQL仓库包安装成功"
else
    log_error "无法下载MySQL仓库包"
    exit 1
fi

# 重新导入GPG密钥
log_info "导入MySQL GPG密钥..."

# 确保GPG密钥目录存在
mkdir -p /etc/pki/rpm-gpg/

# 导入所有相关的MySQL GPG密钥
declare -A key_files=(
    ["RPM-GPG-KEY-mysql"]="https://repo.mysql.com/RPM-GPG-KEY-mysql"
    ["RPM-GPG-KEY-mysql-2022"]="https://repo.mysql.com/RPM-GPG-KEY-mysql-2022"
)

for key_name in "${!key_files[@]}"; do
    key_url="${key_files[$key_name]}"
    key_file="/etc/pki/rpm-gpg/$key_name"
    
    log_info "下载并导入: $key_name"
    if wget -q "$key_url" -O "$key_file"; then
        rpm --import "$key_file"
        log_success "已导入: $key_name"
    else
        log_warning "无法下载: $key_name"
    fi
done

# 重建RPM数据库
log_info "重建RPM数据库..."
rpm --rebuilddb

# 更新仓库缓存
log_info "更新仓库缓存..."
$PKG_MANAGER makecache

# 验证配置
log_info "验证MySQL仓库配置..."

if $PKG_MANAGER repolist enabled | grep -q mysql80-community; then
    log_success "MySQL仓库已启用"
else
    log_warning "MySQL仓库未启用，尝试启用..."
    $PKG_MANAGER config-manager --enable mysql80-community 2>/dev/null || true
fi

# 测试包列表
log_info "测试MySQL包可用性..."
if $PKG_MANAGER list available mysql-community-server 2>/dev/null | grep -q mysql-community-server; then
    log_success "MySQL Community Server包可用"
else
    log_error "MySQL Community Server包不可用"
    exit 1
fi

# 显示可用的MySQL包
log_info "可用的MySQL包："
$PKG_MANAGER list available | grep mysql-community | head -10

log_success "MySQL仓库和GPG密钥配置完成！"
log_info "现在可以使用以下命令安装MySQL："
echo "  $PKG_MANAGER install -y mysql-community-server mysql-community-client"

log_info "如果仍然遇到GPG问题，可以使用:"
echo "  $PKG_MANAGER install -y --nogpgcheck mysql-community-server mysql-community-client"
