#!/bin/bash

# MySQL 8.0 一键安装启动脚本
# 支持 Ubuntu/Debian 和 CentOS/RHEL 系统
# 作者: AI Assistant
# 日期: 2025-07-09
# 版本: 2.1 - 添加临时密码显示功能

# 使用方法:
#   sudo ./install_mysql8.sh                    # 完整安装MySQL
#   sudo ./install_mysql8.sh --show-temp-password  # 仅显示临时密码
#   sudo ./install_mysql8.sh -t                 # 仅显示临时密码(简写)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    elif [[ -f /etc/redhat-release ]]; then
        OS="CentOS"
        VER=$(rpm -q --qf "%{VERSION}" $(rpm -q --whatprovides redhat-release))
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查MySQL是否已安装
check_mysql_installed() {
    local mysql_installed=false
    local service_running=false
    
    # 检查MySQL命令行工具
    if command -v mysql >/dev/null 2>&1; then
        mysql_installed=true
        log_warning "MySQL 客户端已经安装"
        mysql --version 2>/dev/null || true
    fi
    
    # 检查MySQL服务
    if systemctl list-unit-files | grep -q mysql || systemctl list-unit-files | grep -q mysqld; then
        mysql_installed=true
        log_warning "MySQL 服务已安装"
        
        # 检查服务状态
        if systemctl is-active --quiet mysqld 2>/dev/null || systemctl is-active --quiet mysql 2>/dev/null; then
            service_running=true
            log_info "MySQL 服务正在运行"
        fi
    fi
    
    # 检查MySQL包是否已安装
    if dpkg -l | grep -q mysql-server 2>/dev/null || rpm -qa | grep -q mysql-server 2>/dev/null || rpm -qa | grep -q mysql-community-server 2>/dev/null; then
        mysql_installed=true
        log_warning "MySQL 服务器包已安装"
    fi
    
    if [[ "$mysql_installed" == true ]]; then
        echo
        log_warning "检测到MySQL已安装，请选择操作："
        echo "1) 跳过安装，仅配置现有MySQL"
        echo "2) 重新安装MySQL (会删除现有数据)"
        echo "3) 退出脚本"
        echo
        read -p "请选择 (1/2/3): " choice
        
        case $choice in
            1)
                log_info "跳过安装步骤，仅进行配置"
                SKIP_INSTALLATION=true
                return 0
                ;;
            2)
                log_warning "将重新安装MySQL，这会删除所有现有数据！"
                read -p "确认要继续吗? (yes/no): " confirm
                if [[ "$confirm" != "yes" ]]; then
                    log_info "取消重新安装，退出脚本"
                    exit 0
                fi
                
                # 停止服务
                if [[ "$service_running" == true ]]; then
                    log_info "停止MySQL服务..."
                    systemctl stop mysqld 2>/dev/null || systemctl stop mysql 2>/dev/null || true
                fi
                
                # 卸载现有包
                log_info "卸载现有MySQL包..."
                if command -v apt >/dev/null 2>&1; then
                    apt remove --purge -y mysql-server mysql-client mysql-common mysql-community-server mysql-community-client 2>/dev/null || true
                    apt autoremove -y 2>/dev/null || true
                elif command -v yum >/dev/null 2>&1 || command -v dnf >/dev/null 2>&1; then
                    PKG_MANAGER=$(command -v dnf >/dev/null 2>&1 && echo "dnf" || echo "yum")
                    $PKG_MANAGER remove -y mysql-server mysql mysql-community-server mysql-community-client 2>/dev/null || true
                fi
                
                # 备份并清理数据目录 (可选)
                if [[ -d /var/lib/mysql ]]; then
                    log_warning "备份现有数据目录到 /var/lib/mysql.backup.$(date +%Y%m%d_%H%M%S)"
                    mv /var/lib/mysql /var/lib/mysql.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
                fi
                
                SKIP_INSTALLATION=false
                return 0
                ;;
            3|*)
                log_info "退出安装"
                exit 0
                ;;
        esac
    fi
    
    SKIP_INSTALLATION=false
}

# 设置MySQL root密码
set_mysql_password() {
    while true; do
        read -s -p "请设置MySQL root密码 (至少8位字符): " MYSQL_ROOT_PASSWORD
        echo
        if [[ ${#MYSQL_ROOT_PASSWORD} -ge 8 ]]; then
            read -s -p "请再次确认密码: " MYSQL_ROOT_PASSWORD_CONFIRM
            echo
            if [[ "$MYSQL_ROOT_PASSWORD" == "$MYSQL_ROOT_PASSWORD_CONFIRM" ]]; then
                break
            else
                log_error "两次密码输入不一致，请重新输入"
            fi
        else
            log_error "密码长度至少需要8位字符"
        fi
    done
}

# Ubuntu/Debian 安装函数
install_mysql_debian() {
    log_info "在 Debian/Ubuntu 系统上安装 MySQL 8.0..."
    
    # 更新包索引
    log_info "更新包索引..."
    apt update
    
    # 安装必要的包
    log_info "安装必要的依赖包..."
    apt install -y wget lsb-release gnupg curl
    
    # 方法1: 尝试安装官方MySQL仓库
    log_info "配置MySQL官方APT仓库..."
    cd /tmp
    
    # 检查MySQL仓库是否已配置
    if apt-cache policy | grep -q "dev.mysql.com"; then
        log_info "MySQL官方仓库已配置，跳过仓库配置..."
    else
        # 下载并安装MySQL APT配置包
        if wget https://dev.mysql.com/get/mysql-apt-config_0.8.29-1_all.deb; then
            # 预配置选择MySQL 8.0
            export DEBIAN_FRONTEND=noninteractive
            echo "mysql-apt-config mysql-apt-config/select-server select mysql-8.0" | debconf-set-selections
            
            log_info "安装MySQL APT配置包..."
            if dpkg -i mysql-apt-config_0.8.29-1_all.deb 2>/dev/null; then
                log_success "MySQL仓库配置成功"
            else
                log_warning "MySQL仓库可能已存在，继续配置..."
            fi
            
            # 清理临时文件
            rm -f /tmp/mysql-apt-config_0.8.29-1_all.deb
        else
            log_warning "无法下载MySQL官方仓库配置，尝试安装系统默认版本..."
            install_mysql_standard_debian
            return
        fi
    fi
    
    # 更新包索引
    log_info "更新包索引..."
    apt update
    
    # 预配置MySQL密码
    export DEBIAN_FRONTEND=noninteractive
    echo "mysql-community-server mysql-community-server/root-pass password $MYSQL_ROOT_PASSWORD" | debconf-set-selections
    echo "mysql-community-server mysql-community-server/re-root-pass password $MYSQL_ROOT_PASSWORD" | debconf-set-selections
    
    # 尝试安装MySQL Community版本
    log_info "安装MySQL 8.0 Community Server..."
    if apt install -y mysql-community-server mysql-community-client; then
        log_success "MySQL Community版本安装成功"
    else
        log_warning "MySQL Community版本安装失败，尝试安装标准版本..."
        install_mysql_standard_debian
    fi
}

# 安装系统默认MySQL版本 (Ubuntu/Debian)
install_mysql_standard_debian() {
    log_info "安装系统默认MySQL 8.0版本..."
    
    # 预配置MySQL密码
    export DEBIAN_FRONTEND=noninteractive
    echo "mysql-server mysql-server/root_password password $MYSQL_ROOT_PASSWORD" | debconf-set-selections
    echo "mysql-server mysql-server/root_password_again password $MYSQL_ROOT_PASSWORD" | debconf-set-selections
    
    # 安装MySQL Server
    if apt install -y mysql-server mysql-client; then
        log_success "MySQL标准版本安装成功"
    else
        log_error "MySQL安装失败"
        exit 1
    fi
}

# CentOS/RHEL 安装函数
install_mysql_rhel() {
    log_info "在 CentOS/RHEL 系统上安装 MySQL 8.0..."
    
    # 检测包管理器
    if command -v dnf >/dev/null 2>&1; then
        PKG_MANAGER="dnf"
    elif command -v yum >/dev/null 2>&1; then
        PKG_MANAGER="yum"
    else
        log_error "未找到包管理器 (yum/dnf)"
        exit 1
    fi
    
    # 检查并安装EPEL仓库
    if ! rpm -qa | grep -q epel-release; then
        log_info "安装EPEL仓库..."
        $PKG_MANAGER install -y epel-release
    fi
    
    # 移除冲突的数据库包
    log_info "移除冲突的数据库包..."
    $PKG_MANAGER remove -y mariadb-libs mysql-libs 2>/dev/null || true
    
    # 尝试安装MySQL官方仓库
    log_info "配置MySQL官方仓库..."
    cd /tmp
    
    # 根据系统版本选择合适的仓库包
    if [[ "$VER" =~ ^9 ]]; then
        REPO_PKG="mysql80-community-release-el9-4.noarch.rpm"
    elif [[ "$VER" =~ ^8 ]]; then
        REPO_PKG="mysql80-community-release-el8-4.noarch.rpm"
    elif [[ "$VER" =~ ^7 ]]; then
        REPO_PKG="mysql80-community-release-el7-7.noarch.rpm"
        # 对于CentOS 7，特别处理GPG密钥问题
        log_info "检测到CentOS 7系统，将应用特殊的GPG密钥处理..."
        CENTOS7_GPG_FIX=true
    else
        REPO_PKG="mysql80-community-release-el8-4.noarch.rpm"  # 默认使用el8
    fi
    
    # 检查MySQL仓库是否已安装
    if rpm -qa | grep -q mysql80-community-release; then
        log_info "MySQL官方仓库已安装，跳过仓库配置..."
    else
        if wget https://dev.mysql.com/get/$REPO_PKG; then
            log_info "安装MySQL官方仓库包..."
            if rpm -ivh $REPO_PKG 2>/dev/null; then
                log_success "MySQL仓库安装成功"
            else
                log_warning "MySQL仓库可能已存在，继续安装..."
            fi
            
            # 清理临时文件
            rm -f /tmp/$REPO_PKG
        else
            log_warning "无法下载MySQL官方仓库配置，尝试安装系统默认版本..."
            install_mysql_standard_rhel
            return
        fi
    fi
    
    # 导入GPG密钥 - 修复CentOS 7.6的GPG密钥问题
    log_info "导入MySQL GPG密钥..."

    # 对于CentOS 7，使用特殊的GPG密钥处理
    if [[ "$CENTOS7_GPG_FIX" == true ]]; then
        log_info "应用CentOS 7 GPG密钥修复方案..."

        # 清理所有MySQL相关的GPG密钥
        rpm -qa | grep gpg-pubkey | grep -i mysql | xargs rpm -e 2>/dev/null || true
        rpm -e --allmatches gpg-pubkey-5072e1f5-564ca501 2>/dev/null || true
        rpm -e --allmatches gpg-pubkey-3a79bd29-5b9bcb4a 2>/dev/null || true
        rpm -e --allmatches gpg-pubkey-a4a9406876fcecfa 2>/dev/null || true

        # 手动下载并导入正确的GPG密钥
        cd /tmp
        log_info "下载MySQL GPG密钥文件..."

        # 尝试多个GPG密钥源
        for key_url in \
            "https://repo.mysql.com/RPM-GPG-KEY-mysql-2022" \
            "https://repo.mysql.com/RPM-GPG-KEY-mysql-2023" \
            "https://dev.mysql.com/doc/refman/8.0/en/checking-gpg-signature.html"; do

            key_file=$(basename "$key_url")
            if wget -q --timeout=10 "$key_url" -O "$key_file" 2>/dev/null; then
                if [[ -s "$key_file" ]] && grep -q "BEGIN PGP PUBLIC KEY BLOCK" "$key_file" 2>/dev/null; then
                    log_info "导入GPG密钥: $key_file"
                    rpm --import "$key_file" 2>/dev/null || true
                fi
                rm -f "$key_file" 2>/dev/null || true
            fi
        done

        # 如果仍然无法导入密钥，创建一个临时的yum配置来跳过GPG检查
        if ! rpm -qa | grep -q "gpg-pubkey.*mysql"; then
            log_warning "GPG密钥导入失败，将在安装时跳过GPG验证"

            # 创建临时的repo配置文件，禁用GPG检查
            if [[ -f /etc/yum.repos.d/mysql-community.repo ]]; then
                cp /etc/yum.repos.d/mysql-community.repo /etc/yum.repos.d/mysql-community.repo.backup
                sed -i 's/gpgcheck=1/gpgcheck=0/g' /etc/yum.repos.d/mysql-community.repo
                log_info "已临时禁用MySQL仓库的GPG检查"
            fi
        fi
    else
        # 标准的GPG密钥处理
        # 清理旧的GPG密钥
        rpm -e --allmatches gpg-pubkey-5072e1f5-564ca501 2>/dev/null || true
        rpm -e --allmatches gpg-pubkey-3a79bd29-5b9bcb4a 2>/dev/null || true

        # 导入最新的GPG密钥
        rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2022 2>/dev/null || true
        rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2023 2>/dev/null || true
    fi

    # 如果GPG密钥导入失败，尝试手动下载并导入
    if ! rpm -qa | grep -q "gpg-pubkey.*mysql"; then
        log_warning "GPG密钥导入可能失败，尝试手动下载..."
        cd /tmp
        wget -q https://repo.mysql.com/RPM-GPG-KEY-mysql-2022 -O RPM-GPG-KEY-mysql-2022 || true
        wget -q https://repo.mysql.com/RPM-GPG-KEY-mysql-2023 -O RPM-GPG-KEY-mysql-2023 || true

        if [[ -f RPM-GPG-KEY-mysql-2022 ]]; then
            rpm --import RPM-GPG-KEY-mysql-2022 2>/dev/null || true
        fi
        if [[ -f RPM-GPG-KEY-mysql-2023 ]]; then
            rpm --import RPM-GPG-KEY-mysql-2023 2>/dev/null || true
        fi

        rm -f RPM-GPG-KEY-mysql-* 2>/dev/null || true
    fi

    # 清理YUM缓存
    log_info "清理包管理器缓存..."
    $PKG_MANAGER clean all
    $PKG_MANAGER makecache

    # 尝试安装MySQL Community版本
    log_info "安装MySQL 8.0 Community Server..."

    # 安装成功标志
    local install_success=false

    # 尝试正常安装
    if $PKG_MANAGER install -y mysql-community-server mysql-community-client 2>/dev/null; then
        install_success=true
        log_success "MySQL Community版本安装成功"
    # 如果GPG验证失败，尝试跳过GPG检查
    elif $PKG_MANAGER install -y --nogpgcheck mysql-community-server mysql-community-client 2>/dev/null; then
        install_success=true
        log_warning "使用--nogpgcheck选项安装成功"
        log_success "MySQL Community版本安装成功"
    # 如果仍然失败，尝试安装标准版本
    else
        log_warning "MySQL Community版本安装失败，尝试安装标准版本..."
        install_mysql_standard_rhel
        return
    fi

    # 如果是CentOS 7且临时禁用了GPG检查，现在恢复设置
    if [[ "$CENTOS7_GPG_FIX" == true ]] && [[ -f /etc/yum.repos.d/mysql-community.repo.backup ]]; then
        log_info "恢复MySQL仓库GPG检查设置..."
        mv /etc/yum.repos.d/mysql-community.repo.backup /etc/yum.repos.d/mysql-community.repo
    fi
}

# 安装系统默认MySQL版本 (CentOS/RHEL)
install_mysql_standard_rhel() {
    log_info "安装系统默认MySQL版本..."
    
    # 检测包管理器
    if command -v dnf >/dev/null 2>&1; then
        PKG_MANAGER="dnf"
    else
        PKG_MANAGER="yum"
    fi
    
    # 尝试安装MySQL Server
    if $PKG_MANAGER install -y mysql-server mysql; then
        log_success "MySQL标准版本安装成功"
    else
        log_error "MySQL安装失败，请检查系统仓库配置"
        exit 1
    fi
}

# 验证MySQL安装
verify_mysql_installation() {
    log_info "验证MySQL安装..."
    
    # 检查MySQL服务是否存在 (支持不同的服务名称)
    local mysql_service=""
    if systemctl list-unit-files | grep -q "^mysqld.service"; then
        mysql_service="mysqld"
    elif systemctl list-unit-files | grep -q "^mysql.service"; then
        mysql_service="mysql"
    else
        log_error "未找到MySQL服务 (mysqld 或 mysql)"
        return 1
    fi
    
    log_info "找到MySQL服务: $mysql_service"
    
    # 检查MySQL命令是否可用
    if ! command -v mysql >/dev/null 2>&1; then
        log_error "MySQL客户端未正确安装"
        return 1
    fi
    
    # 检查mysqld服务
    if ! command -v mysqld >/dev/null 2>&1; then
        log_error "MySQL服务端未正确安装"
        return 1
    fi
    
    log_success "MySQL安装验证通过"
    
    # 设置全局变量供后续使用
    export MYSQL_SERVICE_NAME="$mysql_service"
    return 0
}

# 启动并配置MySQL服务
configure_mysql_service() {
    local service_name="${MYSQL_SERVICE_NAME:-mysqld}"
    
    log_info "启动MySQL服务 ($service_name)..."
    systemctl start $service_name
    systemctl enable $service_name
    
    log_success "MySQL服务已启动并设置为开机自启"
    
    # 检查服务状态
    if systemctl is-active --quiet $service_name; then
        log_success "MySQL服务运行正常"
    else
        log_error "MySQL服务启动失败"
        systemctl status $service_name
        exit 1
    fi
}

# 检查并显示MySQL临时密码
check_and_show_temp_password() {
    local temp_password=""

    # 检查不同可能的日志文件位置
    local log_files=(
        "/var/log/mysqld.log"
        "/var/log/mysql/error.log"
        "/var/log/mysql.log"
    )

    for log_file in "${log_files[@]}"; do
        if [[ -f "$log_file" ]]; then
            temp_password=$(grep 'temporary password' "$log_file" 2>/dev/null | tail -1 | awk '{print $NF}')
            if [[ -n "$temp_password" ]]; then
                echo
                log_success "在 $log_file 中找到MySQL临时密码！"
                echo "========================================================"
                echo -e "${GREEN}MySQL临时密码: ${YELLOW}$temp_password${NC}"
                echo "========================================================"
                echo -e "${BLUE}使用方法:${NC}"
                echo "  mysql -u root -p'$temp_password' --connect-expired-password"
                echo "  然后执行: ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
                echo "========================================================"
                echo

                # 设置全局变量供其他函数使用
                export FOUND_TEMP_PASSWORD="$temp_password"
                return 0
            fi
        fi
    done

    log_info "未找到MySQL临时密码，可能是首次安装或使用了不同的认证方式"
    return 1
}

# 配置MySQL root密码 (针对不同安装方式)
configure_mysql_password() {
    log_info "配置MySQL安全设置..."
    
    # 尝试不同的密码设置方法
    local password_set=false
    
    # 方法1: 检查并使用临时密码
    log_info "检查是否存在MySQL临时密码..."
    if check_and_show_temp_password && [[ -n "$FOUND_TEMP_PASSWORD" ]]; then
        log_info "正在使用临时密码重置root密码..."

        if mysql -u root -p"$FOUND_TEMP_PASSWORD" --connect-expired-password -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';" 2>/dev/null; then
            password_set=true
            log_success "使用临时密码重置成功"
        else
            log_warning "自动重置失败，请使用上面显示的临时密码手动重置"
            log_info "您可以稍后使用以下命令手动重置："
            echo "  mysql -u root -p'$FOUND_TEMP_PASSWORD' --connect-expired-password"
            echo "  然后执行: ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
        fi
    fi
    
    # 方法2: 尝试无密码连接 (新安装可能允许)
    if [[ "$password_set" == false ]]; then
        log_info "尝试无密码连接设置密码..."
        if mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';" 2>/dev/null; then
            password_set=true
            log_success "无密码连接设置成功"
        fi
    fi
    
    # 方法3: 使用mysql_secure_installation的自动化版本
    if [[ "$password_set" == false ]]; then
        log_info "尝试使用系统认证..."
        if sudo mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';" 2>/dev/null; then
            password_set=true
            log_success "系统认证设置成功"
        fi
    fi
    
    # 方法4: 停止服务，使用--skip-grant-tables重启
    if [[ "$password_set" == false ]]; then
        local service_name="${MYSQL_SERVICE_NAME:-mysqld}"
        
        log_warning "尝试安全模式重置密码..."
        systemctl stop $service_name
        
        # 创建临时配置文件
        cat > /tmp/mysql-skip-grants.cnf << 'EOF'
[mysqld]
skip-grant-tables
skip-networking
EOF
        
        # 使用临时配置启动MySQL
        mysqld --defaults-file=/tmp/mysql-skip-grants.cnf --user=mysql &
        MYSQL_PID=$!
        
        # 等待MySQL启动
        sleep 5
        
        # 重置密码
        if mysql -u root -e "FLUSH PRIVILEGES; ALTER USER 'root'@'localhost' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';" 2>/dev/null; then
            password_set=true
            log_success "安全模式重置成功"
        fi
        
        # 停止临时MySQL进程
        kill $MYSQL_PID 2>/dev/null || true
        rm -f /tmp/mysql-skip-grants.cnf
        
        # 正常启动MySQL
        systemctl start $service_name
    fi
    
    if [[ "$password_set" == false ]]; then
        echo
        log_error "自动设置MySQL root密码失败，需要手动配置"
        echo
        echo "========================================================"
        echo -e "${YELLOW}手动配置MySQL密码的方法：${NC}"
        echo "========================================================"
        echo
        echo "方法1: 使用mysql_secure_installation"
        echo "  sudo mysql_secure_installation"
        echo
        echo "方法2: 查找临时密码并手动重置"
        echo "  # 查找临时密码"
        echo "  sudo grep 'temporary password' /var/log/mysqld.log"
        echo "  # 使用临时密码登录"
        echo "  mysql -u root -p"
        echo "  # 重置密码"
        echo "  ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
        echo
        echo "方法3: 使用系统认证登录"
        echo "  sudo mysql -u root"
        echo "  ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
        echo
        echo "方法4: 安全模式重置"
        echo "  sudo systemctl stop mysqld"
        echo "  sudo mysqld_safe --skip-grant-tables --skip-networking &"
        echo "  mysql -u root"
        echo "  FLUSH PRIVILEGES;"
        echo "  ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
        echo "  exit"
        echo "  sudo systemctl start mysqld"
        echo
        echo "========================================================"

        # 尝试再次查找临时密码并显示
        if [[ -f /var/log/mysqld.log ]]; then
            TEMP_PASSWORD=$(grep 'temporary password' /var/log/mysqld.log | tail -1 | awk '{print $NF}')
            if [[ -n "$TEMP_PASSWORD" ]]; then
                echo -e "${GREEN}发现的临时密码: ${YELLOW}$TEMP_PASSWORD${NC}"
                echo "========================================================"
            fi
        fi

        return 1
    fi
    
    # 运行安全配置
    log_info "应用MySQL安全配置..."
    
    # 创建安全配置脚本
    cat > /tmp/mysql_secure.sql << EOF
-- 删除匿名用户
DELETE FROM mysql.user WHERE User='';

-- 禁止root远程登录 (可选，根据需要调整)
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

-- 删除test数据库
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';

-- 刷新权限
FLUSH PRIVILEGES;
EOF
    
    if mysql -u root -p"$MYSQL_ROOT_PASSWORD" < /tmp/mysql_secure.sql 2>/dev/null; then
        log_success "MySQL安全配置完成"
    else
        log_warning "安全配置可能未完全成功，请稍后手动检查"
    fi
    
    rm -f /tmp/mysql_secure.sql
}

# 创建示例数据库和用户
create_sample_user() {
    read -p "是否创建示例数据库和用户? (y/N): " create_sample
    if [[ $create_sample =~ ^[Yy]$ ]]; then
        read -p "请输入数据库名称 (默认: testdb): " DB_NAME
        DB_NAME=${DB_NAME:-testdb}
        
        read -p "请输入用户名 (默认: testuser): " DB_USER
        DB_USER=${DB_USER:-testuser}
        
        read -s -p "请输入用户密码: " DB_PASSWORD
        echo
        
        log_info "创建数据库和用户..."
        
        cat > /tmp/create_user.sql << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
        
        mysql -u root -p"$MYSQL_ROOT_PASSWORD" < /tmp/create_user.sql
        rm -f /tmp/create_user.sql
        
        log_success "已创建数据库: $DB_NAME"
        log_success "已创建用户: $DB_USER"
    fi
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu防火墙
        ufw allow 3306/tcp
        log_info "已允许MySQL端口3306通过UFW防火墙"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS/RHEL防火墙
        firewall-cmd --permanent --add-port=3306/tcp
        firewall-cmd --reload
        log_info "已允许MySQL端口3306通过firewalld防火墙"
    else
        log_warning "未检测到防火墙管理工具，请手动配置防火墙允许3306端口"
    fi
}

# 显示安装信息
show_installation_info() {
    log_success "MySQL 8.0 安装完成！"
    echo
    echo "======================== 安装信息 ========================"
    echo "MySQL版本: $(mysql --version)"
    echo "服务状态: $(systemctl is-active mysqld)"
    echo "自启状态: $(systemctl is-enabled mysqld)"
    echo "配置文件: /etc/mysql/mysql.conf.d/mysqld.cnf (Ubuntu) 或 /etc/my.cnf (CentOS)"
    echo "数据目录: /var/lib/mysql"
    echo "日志文件: /var/log/mysql/ (Ubuntu) 或 /var/log/mysqld.log (CentOS)"
    echo "默认端口: 3306"
    echo
    echo "======================== 连接信息 ========================"
    echo "连接命令: mysql -u root -p"
    echo "root密码: [您设置的密码]"
    echo

    # 如果存在临时密码信息，显示相关说明
    if [[ -f /var/log/mysqld.log ]] && grep -q 'temporary password' /var/log/mysqld.log 2>/dev/null; then
        echo "======================== 临时密码信息 ========================"
        echo "注意: 如果密码设置失败，可以使用以下临时密码："
        TEMP_PASSWORD=$(grep 'temporary password' /var/log/mysqld.log | tail -1 | awk '{print $NF}')
        if [[ -n "$TEMP_PASSWORD" ]]; then
            echo -e "临时密码: ${YELLOW}$TEMP_PASSWORD${NC}"
            echo "使用方法: mysql -u root -p'$TEMP_PASSWORD' --connect-expired-password"
            echo "然后执行: ALTER USER 'root'@'localhost' IDENTIFIED BY '新密码';"
        fi
        echo "=========================================================="
    fi
    echo
    echo "======================== 常用命令 ========================"
    echo "启动服务: sudo systemctl start mysqld"
    echo "停止服务: sudo systemctl stop mysqld"
    echo "重启服务: sudo systemctl restart mysqld"
    echo "查看状态: sudo systemctl status mysqld"
    echo "查看日志: sudo journalctl -u mysqld"
    echo
    echo "======================== 安全建议 ========================"
    echo "1. 定期更新MySQL到最新版本"
    echo "2. 定期备份重要数据"
    echo "3. 限制root用户远程访问"
    echo "4. 使用强密码策略"
    echo "5. 定期检查用户权限"
    echo "========================================================"
}

# 显示临时密码的独立函数
show_temp_password_only() {
    log_info "检查MySQL临时密码..."
    echo

    if check_and_show_temp_password; then
        echo
        log_info "如果需要重置密码，请使用上面显示的临时密码"
        echo
        echo "重置密码的步骤："
        echo "1. 使用临时密码登录: mysql -u root -p'临时密码' --connect-expired-password"
        echo "2. 执行重置命令: ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
        echo "3. 退出MySQL: exit"
        echo "4. 使用新密码登录: mysql -u root -p"
    else
        log_warning "未找到临时密码"
        echo
        echo "可能的原因："
        echo "1. MySQL尚未安装"
        echo "2. 使用了不同的认证方式"
        echo "3. 临时密码已被使用过"
        echo
        echo "其他登录方式："
        echo "1. 尝试无密码登录: mysql -u root"
        echo "2. 使用系统认证: sudo mysql -u root"
        echo "3. 运行安全配置: sudo mysql_secure_installation"
    fi

    exit 0
}

# 主函数
main() {
    # 检查是否只需要显示临时密码
    if [[ "$1" == "--show-temp-password" ]] || [[ "$1" == "-t" ]]; then
        show_temp_password_only
    fi

    log_info "开始执行MySQL 8.0一键安装脚本..."

    # 检查权限
    check_root

    # 检测操作系统
    detect_os
    
    # 检查是否已安装
    check_mysql_installed
    
    # 设置密码
    set_mysql_password
    
    # 根据是否需要安装来执行不同步骤
    if [[ "$SKIP_INSTALLATION" != true ]]; then
        # 根据操作系统选择安装方法
        case $OS in
            *Ubuntu*|*Debian*)
                install_mysql_debian
                ;;
            *CentOS*|*Red*|*Rocky*|*AlmaLinux*)
                install_mysql_rhel
                ;;
            *)
                log_error "不支持的操作系统: $OS"
                log_info "支持的系统: Ubuntu, Debian, CentOS, RHEL, Rocky Linux, AlmaLinux"
                exit 1
                ;;
        esac
        
        # 验证安装
        if ! verify_mysql_installation; then
            log_error "MySQL安装验证失败，请检查错误信息"
            exit 1
        fi
    else
        log_info "跳过安装步骤，验证现有MySQL..."
        if ! verify_mysql_installation; then
            log_error "现有MySQL验证失败，请检查MySQL状态"
            exit 1
        fi
    fi
    
    # 启动和配置服务
    configure_mysql_service
    
    # 配置密码和安全设置
    configure_mysql_password
    
    # 创建示例用户
    create_sample_user
    
    # 配置防火墙
    read -p "是否配置防火墙允许MySQL端口? (y/N): " config_firewall
    if [[ $config_firewall =~ ^[Yy]$ ]]; then
        configure_firewall
    fi
    
    # 显示安装信息
    show_installation_info
    
    log_success "MySQL 8.0安装和配置完成！"
}

# 执行主函数
main "$@"
