import { NextRequest, NextResponse } from "next/server"

// 由于移除了本地 API 路由，中间件现在只处理页面路由的保护
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 对于静态导出，中间件的作用有限，主要在客户端处理路由保护
  // 这里只做基本的路径处理

  // 保护需要登录的页面路由
  const protectedPaths = ["/dashboard"]

  if (protectedPaths.some(path => pathname.startsWith(path))) {
    // 检查是否有 token（这里只是简单检查，实际验证在客户端进行）
    const token = request.cookies.get("token")?.value ||
                  request.headers.get("authorization")?.replace("Bearer ", "")

    if (!token) {
      // 重定向到登录页面
      return NextResponse.redirect(new URL("/login/", request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
}
