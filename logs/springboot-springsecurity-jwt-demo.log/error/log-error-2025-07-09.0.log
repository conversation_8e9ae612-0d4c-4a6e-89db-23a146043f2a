2025-07-09 10:55:29.257 [http-nio-8080-exec-4] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-09 10:55:29.280 [http-nio-8080-exec-4] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer dev-token-1751977903752, ip: 0:0:0:0:0:0:0:1, uri: /api/auth/send-code
2025-07-09 11:06:56.635 [http-nio-8080-exec-2] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-09 11:07:48.662 [http-nio-8080-exec-7] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-09 11:07:48.662 [http-nio-8080-exec-6] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-09 11:07:48.672 [http-nio-8080-exec-6] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer dev-token-1752030453964, ip: 0:0:0:0:0:0:0:1, uri: /api/api/keys
2025-07-09 11:07:48.672 [http-nio-8080-exec-7] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer dev-token-1752030453964, ip: 0:0:0:0:0:0:0:1, uri: /api/api/keys
2025-07-09 11:08:48.611 [http-nio-8080-exec-1] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-09 11:08:48.617 [http-nio-8080-exec-1] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer dev-token-1752030453964, ip: 0:0:0:0:0:0:0:1, uri: /api/keys
2025-07-09 11:11:03.403 [http-nio-8080-exec-4] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-09 11:11:03.407 [http-nio-8080-exec-4] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer dev-token-1752030453964, ip: 0:0:0:0:0:0:0:1, uri: /api/keys
