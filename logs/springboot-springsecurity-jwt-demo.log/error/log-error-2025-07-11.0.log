2025-07-11 13:11:26.251 [http-nio-8080-exec-3] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-11 13:11:26.251 [http-nio-8080-exec-4] ERROR com.icoder.security.JwtUtils - Invalid JWT token: JWT strings must contain exactly 2 period characters. Found: 0
2025-07-11 13:11:26.317 [http-nio-8080-exec-4] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer invalid-token-for-testing, ip: 0:0:0:0:0:0:0:1, uri: /api/keys
2025-07-11 13:11:26.317 [http-nio-8080-exec-3] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer invalid-token-for-testing, ip: 0:0:0:0:0:0:0:1, uri: /api/keys
2025-07-11 14:05:33.064 [http-nio-8080-exec-1] ERROR org.hibernate.engine.jdbc.spi.SqlExceptionHelper - HikariPool-1 - Connection is not available, request timed out after 30110ms.
2025-07-11 14:05:33.066 [http-nio-8080-exec-1] ERROR org.hibernate.engine.jdbc.spi.SqlExceptionHelper - No operations allowed after connection closed.
2025-07-11 14:05:33.124 [http-nio-8080-exec-1] ERROR com.icoder.security.JwtAuthenticationFilter - Cannot set user authentication: Unable to acquire JDBC Connection; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection
2025-07-11 14:05:33.143 [http-nio-8080-exec-1] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.4mtNPaXyTEXAUTSXrFiE-SIbGmVC5CAl3RD2iU8djvU, ip: 0:0:0:0:0:0:0:1, uri: /api/keys
2025-07-11 15:29:56.108 [http-nio-8080-exec-2] ERROR com.icoder.security.JwtAuthenticationEntryPoint - Unauthorized key: sk-1192bc4bdb2e4fa5993198aeac83f010, ip: 0:0:0:0:0:0:0:1, uri: /api/codeMoss/processAudio
2025-07-11 15:33:52.467 [http-nio-8080-exec-3] ERROR o.a.c.c.C.[.[localhost].[/api].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] threw exception
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"MissingParameter","message":"The request failed because it is missing `messages.role` parameter. Request id: 02175221923266684ad5aad61b27dbb859831171057baeaa41b22","param":"messages.role","type":"BadRequest"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:101)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:168)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:122)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:825)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:783)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at com.icoder.service.HuoShanService.lambda$generateSolutionStream$5(HuoShanService.java:435)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1632)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)
2025-07-11 15:33:52.468 [http-nio-8080-exec-3] ERROR o.a.c.c.C.[.[localhost].[/api].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [/api] threw exception [Request processing failed; nested exception is java.util.concurrent.CompletionException: org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"MissingParameter","message":"The request failed because it is missing `messages.role` parameter. Request id: 02175221923266684ad5aad61b27dbb859831171057baeaa41b22","param":"messages.role","type":"BadRequest"}}"] with root cause
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"MissingParameter","message":"The request failed because it is missing `messages.role` parameter. Request id: 02175221923266684ad5aad61b27dbb859831171057baeaa41b22","param":"messages.role","type":"BadRequest"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:101)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:168)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:122)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:825)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:783)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at com.icoder.service.HuoShanService.lambda$generateSolutionStream$5(HuoShanService.java:435)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1632)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)
