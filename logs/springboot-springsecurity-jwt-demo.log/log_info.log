2025-07-22 16:41:20.190 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-22 16:41:20.190 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 48549 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-22 16:41:20.196 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-22 16:41:20.638 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-22 16:41:20.664 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 5 JPA repository interfaces.
2025-07-22 16:41:20.935 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-22 16:41:20.939 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-22 16:41:20.940 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-22 16:41:20.940 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-22 16:41:21.169 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-22 16:41:21.169 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 941 ms
2025-07-22 16:41:21.326 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-22 16:41:21.383 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-22 16:41:21.433 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-22 16:41:21.462 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-22 16:41:22.224 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-22 16:41:22.237 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-22 16:41:23.849 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-22 16:41:23.857 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-22 16:41:24.110 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-22 16:41:24.110 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-22 16:41:24.308 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@17f8db6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44492c06, org.springframework.security.web.context.SecurityContextPersistenceFilter@60f9dc7e, org.springframework.security.web.header.HeaderWriterFilter@3f3c5ecd, org.springframework.web.filter.CorsFilter@5c4714ef, org.springframework.security.web.authentication.logout.LogoutFilter@536da29c, com.icoder.security.ApiKeyAuthFilter@6d815fb7, com.icoder.security.JwtAuthenticationFilter@1322b575, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@503556cb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@12aa381f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c18ee69, org.springframework.security.web.session.SessionManagementFilter@224e6e88, org.springframework.security.web.access.ExceptionTranslationFilter@1ed9d173, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@350f18a6]
2025-07-22 16:41:24.581 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-22 16:41:24.591 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-22 16:41:24.596 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 4.628 seconds (JVM running for 4.997)
2025-07-22 16:42:34.903 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 16:42:34.905 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-22 16:42:34.934 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 29 ms
2025-07-22 16:42:34.949 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-22 16:43:02.273 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-22 16:44:14.532 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-22 16:45:00.267 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-22 16:45:53.786 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-22 16:46:19.531 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-22 16:50:52.305 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f01
2025-07-22 16:50:55.016 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
