2025-07-13 17:08:17.724 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 9951 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-13 17:08:17.726 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-13 17:08:17.728 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-13 17:08:18.222 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-13 17:08:18.253 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 5 JPA repository interfaces.
2025-07-13 17:08:18.478 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-13 17:08:18.482 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-13 17:08:18.482 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-13 17:08:18.482 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-13 17:08:18.515 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-13 17:08:18.515 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 769 ms
2025-07-13 17:08:18.596 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-13 17:08:18.615 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-13 17:08:18.664 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-13 17:08:18.697 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-13 17:08:18.880 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-13 17:08:18.888 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-13 17:08:19.555 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-13 17:08:19.573 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-13 17:08:19.894 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-13 17:08:19.895 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-13 17:08:20.164 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@33f349ae, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@377e573a, org.springframework.security.web.context.SecurityContextPersistenceFilter@34e990cc, org.springframework.security.web.header.HeaderWriterFilter@365e65bb, org.springframework.web.filter.CorsFilter@76db9048, org.springframework.security.web.authentication.logout.LogoutFilter@7f2ca6f8, com.icoder.security.ApiKeyAuthFilter@8432469, com.icoder.security.JwtAuthenticationFilter@645dc557, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@21f91efa, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@35e75f7a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@349f3ff7, org.springframework.security.web.session.SessionManagementFilter@1533338c, org.springframework.security.web.access.ExceptionTranslationFilter@687b0ddc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23321be7]
2025-07-13 17:08:20.382 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-13 17:08:20.395 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-13 17:08:20.400 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.931 seconds (JVM running for 3.766)
2025-07-13 17:08:53.071 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-13 17:08:53.072 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-13 17:08:53.079 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-07-13 17:08:53.091 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-13 17:09:29.707 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-13 17:09:58.430 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-13 17:12:22.627 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-13 17:12:22.630 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-13 17:12:22.635 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
