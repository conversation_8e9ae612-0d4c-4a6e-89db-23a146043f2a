2025-07-09 10:18:28.344 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 29607 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 10:18:28.343 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 10:18:28.345 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 10:18:28.644 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 10:18:28.671 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 5 JPA repository interfaces.
2025-07-09 10:18:28.932 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 10:18:28.935 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 10:18:28.935 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 10:18:28.936 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 10:18:28.981 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 10:18:28.981 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 616 ms
2025-07-09 10:18:29.044 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 10:18:29.085 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 10:18:29.132 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 10:18:29.167 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 10:18:29.350 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 10:18:29.355 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 10:18:30.533 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 10:18:30.548 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 10:18:30.792 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 10:18:30.792 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 10:18:30.970 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@d76099a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@47f0f414, org.springframework.security.web.context.SecurityContextPersistenceFilter@1a89414e, org.springframework.security.web.header.HeaderWriterFilter@2673487b, org.springframework.web.filter.CorsFilter@7e1d8d41, org.springframework.security.web.authentication.logout.LogoutFilter@48277712, com.icoder.security.ApiKeyAuthFilter@55951fcd, com.icoder.security.JwtAuthenticationFilter@5e0f2c82, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@44492c06, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71fb8301, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@44976b08, org.springframework.security.web.session.SessionManagementFilter@68a5aadd, org.springframework.security.web.access.ExceptionTranslationFilter@685e6a68, org.springframework.security.web.access.intercept.AuthorizationFilter@69a73867]
2025-07-09 10:18:31.225 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 10:18:31.234 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 10:18:31.238 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.331 seconds (JVM running for 3.64)
2025-07-09 10:55:29.032 [http-nio-8080-exec-3] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 10:55:29.041 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 10:55:29.114 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 73 ms
2025-07-09 10:55:29.186 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer dev-token-1751977903752
2025-07-09 11:03:15.911 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 11:06:49.318 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 46698 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 11:06:49.318 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 11:06:49.321 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 11:06:49.830 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 11:06:49.865 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 5 JPA repository interfaces.
2025-07-09 11:06:50.312 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 11:06:50.319 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 11:06:50.319 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 11:06:50.319 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 11:06:50.722 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 11:06:50.723 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1360 ms
2025-07-09 11:06:50.848 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 11:06:50.920 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 11:06:51.007 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 11:06:51.057 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 11:06:51.287 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 11:06:51.297 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 11:06:52.357 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 11:06:52.379 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 11:06:52.754 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 11:06:52.754 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 11:06:53.042 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@f76872f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2d4e99de, org.springframework.security.web.context.SecurityContextPersistenceFilter@254e9709, org.springframework.security.web.header.HeaderWriterFilter@5368e981, org.springframework.web.filter.CorsFilter@580ffea, org.springframework.security.web.authentication.logout.LogoutFilter@1224e1b6, com.icoder.security.ApiKeyAuthFilter@3513d214, com.icoder.security.JwtAuthenticationFilter@98637a2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@147c00aa, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4db728df, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1851c7d2, org.springframework.security.web.session.SessionManagementFilter@ff5d4f1, org.springframework.security.web.access.ExceptionTranslationFilter@77ba583, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@350567f1]
2025-07-09 11:06:53.335 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 11:06:53.346 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 11:06:53.352 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 4.269 seconds (JVM running for 4.741)
2025-07-09 11:06:56.572 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 11:06:56.573 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 11:06:56.575 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 11:06:56.615 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer dev-token-1751977903752
2025-07-09 11:07:48.655 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer dev-token-1752030453964
2025-07-09 11:07:48.655 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer dev-token-1752030453964
2025-07-09 11:08:48.571 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer dev-token-1752030453964
2025-07-09 11:11:03.391 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer dev-token-1752030453964
2025-07-09 11:27:19.203 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 51881 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 11:27:19.203 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 11:27:19.204 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 11:27:19.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 11:27:20.022 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 5 JPA repository interfaces.
2025-07-09 11:27:20.226 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 11:27:20.229 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 11:27:20.229 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 11:27:20.230 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 11:27:20.352 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 11:27:20.352 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1131 ms
2025-07-09 11:27:20.413 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 11:27:20.470 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 11:27:20.517 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 11:27:20.556 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 11:27:20.758 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 11:27:20.773 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 11:27:21.681 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 11:27:21.692 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 11:27:22.023 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 11:27:22.024 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 11:27:22.328 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@e72fb04, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1533338c, org.springframework.security.web.context.SecurityContextPersistenceFilter@5d01b0d8, org.springframework.security.web.header.HeaderWriterFilter@a0d875d, org.springframework.web.filter.CorsFilter@4315fe1b, org.springframework.security.web.authentication.logout.LogoutFilter@194224ca, com.icoder.security.ApiKeyAuthFilter@75839695, com.icoder.security.JwtAuthenticationFilter@37a67cf, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ff21443, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@31b6b0c7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@627bcd7e, org.springframework.security.web.session.SessionManagementFilter@550fa96f, org.springframework.security.web.access.ExceptionTranslationFilter@74bbc273, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@52f43225]
2025-07-09 11:27:22.633 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 11:27:22.645 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 11:27:22.649 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.659 seconds (JVM running for 3.934)
2025-07-09 11:28:15.904 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 11:28:15.905 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 11:28:15.907 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 11:28:15.928 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-09 11:28:52.209 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-09 11:29:05.511 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-09 11:29:21.616 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-09 11:32:11.203 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-09 11:32:31.051 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-09 11:32:41.720 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 11:32:41.729 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 11:33:20.137 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 11:33:20.154 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 11:33:20.160 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 11:33:31.524 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 11:33:31.532 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 53593 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 11:33:31.532 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 11:33:32.956 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 11:33:33.025 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 60 ms. Found 5 JPA repository interfaces.
2025-07-09 11:33:33.375 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 11:33:33.378 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 11:33:33.379 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 11:33:33.379 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 11:33:33.419 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 11:33:33.419 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1838 ms
2025-07-09 11:33:33.511 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 11:33:33.531 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 11:33:33.583 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 11:33:33.622 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 11:33:33.805 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 11:33:33.811 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 11:33:34.486 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 11:33:34.501 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 11:33:34.921 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 11:33:34.921 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 11:33:35.337 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@191e654, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a6556b6, org.springframework.security.web.context.SecurityContextPersistenceFilter@2d07aacc, org.springframework.security.web.header.HeaderWriterFilter@5613247e, org.springframework.web.filter.CorsFilter@63bca84d, org.springframework.security.web.authentication.logout.LogoutFilter@5d37aa0f, com.icoder.security.ApiKeyAuthFilter@5822ecda, com.icoder.security.JwtAuthenticationFilter@45e7bb79, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@194224ca, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@350f18a6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2ffb0d10, org.springframework.security.web.session.SessionManagementFilter@3de56885, org.springframework.security.web.access.ExceptionTranslationFilter@12f8682a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7717b4a0]
2025-07-09 11:33:35.695 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 11:33:35.707 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 11:33:35.712 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 4.514 seconds (JVM running for 5.881)
2025-07-09 12:00:44.811 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 12:00:44.821 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 12:00:44.935 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 113 ms
2025-07-09 12:00:45.000 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 12:01:00.033 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 12:01:14.161 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 12:01:14.162 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 59459 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 12:01:14.162 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 12:01:14.643 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 12:01:14.667 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 5 JPA repository interfaces.
2025-07-09 12:01:14.943 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 12:01:14.947 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 12:01:14.947 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 12:01:14.947 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 12:01:15.074 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 12:01:15.074 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 898 ms
2025-07-09 12:01:15.147 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 12:01:15.198 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 12:01:15.250 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 12:01:15.283 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 12:01:15.506 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 12:01:15.512 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 12:01:16.115 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 12:01:16.118 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 12:01:16.348 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 12:01:16.348 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 12:01:16.559 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4aac81ca, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@393ae7a0, org.springframework.security.web.context.SecurityContextPersistenceFilter@9dc782d, org.springframework.security.web.header.HeaderWriterFilter@c4cceb, org.springframework.web.filter.CorsFilter@63326a3a, org.springframework.security.web.authentication.logout.LogoutFilter@2fb48970, com.icoder.security.ApiKeyAuthFilter@78318ac2, com.icoder.security.JwtAuthenticationFilter@b5312df, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@401926df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@550fa96f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21f91efa, org.springframework.security.web.session.SessionManagementFilter@28ce75ec, org.springframework.security.web.access.ExceptionTranslationFilter@e06ec83, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@761f234c]
2025-07-09 12:01:16.825 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 12:01:16.837 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 12:01:16.843 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.908 seconds (JVM running for 4.475)
2025-07-09 12:01:19.091 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 12:01:19.092 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 12:01:19.101 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-07-09 12:01:19.157 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 12:02:22.227 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 12:09:45.136 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 12:09:45.136 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 12:10:08.491 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 12:10:08.492 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 61134 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 12:10:08.492 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 12:10:09.019 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 12:10:09.044 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 5 JPA repository interfaces.
2025-07-09 12:10:09.273 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 12:10:09.276 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 12:10:09.277 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 12:10:09.277 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 12:10:09.356 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 12:10:09.356 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 850 ms
2025-07-09 12:10:09.414 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 12:10:09.456 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 12:10:09.558 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 12:10:09.600 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 12:10:09.775 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 12:10:09.782 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 12:10:10.407 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 12:10:10.418 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 12:10:10.642 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 12:10:10.642 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 12:10:10.836 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e04abc5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1008df1e, org.springframework.security.web.context.SecurityContextPersistenceFilter@3ff8a3ad, org.springframework.security.web.header.HeaderWriterFilter@370a8b6e, org.springframework.web.filter.CorsFilter@7ee12d58, org.springframework.security.web.authentication.logout.LogoutFilter@2673487b, com.icoder.security.ApiKeyAuthFilter@78318ac2, com.icoder.security.JwtAuthenticationFilter@b5312df, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54275b5d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@271e851e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f7bb8df, org.springframework.security.web.session.SessionManagementFilter@1ba35152, org.springframework.security.web.access.ExceptionTranslationFilter@399f5daf, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@9f1ca74]
2025-07-09 12:10:11.098 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 12:10:11.108 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 12:10:11.112 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.877 seconds (JVM running for 4.4)
2025-07-09 12:10:19.720 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 12:10:19.721 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 12:10:19.726 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-09 12:10:19.769 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 13:15:55.482 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 16:18:37.496 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 16:18:37.498 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 16702 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 16:18:37.498 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 16:18:38.130 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 16:18:38.156 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 5 JPA repository interfaces.
2025-07-09 16:18:38.463 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 16:18:38.467 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 16:18:38.467 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 16:18:38.467 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 16:18:38.503 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 16:18:38.503 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 987 ms
2025-07-09 16:18:38.591 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 16:18:38.667 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 16:18:38.719 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 16:18:38.751 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 16:18:38.981 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 16:18:38.986 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 16:18:39.836 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 16:18:39.845 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 16:18:40.061 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 16:18:40.061 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 16:18:40.248 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@43a59289, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6c2883b1, org.springframework.security.web.context.SecurityContextPersistenceFilter@3e4afd10, org.springframework.security.web.header.HeaderWriterFilter@1ddf42dd, org.springframework.web.filter.CorsFilter@336e3be2, org.springframework.security.web.authentication.logout.LogoutFilter@750e2d33, com.icoder.security.ApiKeyAuthFilter@3356ff58, com.icoder.security.JwtAuthenticationFilter@a9a8ec3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20faaf77, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1d8be7b9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@38fb151a, org.springframework.security.web.session.SessionManagementFilter@632c3f55, org.springframework.security.web.access.ExceptionTranslationFilter@7cdfa824, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@419f0ea]
2025-07-09 16:18:40.496 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 16:18:40.505 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 16:18:40.510 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.267 seconds (JVM running for 3.57)
2025-07-09 16:18:45.936 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 16:18:46.029 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 16:18:46.048 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 10 ms
2025-07-09 16:18:46.122 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:20:49.029 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:20:49.029 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:21:02.508 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:21:02.508 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:21:28.093 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 16:21:28.125 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 16:21:28.145 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 16:52:35.912 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 16:52:35.913 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 24318 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 16:52:35.913 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 16:52:36.677 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 16:52:36.705 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 5 JPA repository interfaces.
2025-07-09 16:52:36.937 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 16:52:36.940 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 16:52:36.941 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 16:52:36.941 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 16:52:36.983 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 16:52:36.983 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1053 ms
2025-07-09 16:52:37.040 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 16:52:37.080 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 16:52:37.124 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 16:52:37.151 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 16:52:37.334 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 16:52:37.341 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 16:52:38.301 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 16:52:38.312 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 16:52:38.599 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 16:52:38.599 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 16:52:38.837 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7db47323, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5529522f, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d8be7b9, org.springframework.security.web.header.HeaderWriterFilter@65d7eea4, org.springframework.web.filter.CorsFilter@175ac243, org.springframework.security.web.authentication.logout.LogoutFilter@252c6cdb, com.icoder.security.ApiKeyAuthFilter@b55f5b7, com.icoder.security.JwtAuthenticationFilter@40016ce1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3d53e876, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@aa0dbca, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@789dd6bf, org.springframework.security.web.session.SessionManagementFilter@20faaf77, org.springframework.security.web.access.ExceptionTranslationFilter@1ddf42dd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c544c9]
2025-07-09 16:52:39.340 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 16:52:39.358 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 16:52:39.371 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.685 seconds (JVM running for 4.156)
2025-07-09 16:52:43.981 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 16:52:43.981 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 16:52:43.988 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-09 16:52:44.018 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:52:50.854 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:52:50.854 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:54:42.537 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 16:55:29.172 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 16:55:29.196 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 16:55:29.226 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 17:06:28.643 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 17:06:28.643 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 27710 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 17:06:28.646 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 17:06:29.312 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 17:06:29.336 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 5 JPA repository interfaces.
2025-07-09 17:06:29.713 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 17:06:29.718 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 17:06:29.719 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 17:06:29.719 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 17:06:29.817 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 17:06:29.817 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1152 ms
2025-07-09 17:06:29.897 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 17:06:29.998 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 17:06:30.113 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 17:06:30.178 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 17:06:30.489 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 17:06:30.528 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 17:06:31.422 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 17:06:31.433 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 17:06:32.304 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 17:06:32.305 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 17:06:32.617 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3c544c9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@22e813fc, org.springframework.security.web.context.SecurityContextPersistenceFilter@1224e1b6, org.springframework.security.web.header.HeaderWriterFilter@71fb8301, org.springframework.web.filter.CorsFilter@433d93e7, org.springframework.security.web.authentication.logout.LogoutFilter@401ec794, com.icoder.security.ApiKeyAuthFilter@5e0f2c82, com.icoder.security.JwtAuthenticationFilter@76596288, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4ffa7041, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@56b05bd7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@175ac243, org.springframework.security.web.session.SessionManagementFilter@6076c66, org.springframework.security.web.access.ExceptionTranslationFilter@17f8db6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@38dbeb39]
2025-07-09 17:06:32.911 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 17:06:32.920 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 17:06:32.924 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 4.5 seconds (JVM running for 4.859)
2025-07-09 17:06:37.982 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 17:06:37.984 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 17:06:37.988 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-09 17:06:38.024 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:06:51.061 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:08:07.607 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:10:29.777 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:12:35.962 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:12:35.962 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:12:40.856 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:12:40.856 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:13:09.512 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:13:09.512 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:15:52.627 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 17:15:52.652 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 17:15:52.669 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 17:47:36.731 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 17:47:36.732 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 37225 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 17:47:36.734 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 17:47:38.694 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 17:47:38.747 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 5 JPA repository interfaces.
2025-07-09 17:47:39.115 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 17:47:39.119 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 17:47:39.119 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 17:47:39.119 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 17:47:39.169 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 17:47:39.169 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2310 ms
2025-07-09 17:47:39.236 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 17:47:39.277 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 17:47:39.333 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 17:47:39.368 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 17:47:39.555 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 17:47:39.561 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 17:47:40.255 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 17:47:40.268 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 17:47:40.858 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 17:47:40.860 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 17:47:41.126 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@538aa83f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@63e70bf9, org.springframework.security.web.context.SecurityContextPersistenceFilter@71fb8301, org.springframework.security.web.header.HeaderWriterFilter@350f18a6, org.springframework.web.filter.CorsFilter@402a69f, org.springframework.security.web.authentication.logout.LogoutFilter@794cb26b, com.icoder.security.ApiKeyAuthFilter@48a46b0f, com.icoder.security.JwtAuthenticationFilter@1d6713dd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18db3b3c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1c4057f9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@213012a0, org.springframework.security.web.session.SessionManagementFilter@5c4714ef, org.springframework.security.web.access.ExceptionTranslationFilter@2d07aacc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1851c7d2]
2025-07-09 17:47:41.448 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 17:47:41.470 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 17:47:41.477 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 5.516 seconds (JVM running for 6.352)
2025-07-09 17:47:42.709 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 17:47:42.710 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 17:47:42.712 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 17:47:42.766 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 17:47:46.964 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 17:47:46.971 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 17:47:46.980 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 19:29:47.753 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 50958 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 19:29:47.753 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 19:29:47.754 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 19:29:48.579 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 19:29:48.604 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 5 JPA repository interfaces.
2025-07-09 19:29:49.028 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 19:29:49.033 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 19:29:49.034 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 19:29:49.034 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 19:29:49.241 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 19:29:49.241 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1473 ms
2025-07-09 19:29:49.403 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 19:29:49.568 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 19:29:49.655 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 19:29:49.697 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 19:29:49.894 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 19:29:49.904 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 19:29:50.860 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 19:29:50.870 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 19:29:51.270 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:29:51.270 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:29:51.604 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@24fc2c80, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@965bcbc, org.springframework.security.web.context.SecurityContextPersistenceFilter@d76099a, org.springframework.security.web.header.HeaderWriterFilter@5b7ee56c, org.springframework.web.filter.CorsFilter@7ab1ad9, org.springframework.security.web.authentication.logout.LogoutFilter@6a6e9289, com.icoder.security.ApiKeyAuthFilter@45e7bb79, com.icoder.security.JwtAuthenticationFilter@2f86f9cf, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7e1d8d41, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@750e2d33, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6cc8da1c, org.springframework.security.web.session.SessionManagementFilter@5f7cd50e, org.springframework.security.web.access.ExceptionTranslationFilter@3d53e876, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@182e4365]
2025-07-09 19:29:52.277 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 19:29:52.293 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 19:29:52.298 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 4.832 seconds (JVM running for 5.153)
2025-07-09 19:29:59.521 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 19:29:59.522 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 19:29:59.529 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-07-09 19:29:59.564 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:31:17.435 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:31:17.435 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:31:20.413 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:31:20.413 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:34:56.702 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 19:34:56.703 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 52286 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 19:34:56.703 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 19:34:57.232 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 19:34:57.255 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 5 JPA repository interfaces.
2025-07-09 19:34:57.462 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 19:34:57.465 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 19:34:57.465 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 19:34:57.465 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 19:34:57.545 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 19:34:57.545 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 827 ms
2025-07-09 19:34:57.604 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 19:34:57.641 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 19:34:57.687 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 19:34:57.721 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 19:34:57.882 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 19:34:57.887 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 19:34:58.436 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 19:34:58.439 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 19:34:58.647 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:34:58.647 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:34:58.847 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@fd4459b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1073c664, org.springframework.security.web.context.SecurityContextPersistenceFilter@aa0dbca, org.springframework.security.web.header.HeaderWriterFilter@1224e1b6, org.springframework.web.filter.CorsFilter@517a46f3, org.springframework.security.web.authentication.logout.LogoutFilter@29b8df5, com.icoder.security.ApiKeyAuthFilter@6f867b0c, com.icoder.security.JwtAuthenticationFilter@7e050be1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b7ee56c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3de8f85c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7fe87c0e, org.springframework.security.web.session.SessionManagementFilter@3d53e876, org.springframework.security.web.access.ExceptionTranslationFilter@65d7eea4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@213012a0]
2025-07-09 19:34:59.094 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 19:34:59.104 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 19:34:59.108 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.643 seconds (JVM running for 5.635)
2025-07-09 19:35:05.739 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 19:35:05.740 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 19:35:05.746 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-09 19:35:05.796 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:22.387 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:22.387 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:24.760 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:24.768 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:44.050 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:45.831 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:45.831 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:54.879 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:35:54.879 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:36:10.103 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 19:36:10.117 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 19:36:10.134 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 19:52:02.570 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 19:52:02.570 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 55935 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 19:52:02.571 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 19:52:03.071 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 19:52:03.095 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 5 JPA repository interfaces.
2025-07-09 19:52:03.328 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 19:52:03.332 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 19:52:03.332 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 19:52:03.332 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 19:52:03.409 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 19:52:03.409 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 824 ms
2025-07-09 19:52:03.468 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 19:52:03.506 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 19:52:03.551 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 19:52:03.581 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 19:52:03.754 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 19:52:03.760 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 19:52:04.354 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 19:52:04.361 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 19:52:04.580 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:52:04.580 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:52:04.771 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3982206a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3e9fb485, org.springframework.security.web.context.SecurityContextPersistenceFilter@1a89414e, org.springframework.security.web.header.HeaderWriterFilter@2673487b, org.springframework.web.filter.CorsFilter@4f3fec43, org.springframework.security.web.authentication.logout.LogoutFilter@1d8be7b9, com.icoder.security.ApiKeyAuthFilter@65e4cb84, com.icoder.security.JwtAuthenticationFilter@d16209e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@44492c06, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c94bd18, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48106381, org.springframework.security.web.session.SessionManagementFilter@68a5aadd, org.springframework.security.web.access.ExceptionTranslationFilter@685e6a68, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6141647f]
2025-07-09 19:52:05.024 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 19:52:05.053 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 19:52:05.058 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.702 seconds (JVM running for 4.002)
2025-07-09 19:52:08.543 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 19:52:08.544 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 19:52:08.548 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-09 19:52:08.587 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:57:38.770 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 19:57:55.738 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 19:57:55.739 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 57139 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-09 19:57:55.739 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 19:57:56.076 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 19:57:56.099 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 5 JPA repository interfaces.
2025-07-09 19:57:56.314 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 19:57:56.317 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 19:57:56.318 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 19:57:56.318 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-09 19:57:56.355 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 19:57:56.355 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 599 ms
2025-07-09 19:57:56.413 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 19:57:56.459 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-09 19:57:56.511 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-09 19:57:56.559 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 19:57:56.751 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 19:57:56.756 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-09 19:57:57.361 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-09 19:57:57.370 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 19:57:57.591 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:57:57.592 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-09 19:57:57.788 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7fe87c0e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@8f4b803, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d3a03fe, org.springframework.security.web.header.HeaderWriterFilter@3d53e876, org.springframework.web.filter.CorsFilter@42f9873e, org.springframework.security.web.authentication.logout.LogoutFilter@51b51641, com.icoder.security.ApiKeyAuthFilter@54be6213, com.icoder.security.JwtAuthenticationFilter@506aa618, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5f7cd50e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@d76099a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@95cecc5, org.springframework.security.web.session.SessionManagementFilter@69a73867, org.springframework.security.web.access.ExceptionTranslationFilter@22aefae0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@517a46f3]
2025-07-09 19:57:58.083 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 19:57:58.094 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-09 19:57:58.098 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.554 seconds (JVM running for 3.172)
2025-07-09 19:58:00.660 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 19:58:00.660 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 19:58:00.664 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-09 19:58:00.690 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:02:45.506 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:05:09.116 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:05:09.116 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:05:14.059 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:05:14.350 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:07:04.074 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:07:04.074 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:07:06.668 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:07:06.668 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:02.089 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:02.089 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:44.755 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:50.949 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:50.949 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:53.284 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:53.286 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:55.829 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:08:55.829 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:06.903 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:06.903 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:12.563 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:12.562 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:27.787 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:27.787 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:30.048 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:30.051 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:47.612 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:47.612 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:49.489 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:10:49.489 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:11:59.780 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:11:59.780 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:12:02.356 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:12:02.360 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:12:09.302 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:12:09.302 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:12:51.852 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:12:51.852 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:16:01.983 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:16:01.983 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:16:03.923 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:16:03.924 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:16:05.403 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:16:05.403 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-09 20:17:25.997 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
