2025-07-11 13:08:15.499 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 13:08:15.500 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 24355 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-11 13:08:15.502 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:08:16.533 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 13:08:16.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 5 JPA repository interfaces.
2025-07-11 13:08:16.774 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-11 13:08:16.777 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 13:08:16.778 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 13:08:16.778 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-11 13:08:17.104 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 13:08:17.104 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1577 ms
2025-07-11 13:08:17.226 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 13:08:17.290 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-11 13:08:17.343 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-11 13:08:17.382 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 13:08:17.582 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 13:08:17.589 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-11 13:08:18.281 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-11 13:08:18.285 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 13:08:18.668 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 13:08:18.668 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 13:08:18.911 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@48106381, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c544c9, org.springframework.security.web.context.SecurityContextPersistenceFilter@5c1c9881, org.springframework.security.web.header.HeaderWriterFilter@68a5aadd, org.springframework.web.filter.CorsFilter@22e813fc, org.springframework.security.web.authentication.logout.LogoutFilter@6c70b7c3, com.icoder.security.ApiKeyAuthFilter@6f867b0c, com.icoder.security.JwtAuthenticationFilter@7e050be1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f99d8c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5d37aa0f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5529522f, org.springframework.security.web.session.SessionManagementFilter@1c4057f9, org.springframework.security.web.access.ExceptionTranslationFilter@1c90029b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4f3fec43]
2025-07-11 13:08:19.194 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 13:08:19.205 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-11 13:08:19.210 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 4.002 seconds (JVM running for 6.175)
2025-07-11 13:08:35.157 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 13:08:35.160 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-11 13:08:35.180 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 20 ms
2025-07-11 13:08:35.751 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-11 13:08:54.995 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-11 13:08:57.455 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:08:57.455 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:02.421 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:02.421 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:05.808 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:05.808 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:15.476 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:15.476 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:16.811 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:16.811 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:17.902 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:17.902 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:20.490 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:20.490 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:21.091 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:21.091 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:50.173 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:50.173 [http-nio-8080-exec-9] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:53.932 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:53.932 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:56.607 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:09:56.607 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.G6dnyrCypbQC9Ey99IhoGl2RLzapCKxFDHAGWVd28Ok
2025-07-11 13:11:26.209 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer invalid-token-for-testing
2025-07-11 13:11:26.209 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer invalid-token-for-testing
2025-07-11 13:11:44.658 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 13:11:44.866 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 13:11:44.922 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 13:55:34.059 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 13:55:34.060 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 36161 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-11 13:55:34.061 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:55:34.579 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 13:55:34.604 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 5 JPA repository interfaces.
2025-07-11 13:55:34.822 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-11 13:55:34.825 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 13:55:34.826 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 13:55:34.826 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-11 13:55:34.925 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 13:55:34.925 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 843 ms
2025-07-11 13:55:34.982 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 13:55:35.019 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-11 13:55:35.066 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-11 13:55:35.091 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 13:55:35.269 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 13:55:35.276 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-11 13:55:35.863 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-11 13:55:35.876 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 13:55:36.121 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 13:55:36.121 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 13:55:36.322 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e9fb485, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4f3fec43, org.springframework.security.web.context.SecurityContextPersistenceFilter@17f8db6, org.springframework.security.web.header.HeaderWriterFilter@2d07aacc, org.springframework.web.filter.CorsFilter@38dbeb39, org.springframework.security.web.authentication.logout.LogoutFilter@48277712, com.icoder.security.ApiKeyAuthFilter@6f867b0c, com.icoder.security.JwtAuthenticationFilter@7e050be1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c4714ef, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71fb8301, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3c544c9, org.springframework.security.web.session.SessionManagementFilter@1c2096c6, org.springframework.security.web.access.ExceptionTranslationFilter@2fb48970, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6f6c8d45]
2025-07-11 13:55:36.651 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 13:55:36.665 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-11 13:55:36.670 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.847 seconds (JVM running for 4.629)
2025-07-11 13:56:35.173 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 13:56:35.174 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-11 13:56:35.178 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-11 13:56:35.202 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-11 13:57:03.009 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: null
2025-07-11 13:57:07.122 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.4mtNPaXyTEXAUTSXrFiE-SIbGmVC5CAl3RD2iU8djvU
2025-07-11 13:57:07.122 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.4mtNPaXyTEXAUTSXrFiE-SIbGmVC5CAl3RD2iU8djvU
2025-07-11 14:05:02.868 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.4mtNPaXyTEXAUTSXrFiE-SIbGmVC5CAl3RD2iU8djvU
2025-07-11 14:05:22.861 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.4mtNPaXyTEXAUTSXrFiE-SIbGmVC5CAl3RD2iU8djvU
2025-07-11 14:05:52.003 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 14:05:52.035 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 14:05:52.040 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 15:29:08.906 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 59338 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-11 15:29:08.907 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 15:29:08.908 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 15:29:09.655 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 15:29:09.712 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 5 JPA repository interfaces.
2025-07-11 15:29:09.983 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-11 15:29:09.986 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 15:29:09.987 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 15:29:09.987 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-11 15:29:10.024 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 15:29:10.025 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1088 ms
2025-07-11 15:29:10.092 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 15:29:10.137 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-11 15:29:10.182 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-11 15:29:10.211 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:29:10.401 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:29:10.409 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-11 15:29:11.025 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-11 15:29:11.029 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:29:11.300 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:29:11.300 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:29:11.496 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1bbb42b4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3f9f8d23, org.springframework.security.web.context.SecurityContextPersistenceFilter@750e2d33, org.springframework.security.web.header.HeaderWriterFilter@dd20ebc, org.springframework.web.filter.CorsFilter@603d25db, org.springframework.security.web.authentication.logout.LogoutFilter@687b0ddc, com.icoder.security.ApiKeyAuthFilter@75527e36, com.icoder.security.JwtAuthenticationFilter@6ef1c3f7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@277bc3a5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@252c6cdb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4d2f9e3c, org.springframework.security.web.session.SessionManagementFilter@7e1d8d41, org.springframework.security.web.access.ExceptionTranslationFilter@5b7ee56c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@69d667a5]
2025-07-11 15:29:11.750 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 15:29:11.759 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-11 15:29:11.763 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.222 seconds (JVM running for 3.867)
2025-07-11 15:29:55.996 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 15:29:55.996 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-11 15:29:56.002 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-11 15:29:56.035 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-1192bc4bdb2e4fa5993198aeac83f010
2025-07-11 15:30:28.456 [http-nio-8080-exec-4] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:30:39.133 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:31:00.876 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:31:11.404 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:31:11.413 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 15:31:11.424 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 15:33:40.961 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 61057 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-11 15:33:40.963 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 15:33:40.964 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 15:33:41.558 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 15:33:41.588 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 5 JPA repository interfaces.
2025-07-11 15:33:41.834 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-11 15:33:41.837 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 15:33:41.837 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 15:33:41.837 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-11 15:33:41.902 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 15:33:41.903 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 904 ms
2025-07-11 15:33:41.973 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 15:33:42.024 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-11 15:33:42.136 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-11 15:33:42.176 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:33:42.391 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:33:42.406 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-11 15:33:43.064 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-11 15:33:43.076 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:33:43.322 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:33:43.322 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:33:43.506 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@213012a0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@43a59289, org.springframework.security.web.context.SecurityContextPersistenceFilter@6076c66, org.springframework.security.web.header.HeaderWriterFilter@5c4714ef, org.springframework.web.filter.CorsFilter@6c2883b1, org.springframework.security.web.authentication.logout.LogoutFilter@f48a080, com.icoder.security.ApiKeyAuthFilter@51af8049, com.icoder.security.JwtAuthenticationFilter@6dded900, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1224e1b6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4ffa7041, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@517a46f3, org.springframework.security.web.session.SessionManagementFilter@65d7eea4, org.springframework.security.web.access.ExceptionTranslationFilter@1c2096c6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@402a69f]
2025-07-11 15:33:43.758 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 15:33:43.767 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-11 15:33:43.771 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.239 seconds (JVM running for 3.586)
2025-07-11 15:33:51.984 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 15:33:51.989 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-11 15:33:51.995 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-11 15:33:52.015 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:34:03.749 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:34:03.752 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 15:34:03.762 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 15:34:33.795 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 61260 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-11 15:34:33.796 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 15:34:33.799 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 15:34:34.124 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 15:34:34.154 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 5 JPA repository interfaces.
2025-07-11 15:34:34.403 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-11 15:34:34.406 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 15:34:34.407 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 15:34:34.407 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-11 15:34:34.444 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 15:34:34.444 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 631 ms
2025-07-11 15:34:34.504 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 15:34:34.546 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-11 15:34:34.594 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-11 15:34:34.626 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:34:34.813 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:34:34.819 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-11 15:34:35.456 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-11 15:34:35.468 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:34:35.745 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:34:35.745 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:34:35.935 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@517a46f3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@38fb151a, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d8be7b9, org.springframework.security.web.header.HeaderWriterFilter@65d7eea4, org.springframework.web.filter.CorsFilter@152e7703, org.springframework.security.web.authentication.logout.LogoutFilter@252c6cdb, com.icoder.security.ApiKeyAuthFilter@3b39e79b, com.icoder.security.JwtAuthenticationFilter@4c6bba7d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3d53e876, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@aa0dbca, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@42f9873e, org.springframework.security.web.session.SessionManagementFilter@20faaf77, org.springframework.security.web.access.ExceptionTranslationFilter@1ddf42dd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6c2883b1]
2025-07-11 15:34:36.224 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 15:34:36.233 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-11 15:34:36.238 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 2.793 seconds (JVM running for 3.106)
2025-07-11 15:34:40.180 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 15:34:40.182 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-11 15:34:40.187 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-11 15:34:40.211 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:35:04.234 [http-nio-8080-exec-5] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:35:23.013 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:35:23.022 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 15:35:23.029 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 15:47:11.643 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 15:47:11.644 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 64349 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-11 15:47:11.644 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 15:47:12.255 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 15:47:12.278 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 5 JPA repository interfaces.
2025-07-11 15:47:12.509 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-11 15:47:12.513 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 15:47:12.514 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 15:47:12.514 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-11 15:47:12.566 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 15:47:12.566 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 908 ms
2025-07-11 15:47:12.629 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 15:47:12.671 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-11 15:47:12.718 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-11 15:47:12.749 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:47:12.961 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:47:12.968 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-11 15:47:13.598 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-11 15:47:13.612 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:47:14.003 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:47:14.003 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:47:14.299 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@fd4459b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1073c664, org.springframework.security.web.context.SecurityContextPersistenceFilter@aa0dbca, org.springframework.security.web.header.HeaderWriterFilter@1224e1b6, org.springframework.web.filter.CorsFilter@517a46f3, org.springframework.security.web.authentication.logout.LogoutFilter@29b8df5, com.icoder.security.ApiKeyAuthFilter@7b44bfb8, com.icoder.security.JwtAuthenticationFilter@75527e36, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b7ee56c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3de8f85c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7fe87c0e, org.springframework.security.web.session.SessionManagementFilter@3d53e876, org.springframework.security.web.access.ExceptionTranslationFilter@65d7eea4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@213012a0]
2025-07-11 15:47:14.609 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 15:47:14.619 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-11 15:47:14.623 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.195 seconds (JVM running for 3.814)
2025-07-11 15:47:24.674 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 15:47:24.675 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-11 15:47:24.679 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-11 15:47:24.705 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:48:44.543 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:48:44.563 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 15:48:44.579 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 15:51:55.084 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 15:51:55.085 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 65625 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-11 15:51:55.086 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 15:51:55.527 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 15:51:55.554 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 5 JPA repository interfaces.
2025-07-11 15:51:55.819 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-11 15:51:55.822 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 15:51:55.823 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 15:51:55.823 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-11 15:51:55.867 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 15:51:55.867 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 765 ms
2025-07-11 15:51:55.942 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 15:51:55.991 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-11 15:51:56.052 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-11 15:51:56.091 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:51:56.350 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:51:56.357 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-11 15:51:57.082 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-11 15:51:57.096 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:51:57.401 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:51:57.401 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-11 15:51:57.669 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@48106381, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c544c9, org.springframework.security.web.context.SecurityContextPersistenceFilter@5c1c9881, org.springframework.security.web.header.HeaderWriterFilter@68a5aadd, org.springframework.web.filter.CorsFilter@22e813fc, org.springframework.security.web.authentication.logout.LogoutFilter@6c70b7c3, com.icoder.security.ApiKeyAuthFilter@54be6213, com.icoder.security.JwtAuthenticationFilter@506aa618, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f99d8c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5d37aa0f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5529522f, org.springframework.security.web.session.SessionManagementFilter@1c4057f9, org.springframework.security.web.access.ExceptionTranslationFilter@1c90029b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4f3fec43]
2025-07-11 15:51:58.247 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 15:51:58.263 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-11 15:51:58.270 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.4 seconds (JVM running for 3.744)
2025-07-11 15:52:05.837 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 15:52:05.837 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-11 15:52:05.840 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-11 15:52:05.862 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: sk-973184913d5e4a5886c31fd29f3910e5
2025-07-11 15:54:21.345 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 15:54:21.356 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 15:54:21.365 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
