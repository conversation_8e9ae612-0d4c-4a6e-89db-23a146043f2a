2025-07-10 15:21:56.170 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-10 15:21:56.171 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 9925 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-10 15:21:56.172 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-10 15:21:56.523 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10 15:21:56.546 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 5 JPA repository interfaces.
2025-07-10 15:21:56.782 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-10 15:21:56.785 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-10 15:21:56.785 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 15:21:56.785 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-10 15:21:56.841 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-10 15:21:56.841 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 651 ms
2025-07-10 15:21:56.912 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10 15:21:56.970 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-10 15:21:57.034 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-10 15:21:57.068 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-10 15:21:57.271 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-10 15:21:57.279 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-10 15:21:58.000 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-10 15:21:58.011 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10 15:21:58.280 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-10 15:21:58.280 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-10 15:21:58.713 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@59371066, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@48106381, org.springframework.security.web.context.SecurityContextPersistenceFilter@1ddf42dd, org.springframework.security.web.header.HeaderWriterFilter@74bdfa0b, org.springframework.web.filter.CorsFilter@3c544c9, org.springframework.security.web.authentication.logout.LogoutFilter@dd20ebc, com.icoder.security.ApiKeyAuthFilter@21c75084, com.icoder.security.JwtAuthenticationFilter@7e2bd5e6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1c18ee69, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65d7eea4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7db47323, org.springframework.security.web.session.SessionManagementFilter@318e3942, org.springframework.security.web.access.ExceptionTranslationFilter@147c00aa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3e9fb485]
2025-07-10 15:21:59.088 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-10 15:21:59.102 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-10 15:21:59.116 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.161 seconds (JVM running for 3.617)
2025-07-10 15:23:12.825 [http-nio-8080-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 15:23:12.828 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-10 15:23:12.867 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 39 ms
2025-07-10 15:23:12.926 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:19.287 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:19.287 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:35.759 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:35.759 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:38.319 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:38.325 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:46.208 [http-nio-8080-exec-3] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:46.208 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:23:50.741 [http-nio-8080-exec-6] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:24:01.357 [http-nio-8080-exec-10] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:27:38.232 [http-nio-8080-exec-1] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:28:14.441 [http-nio-8080-exec-7] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 15:29:59.751 [http-nio-8080-exec-8] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
2025-07-10 18:27:21.642 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-10 18:27:21.646 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 58442 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-10 18:27:21.646 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-10 18:27:23.742 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10 18:27:23.774 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 5 JPA repository interfaces.
2025-07-10 18:27:24.017 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-10 18:27:24.021 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-10 18:27:24.021 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 18:27:24.021 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-10 18:27:24.225 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-10 18:27:24.226 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2549 ms
2025-07-10 18:27:24.289 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10 18:27:24.334 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-10 18:27:24.383 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-10 18:27:24.414 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-10 18:27:24.609 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-10 18:27:24.617 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-10 18:27:25.231 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-10 18:27:25.236 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10 18:27:25.579 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-10 18:27:25.579 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-10 18:27:25.839 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@fd4459b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1073c664, org.springframework.security.web.context.SecurityContextPersistenceFilter@aa0dbca, org.springframework.security.web.header.HeaderWriterFilter@1224e1b6, org.springframework.web.filter.CorsFilter@517a46f3, org.springframework.security.web.authentication.logout.LogoutFilter@29b8df5, com.icoder.security.ApiKeyAuthFilter@4afd65fd, com.icoder.security.JwtAuthenticationFilter@51af8049, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b7ee56c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3de8f85c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7fe87c0e, org.springframework.security.web.session.SessionManagementFilter@3d53e876, org.springframework.security.web.access.ExceptionTranslationFilter@65d7eea4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@213012a0]
2025-07-10 18:27:26.198 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-10 18:27:26.209 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10 18:27:26.210 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-10 18:27:26.213 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-10 18:27:26.214 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-10 18:27:26.214 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-10 18:27:26.217 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-10 18:27:26.217 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-07-10 18:27:26.222 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-10 18:27:41.793 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-10 18:27:41.794 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Starting OllamaDeepseekChatBotApplication using Java 1.8.0_432 on LPT020294 with PID 58521 (/Users/<USER>/code/mycode/coder-moss-api-master/target/classes started by zhaoq in /Users/<USER>/code/mycode/coder-moss-api-master)
2025-07-10 18:27:41.795 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-10 18:27:42.086 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10 18:27:42.110 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 5 JPA repository interfaces.
2025-07-10 18:27:42.327 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-10 18:27:42.330 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-10 18:27:42.330 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 18:27:42.330 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-10 18:27:42.361 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-10 18:27:42.361 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 550 ms
2025-07-10 18:27:42.421 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10 18:27:42.650 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-10 18:27:42.749 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-10 18:27:42.811 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-10 18:27:43.091 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-10 18:27:43.104 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-10 18:27:43.845 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-10 18:27:43.856 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10 18:27:44.171 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.OncePerRequestFilter.doFilter(javax.servlet.ServletRequest,javax.servlet.ServletResponse,javax.servlet.FilterChain) throws javax.servlet.ServletException,java.io.IOException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-10 18:27:44.172 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.filter.GenericFilterBean.init(javax.servlet.FilterConfig) throws javax.servlet.ServletException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-10 18:27:44.435 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@538aa83f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@63e70bf9, org.springframework.security.web.context.SecurityContextPersistenceFilter@71fb8301, org.springframework.security.web.header.HeaderWriterFilter@350f18a6, org.springframework.web.filter.CorsFilter@402a69f, org.springframework.security.web.authentication.logout.LogoutFilter@794cb26b, com.icoder.security.ApiKeyAuthFilter@141aba65, com.icoder.security.JwtAuthenticationFilter@376af784, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18db3b3c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1c4057f9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@213012a0, org.springframework.security.web.session.SessionManagementFilter@5c4714ef, org.springframework.security.web.access.ExceptionTranslationFilter@2d07aacc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1851c7d2]
2025-07-10 18:27:44.771 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-10 18:27:44.783 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-10 18:27:44.789 [main] INFO  com.icoder.OllamaDeepseekChatBotApplication - Started OllamaDeepseekChatBotApplication in 3.245 seconds (JVM running for 3.83)
2025-07-10 18:28:06.189 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 18:28:06.190 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-10 18:28:06.193 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-10 18:28:06.223 [http-nio-8080-exec-2] INFO  com.icoder.security.ApiKeyAuthFilter - start auth key: Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.xttOKFaRW3GvmsMObDsYbsWiBBHRkhrfOsu3_ks4744
