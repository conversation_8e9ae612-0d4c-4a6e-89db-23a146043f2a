#!/bin/bash

# 应用程序名称
APP_NAME="coder-moss-api"

# 应用程序JAR文件路径
APP_JAR="./coder-moss-1.0-SNAPSHOT.jar"

# 配置文件路径
CONFIG_FILE="./application.properties"

# 日志文件路径
LOG_FILE="./${APP_NAME}.log"

# 启动命令
CMD="java -jar ${APP_JAR} --spring.config.location=file:${CONFIG_FILE}"

# 启动应用程序并输出日志
echo "Starting ${APP_NAME}..."
nohup ${CMD} > ${LOG_FILE} 2>&1 &

# 输出进程ID
echo "$!" > /var/run/${APP_NAME}.pid

echo "${APP_NAME} started with PID $(cat /var/run/${APP_NAME}.pid)"