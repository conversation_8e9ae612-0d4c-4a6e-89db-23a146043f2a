/*
SQLyog Ultimate v12.09 (64 bit)
MySQL - 8.0.20 : Database - apikey_management
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`apikey_management` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `apikey_management`;

/*Table structure for table `api_keys` */

DROP TABLE IF EXISTS `api_keys`;

CREATE TABLE `api_keys` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `active` bit(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `expires_at` datetime(6) DEFAULT NULL,
  `key_value` varchar(255) NOT NULL,
  `remaining_calls` int NOT NULL,
  `total_calls` int NOT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKpp6b99o4acta2wol0qnnod7bb` (`key_value`),
  KEY `idx_api_keys_user_id` (`user_id`),
  CONSTRAINT `FK89d4ddye91twgmx31epv7ro7h` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `api_keys` */

insert  into `api_keys`(`id`,`active`,`created_at`,`expires_at`,`key_value`,`remaining_calls`,`total_calls`,`user_id`) values (1,'','2025-03-15 13:02:36.000000','2026-03-15 13:02:36.000000','admin-api-key-00000000-0000-0000-0000-000000000000',1003,1003,1),(2,'\0','2025-03-15 14:13:45.067013','2026-03-15 14:13:45.067013','sk-1192bc4bdb2e4fa5993198aeac83f010',100,102,1);

/*Table structure for table `payment_records` */

DROP TABLE IF EXISTS `payment_records`;

CREATE TABLE `payment_records` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `amount` decimal(38,2) NOT NULL,
  `calls_added` int NOT NULL,
  `payment_method` varchar(255) NOT NULL,
  `payment_time` datetime(6) NOT NULL,
  `status` varchar(255) NOT NULL,
  `transaction_id` varchar(255) NOT NULL,
  `api_key_id` bigint DEFAULT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_payment_records_user_id` (`user_id`),
  KEY `idx_payment_records_api_key_id` (`api_key_id`),
  CONSTRAINT `FKf2n425j7cb5hb8dxccv856ygt` FOREIGN KEY (`api_key_id`) REFERENCES `api_keys` (`id`),
  CONSTRAINT `FKr8wg2scgi57nvos1u55330aty` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `payment_records` */

/*Table structure for table `usage_records` */

DROP TABLE IF EXISTS `usage_records`;

CREATE TABLE `usage_records` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(255) NOT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `notes` varchar(255) DEFAULT NULL,
  `request_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `response_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `used_at` datetime(6) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `api_key_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_usage_records_api_key_id` (`api_key_id`),
  CONSTRAINT `FKehym1me2u8e8pgsbweknin405` FOREIGN KEY (`api_key_id`) REFERENCES `api_keys` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `usage_records` */

insert  into `usage_records`(`id`,`endpoint`,`ip_address`,`notes`,`request_data`,`response_data`,`used_at`,`user_agent`,`api_key_id`) values (1,'deepseek-chat','0:0:0:0:0:0:0:1','111','两数之和','{\"id\":\"a31d5d34-7b32-4b64-9864-6c81687d8c2b\",\"object\":\"chat.completion\",\"created\":1742131107,\"model\":\"deepseek-chat\",\"choices\":[{\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"{\\n  \\\"thoughts\\\": [\\n    \\\"使用哈希表来存储数组中的元素及其索引，以便快速查找目标值与当前值的差是否存在于数组中。\\\",\\n    \\\"遍历数组，对于每个元素，计算目标值与该元素的差值，检查这个差值是否已经存在于哈希表中。\\\",\\n    \\\"如果存在，则返回当前元素的索引和差值的索引。\\\",\\n    \\\"如果不存在，则将当前元素及其索引添加到哈希表中，继续遍历。\\\"\\n  ],\\n  \\\"code\\\": \\\"public int[] twoSum(int[] nums, int target) {\\\\n    // 创建一个哈希表来存储数组元素及其索引\\\\n    Map<Integer, Integer> map = new HashMap<>();\\\\n    // 遍历数组\\\\n    for (int i = 0; i < nums.length; i++) {\\\\n        // 计算目标值与当前元素的差值\\\\n        int complement = target - nums[i];\\\\n        // 检查差值是否已经存在于哈希表中\\\\n        if (map.containsKey(complement)) {\\\\n            // 如果存在，返回当前元素的索引和差值的索引\\\\n            return new int[] { map.get(complement), i };\\\\n        }\\\\n        // 将当前元素及其索引添加到哈希表中\\\\n        map.put(nums[i], i);\\\\n    }\\\\n    // 如果没有找到符合条件的两个数，抛出异常\\\\n    throw new IllegalArgumentException(\\\\\\\"No two sum solution\\\\\\\");\\\\n}\\\",\\n  \\\"time_complexity\\\": \\\"O(n) 因为只需要遍历数组一次，每次查找哈希表的操作时间复杂度为O(1)\\\",\\n  \\\"space_complexity\\\": \\\"O(n) 因为最坏情况下，哈希表需要存储数组中的所有元素\\\"\\n}\"},\"logprobs\":null,\"finish_reason\":\"stop\"}],\"usage\":{\"prompt_tokens\":155,\"completion_tokens\":354,\"total_tokens\":509,\"prompt_tokens_details\":{\"cached_tokens\":128},\"prompt_cache_hit_tokens\":128,\"prompt_cache_miss_tokens\":27},\"system_fingerprint\":\"fp_3a5770e1b4_prod0225\"}','2025-03-16 13:18:53.655000','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',2);

/*Table structure for table `user_roles` */

DROP TABLE IF EXISTS `user_roles`;

CREATE TABLE `user_roles` (
  `user_id` bigint NOT NULL,
  `role` varchar(255) DEFAULT NULL,
  KEY `FKhfh9dx7w3ubf1co1vdev94g3f` (`user_id`),
  CONSTRAINT `FKhfh9dx7w3ubf1co1vdev94g3f` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `user_roles` */

insert  into `user_roles`(`user_id`,`role`) values (1,'ADMIN'),(1,'USER'),(4,'USER');

/*Table structure for table `users` */

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NOT NULL,
  `email` varchar(255) NOT NULL,
  `enabled` bit(1) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `invite_code` varchar(255) DEFAULT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `referred_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK6dotkott2kjsp8vw4d0m25fb7` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `users` */

insert  into `users`(`id`,`created_at`,`email`,`enabled`,`full_name`,`invite_code`,`last_login`,`password`,`referred_by`) values (1,'2025-03-15 13:02:33.000000','<EMAIL>','','System Administrator','ADMIN123','2025-03-16 04:55:11.793000','$2a$10$mQeSccLR2ZqT0w0.vGan0u9ytK5fXMyTXJiBjbIpo.rYo51RQTK5a',NULL),(4,'2025-03-16 05:10:33.146000','<EMAIL>','\0','test','42172898',NULL,'$2a$10$mQeSccLR2ZqT0w0.vGan0u9ytK5fXMyTXJiBjbIpo.rYo51RQTK5a',NULL);

/*Table structure for table `verification_tokens` */

DROP TABLE IF EXISTS `verification_tokens`;

CREATE TABLE `verification_tokens` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `expiry_date` datetime(6) NOT NULL,
  `token` varchar(255) NOT NULL,
  `token_type` varchar(255) NOT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK6q9nsb665s9f8qajm3j07kd1e` (`token`),
  UNIQUE KEY `UKdqp95ggn6gvm865km5muba2o5` (`user_id`),
  KEY `idx_verification_tokens_user_id` (`user_id`),
  CONSTRAINT `FK54y8mqsnq1rtyf581sfmrbp4f` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Data for the table `verification_tokens` */

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
