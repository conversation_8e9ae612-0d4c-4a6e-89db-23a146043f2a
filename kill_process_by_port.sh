#!/bin/bash

# CentOS 7.6 根据端口号关闭进程脚本
# 作者: AI Assistant
# 日期: 2025-07-18
# 兼容: CentOS 7.6

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项] <端口号>"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -f, --force    强制杀死进程 (使用 kill -9)"
    echo "  -l, --list     仅列出使用指定端口的进程，不杀死"
    echo "  -a, --all      杀死所有使用该端口的进程"
    echo "  -t, --timeout  设置等待时间(秒)，默认5秒"
    echo
    echo "示例:"
    echo "  $0 3306                    # 杀死使用3306端口的进程"
    echo "  $0 -f 8080                 # 强制杀死使用8080端口的进程"
    echo "  $0 -l 80                   # 仅列出使用80端口的进程"
    echo "  $0 -a 9000                 # 杀死所有使用9000端口的进程"
    echo "  $0 -t 10 3306              # 设置10秒等待时间后杀死3306端口进程"
    echo
}

# 检查是否为有效端口号
validate_port() {
    local port=$1
    if [[ ! $port =~ ^[0-9]+$ ]]; then
        log_error "无效的端口号: $port"
        log_info "端口号必须是1-65535之间的数字"
        exit 1
    fi
    
    if [[ $port -lt 1 || $port -gt 65535 ]]; then
        log_error "端口号超出范围: $port"
        log_info "端口号必须在1-65535之间"
        exit 1
    fi
}

# 检查必要的工具是否存在
check_tools() {
    local missing_tools=()
    
    # 检查 netstat (CentOS 7默认可能没有)
    if ! command -v netstat >/dev/null 2>&1; then
        missing_tools+=("net-tools")
    fi
    
    # 检查 ss (应该默认存在)
    if ! command -v ss >/dev/null 2>&1; then
        missing_tools+=("iproute")
    fi
    
    # 检查 lsof (可能需要安装)
    if ! command -v lsof >/dev/null 2>&1; then
        missing_tools+=("lsof")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_warning "缺少必要工具，正在尝试安装: ${missing_tools[*]}"
        
        # 检查是否为root用户
        if [[ $EUID -eq 0 ]]; then
            yum install -y "${missing_tools[@]}" 2>/dev/null || {
                log_error "无法安装必要工具: ${missing_tools[*]}"
                log_info "请手动安装: yum install -y ${missing_tools[*]}"
                exit 1
            }
            log_success "工具安装完成"
        else
            log_error "需要root权限安装工具: ${missing_tools[*]}"
            log_info "请以root身份运行: sudo yum install -y ${missing_tools[*]}"
            exit 1
        fi
    fi
}

# 获取使用指定端口的进程信息
get_processes_by_port() {
    local port=$1
    local processes=()
    
    log_info "查找使用端口 $port 的进程..."
    
    # 方法1: 使用ss命令 (CentOS 7推荐)
    if command -v ss >/dev/null 2>&1; then
        while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                processes+=("$line")
            fi
        done < <(ss -tlnp 2>/dev/null | grep ":$port " | awk '{print $NF}' | grep -o 'pid=[0-9]*' | cut -d= -f2 | sort -u)
    fi
    
    # 方法2: 使用netstat命令 (备用)
    if [[ ${#processes[@]} -eq 0 ]] && command -v netstat >/dev/null 2>&1; then
        while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                processes+=("$line")
            fi
        done < <(netstat -tlnp 2>/dev/null | grep ":$port " | awk '{print $NF}' | cut -d/ -f1 | grep -E '^[0-9]+$' | sort -u)
    fi
    
    # 方法3: 使用lsof命令 (备用)
    if [[ ${#processes[@]} -eq 0 ]] && command -v lsof >/dev/null 2>&1; then
        while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                processes+=("$line")
            fi
        done < <(lsof -ti :$port 2>/dev/null | sort -u)
    fi
    
    # 去重并过滤有效的PID
    local unique_pids=()
    for pid in "${processes[@]}"; do
        if [[ $pid =~ ^[0-9]+$ ]] && [[ ! " ${unique_pids[*]} " =~ " $pid " ]]; then
            # 验证PID是否存在
            if kill -0 "$pid" 2>/dev/null; then
                unique_pids+=("$pid")
            fi
        fi
    done
    
    echo "${unique_pids[@]}"
}

# 显示进程详细信息
show_process_info() {
    local pids=($1)
    
    if [[ ${#pids[@]} -eq 0 ]]; then
        log_warning "未找到使用端口 $PORT 的进程"
        return 1
    fi
    
    echo
    log_info "找到 ${#pids[@]} 个使用端口 $PORT 的进程:"
    echo
    
    # 表格头
    printf "%-8s %-20s %-10s %-8s %-s\n" "PID" "进程名" "用户" "CPU%" "命令行"
    printf "%-8s %-20s %-10s %-8s %-s\n" "--------" "--------------------" "----------" "--------" "----------------------------------------"
    
    for pid in "${pids[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            # 获取进程信息
            local proc_info=$(ps -p "$pid" -o pid,comm,user,pcpu,args --no-headers 2>/dev/null)
            if [[ -n "$proc_info" ]]; then
                echo "$proc_info" | while read -r proc_pid proc_name proc_user proc_cpu proc_args; do
                    # 截断过长的命令行
                    local short_args="${proc_args:0:40}"
                    if [[ ${#proc_args} -gt 40 ]]; then
                        short_args="${short_args}..."
                    fi
                    printf "%-8s %-20s %-10s %-8s %-s\n" "$proc_pid" "$proc_name" "$proc_user" "$proc_cpu" "$short_args"
                done
            fi
        fi
    done
    echo
    
    return 0
}

# 杀死进程
kill_processes() {
    local pids=($1)
    local force=$2
    local timeout_val=$3
    
    if [[ ${#pids[@]} -eq 0 ]]; then
        log_warning "没有需要杀死的进程"
        return 0
    fi
    
    local killed_count=0
    local failed_pids=()
    
    for pid in "${pids[@]}"; do
        if ! kill -0 "$pid" 2>/dev/null; then
            log_warning "进程 $pid 已不存在，跳过"
            continue
        fi
        
        # 获取进程名称
        local proc_name=$(ps -p "$pid" -o comm --no-headers 2>/dev/null || echo "unknown")
        
        log_info "正在终止进程: $pid ($proc_name)"
        
        if [[ "$force" == true ]]; then
            # 强制杀死
            if kill -9 "$pid" 2>/dev/null; then
                log_success "强制杀死进程 $pid ($proc_name)"
                ((killed_count++))
            else
                log_error "无法杀死进程 $pid ($proc_name)"
                failed_pids+=("$pid")
            fi
        else
            # 优雅终止
            if kill -TERM "$pid" 2>/dev/null; then
                log_info "发送终止信号给进程 $pid ($proc_name)，等待 $timeout_val 秒..."
                
                # 等待进程终止
                local count=0
                while [[ $count -lt $timeout_val ]]; do
                    if ! kill -0 "$pid" 2>/dev/null; then
                        log_success "进程 $pid ($proc_name) 已正常终止"
                        ((killed_count++))
                        break
                    fi
                    sleep 1
                    ((count++))
                done
                
                # 如果超时仍未终止，强制杀死
                if kill -0 "$pid" 2>/dev/null; then
                    log_warning "进程 $pid ($proc_name) 超时未响应，强制杀死"
                    if kill -9 "$pid" 2>/dev/null; then
                        log_success "强制杀死进程 $pid ($proc_name)"
                        ((killed_count++))
                    else
                        log_error "无法强制杀死进程 $pid ($proc_name)"
                        failed_pids+=("$pid")
                    fi
                fi
            else
                log_error "无法发送终止信号给进程 $pid ($proc_name)"
                failed_pids+=("$pid")
            fi
        fi
    done
    
    echo
    log_success "成功终止 $killed_count 个进程"
    
    if [[ ${#failed_pids[@]} -gt 0 ]]; then
        log_error "以下进程终止失败: ${failed_pids[*]}"
        return 1
    fi
    
    return 0
}

# 确认操作
confirm_action() {
    local pids=($1)
    local port=$2
    
    if [[ "$FORCE_ACTION" == true ]]; then
        return 0
    fi
    
    echo
    log_warning "即将杀死以下 ${#pids[@]} 个使用端口 $port 的进程:"
    for pid in "${pids[@]}"; do
        local proc_info=$(ps -p "$pid" -o pid,comm,user,args --no-headers 2>/dev/null || echo "$pid unknown unknown unknown")
        echo "  - $proc_info"
    done
    echo
    
    read -p "确认要继续吗? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 主函数
main() {
    local PORT=""
    local FORCE_KILL=false
    local LIST_ONLY=false
    local KILL_ALL=false
    local TIMEOUT=5
    local FORCE_ACTION=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_KILL=true
                FORCE_ACTION=true
                shift
                ;;
            -l|--list)
                LIST_ONLY=true
                shift
                ;;
            -a|--all)
                KILL_ALL=true
                FORCE_ACTION=true
                shift
                ;;
            -t|--timeout)
                if [[ -n $2 && $2 =~ ^[0-9]+$ ]]; then
                    TIMEOUT=$2
                    shift 2
                else
                    log_error "无效的超时时间: $2"
                    exit 1
                fi
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$PORT" ]]; then
                    PORT=$1
                else
                    log_error "只能指定一个端口号"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查是否提供了端口号
    if [[ -z "$PORT" ]]; then
        log_error "请指定端口号"
        show_help
        exit 1
    fi
    
    # 验证端口号
    validate_port "$PORT"
    
    # 检查必要工具
    check_tools
    
    log_info "开始处理端口 $PORT..."
    
    # 获取使用该端口的进程
    local pids_array=($(get_processes_by_port "$PORT"))
    
    # 显示进程信息
    if ! show_process_info "${pids_array[*]}"; then
        exit 0
    fi
    
    # 如果只是列出，则退出
    if [[ "$LIST_ONLY" == true ]]; then
        log_info "仅显示进程信息，未执行杀死操作"
        exit 0
    fi
    
    # 确认操作 (除非强制模式)
    if [[ "$KILL_ALL" == true ]]; then
        log_info "使用 --all 选项，将杀死所有相关进程"
    else
        confirm_action "${pids_array[*]}" "$PORT"
    fi
    
    # 杀死进程
    kill_processes "${pids_array[*]}" "$FORCE_KILL" "$TIMEOUT"
    
    # 验证端口是否已释放
    sleep 1
    local remaining_pids=($(get_processes_by_port "$PORT"))
    if [[ ${#remaining_pids[@]} -eq 0 ]]; then
        log_success "端口 $PORT 已成功释放"
    else
        log_warning "端口 $PORT 仍有 ${#remaining_pids[@]} 个进程在使用"
        log_info "可以使用 -f 选项强制杀死进程"
    fi
}

# 信号处理
trap 'log_info "脚本被中断"; exit 130' INT TERM

# 执行主函数
main "$@"
