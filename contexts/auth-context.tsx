"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { AuthService, ApiClient } from "@/lib/api"
import { toast } from "sonner"

interface User {
  id: string | number
  email: string
  name: string
  roles?: string[]
  enabled?: boolean
  createdAt?: string
  lastLogin?: string
  remainingCalls?: number
  totalCalls?: number
}

interface AuthContextType {
  user: User | null
  token: string | null
  login: (token: string, user: User) => void
  logout: () => Promise<void>
  isLoading: boolean
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // 设置401错误处理
  useEffect(() => {
    const handleUnauthorized = async () => {
      // 显示登录失效提示
      toast.error("登录已失效，请重新登录", {
        duration: 3000,
        position: "top-center",
      })

      // 清除认证状态
      setToken(null)
      setUser(null)
      localStorage.removeItem("token")
      localStorage.removeItem("user")

      // 延迟跳转，让用户看到提示信息
      setTimeout(() => {
        router.push("/login")
      }, 1500)
    }

    // 注册401错误处理函数到ApiClient
    ApiClient.setUnauthorizedHandler(handleUnauthorized)

    // 清理函数
    return () => {
      ApiClient.setUnauthorizedHandler(() => {})
    }
  }, [router])

  useEffect(() => {
    // 从localStorage恢复登录状态
    const storedToken = localStorage.getItem("token")
    const storedUser = localStorage.getItem("user")

    if (storedToken && storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser)
        setToken(storedToken)
        setUser(parsedUser)
      } catch (error) {
        console.error("解析用户数据失败:", error)
        // 清除无效数据
        localStorage.removeItem("token")
        localStorage.removeItem("user")
      }
    }

    setIsLoading(false)
  }, [])

  const login = (newToken: string, newUser: User) => {
    setToken(newToken)
    setUser(newUser)
    localStorage.setItem("token", newToken)
    localStorage.setItem("user", JSON.stringify(newUser))
  }

  const logout = async () => {
    try {
      // 调用后端退出登录API
      await AuthService.logout()
    } catch (error) {
      console.error("退出登录API调用失败:", error)
      // 即使API调用失败，也要清除本地状态
    } finally {
      // 清除本地状态
      setToken(null)
      setUser(null)
      localStorage.removeItem("token")
      localStorage.removeItem("user")
      router.push("/login")
    }
  }

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    isLoading,
    isAuthenticated: !!user && !!token
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

// 路由保护组件
interface ProtectedRouteProps {
  children: ReactNode
  fallback?: ReactNode
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login")
    }
  }, [isAuthenticated, isLoading, router])

  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      )
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}
