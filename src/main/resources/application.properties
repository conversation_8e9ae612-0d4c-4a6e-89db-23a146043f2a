server.port=8080
spring.application.name=ollama-deepseek-chatBot

server.servlet.context-path=/api

spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=5MB


# Database Configuration
spring.datasource.url=*****************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Wgh@zq520
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql=false
spring.jpa.open-in-view=true

# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=lhlctrxnznknppor
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# JWT Configuration
app.jwt.secret=jxgEQeXHuPq8VdbyYFNkANdudQ53YUn4BJ8CHfTQVMye7oo14IlISxTQeSgIjXmMB3bxTLI
app.jwt.expiration=86400000

# Payment Configuration
payment.wechat.appId=your-wechat-appid
payment.wechat.mchId=your-wechat-merchant-id
payment.wechat.mchKey=your-wechat-merchant-key
payment.wechat.notifyUrl=http://your-domain.com/api/payment/wechat/notify

payment.alipay.appId=your-alipay-appid
payment.alipay.privateKey=your-alipay-private-key
payment.alipay.publicKey=your-alipay-public-key
payment.alipay.notifyUrl=http://your-domain.com/api/payment/alipay/notify
payment.alipay.returnUrl=http://your-domain.com/payment/success

# API Key Configuration
apikey.default.expiration.days=365
apikey.default.calls=100

# Application Configuration
app.admin.email=<EMAIL>
app.base.url=http://localhost:3000


huoshan.api.url=https://ark.cn-beijing.volces.com/api/v3/chat/completions
huoshan.api.key=8bd769ab-7b39-4f7b-9aab-d96813ffe956

deepseek.api.url=https://api.deepseek.com/chat/completions
deepseek.api.key=***********************************

gemini.api.url=https://gemini.offer-helper.top/v1/chat/completions
gemini.api.key=AIzaSyB1OqZQJKCQinZ1yO6HXC560A85NWK7mu0


openRouter.api.url=https://openrouter.ai/api/v1/chat/completions
openRouter.api.key=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

aliyun.oss.endpoint=oss-cn-shanghai.aliyuncs.com
aliyun.oss.accessKeyId=LTAI5tFyE6C3CMzaTywA5KH2
aliyun.oss.accessKeySecret=******************************
aliyun.oss.bucketName=edu-project-0414
