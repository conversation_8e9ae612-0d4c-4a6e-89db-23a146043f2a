//package com.icoder.service;
//
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.google.gson.Gson;
//import org.springframework.stereotype.Service;
//import org.springframework.util.StringUtils;
//
//import javax.crypto.Mac;
//import javax.crypto.spec.SecretKeySpec;
//import java.io.*;
//import java.net.HttpURLConnection;
//import java.net.URL;
//import java.net.URLConnection;
//import java.net.URLEncoder;
//import java.nio.charset.Charset;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
//@Service
//public class OcrService {
//    private String requestUrl = "https://api.xf-yun.com/v1/private/sf8e6aca1";
//
//    // 控制台获取以下信息
//    private static String APPID = "60930619";
//    private static String apiSecret = "Nzk5ODE3M2MwYTg2MGZjMzJjOTU2Yzlm";
//    private static String apiKey = "2031ba208597af7489c0c815511bcb6a";
//
//    // 图像路径
//    private String imagePath;
//
//    // 解析json
//    private static Gson gson = new Gson();
//
//    public void setImagePath(String imagePath) {
//        this.imagePath = imagePath;
//    }
//
//    public String doRequest() throws Exception {
//        URL realUrl = new URL(buildRequetUrl());
//        URLConnection connection = realUrl.openConnection();
//        HttpURLConnection httpURLConnection = (HttpURLConnection) connection;
//        httpURLConnection.setDoInput(true);
//        httpURLConnection.setDoOutput(true);
//        httpURLConnection.setRequestMethod("POST");
//        httpURLConnection.setRequestProperty("Content-type", "application/json");
//        OutputStream out = httpURLConnection.getOutputStream();
//        String params = buildParam();
////        System.out.println("params=>" + params);
//        out.write(params.getBytes());
//        out.flush();
//        InputStream is = null;
//        try {
//            is = httpURLConnection.getInputStream();
//        } catch (Exception e) {
//            is = httpURLConnection.getErrorStream();
//            throw new Exception("make request error:" + "code is " + httpURLConnection.getResponseMessage() + readAllBytes(is));
//        }
//        String resp = readAllBytes(is);
//        // 解析JSON响应
//        JsonParse myJsonParse = new Gson().fromJson(resp, JsonParse.class);
//        String textBase64Decode = new String(Base64.getDecoder().decode(myJsonParse.payload.result.text), "UTF-8");
//        JSONObject jsonObject = JSON.parseObject(textBase64Decode);
//
//        if (Objects.isNull(jsonObject)) {
//            throw new RuntimeException("识别结果为空");
//        }
//
//        JSONArray pages = jsonObject.getJSONArray("pages");
//
//        StringBuilder contentBuilder = new StringBuilder();
//
//
//        // 遍历每一页
//        for (int i = 0; i < pages.size(); i++) {
//            JSONObject page = pages.getJSONObject(i);
//            JSONArray lines = page.getJSONArray("lines");
//
//            if (Objects.nonNull(lines)) {
//                // 遍历每一行
//                for (int j = 0; j < lines.size(); j++) {
//                    JSONObject line = lines.getJSONObject(j);
//                    JSONArray words = line.getJSONArray("words");
//
//                    if (Objects.nonNull(words)) {
//                        // 遍历每一个单词
//                        for (int k = 0; k < words.size(); k++) {
//                            JSONObject word = words.getJSONObject(k);
//                            String content = word.getString("content");
//                            contentBuilder.append(content);
//                        }
//                    }
//                    // 添加换行符以区分不同行的内容
//                    contentBuilder.append("\n");
//                }
//            }
//
//        }
//
//        String result = contentBuilder.toString().trim();
//        if (StringUtils.isEmpty(result)) {
//            throw new RuntimeException("识别结果为空");
//        }
//        return result;
//    }
//
//    public String buildRequetUrl() {
//        URL url = null;
//        // 替换调schema前缀 ，原因是URL库不支持解析包含ws,wss schema的url
//        String httpRequestUrl = requestUrl.replace("ws://", "http://").replace("wss://", "https://");
//        try {
//            url = new URL(httpRequestUrl);
//            // 获取当前日期并格式化
//            SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
//            format.setTimeZone(TimeZone.getTimeZone("GMT"));
//            String date = format.format(new Date());
//            String host = url.getHost();
//            StringBuilder builder = new StringBuilder("host: ").append(host).append("\n").//
//                    append("date: ").append(date).append("\n").//
//                    append("POST ").append(url.getPath()).append(" HTTP/1.1");
//            Charset charset = Charset.forName("UTF-8");
//            Mac mac = Mac.getInstance("hmacsha256");
//            SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(charset), "hmacsha256");
//            mac.init(spec);
//            byte[] hexDigits = mac.doFinal(builder.toString().getBytes(charset));
//            String sha = Base64.getEncoder().encodeToString(hexDigits);
//            String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"", apiKey, "hmac-sha256", "host date request-line", sha);
//            String authBase = Base64.getEncoder().encodeToString(authorization.getBytes(charset));
//            return String.format("%s?authorization=%s&host=%s&date=%s", requestUrl, URLEncoder.encode(authBase), URLEncoder.encode(host), URLEncoder.encode(date));
//        } catch (Exception e) {
//            throw new RuntimeException("assemble requestUrl error:" + e.getMessage());
//        }
//    }
//
//    private String buildParam() throws IOException {
//        String param = "{" +
//                "    \"header\": {" +
//                "        \"app_id\": \"" + APPID + "\"," +
//                "        \"status\": 3" +
//                "    }," +
//                "    \"parameter\": {" +
//                "        \"sf8e6aca1\": {" +
//                "            \"category\": \"ch_en_public_cloud\"," +
//                "            \"result\": {" +
//                "                \"encoding\": \"utf8\"," +
//                "                \"compress\": \"raw\"," +
//                "                \"format\": \"json\"" +
//                "            }" +
//                "        }" +
//                "    }," +
//                "    \"payload\": {" +
//                "        \"sf8e6aca1_data_1\": {" +
//                "            \"encoding\": \"jpg\"," +
//                "            \"status\": " + 3 + "," +
//                "            \"image\": \"" + Base64.getEncoder().encodeToString(read(imagePath)) + "\"" +
//                "        }" +
//                "    }" +
//                "}";
//        return param;
//    }
//
//    private String readAllBytes(InputStream is) throws IOException {
//        byte[] b = new byte[1024];
//        StringBuilder sb = new StringBuilder();
//        int len = 0;
//        while ((len = is.read(b)) != -1) {
//            sb.append(new String(b, 0, len, "utf-8"));
//        }
//        return sb.toString();
//    }
//
//    public static byte[] read(String filePath) throws IOException {
//        InputStream in = new FileInputStream(filePath);
//        byte[] data = inputStream2ByteArray(in);
//        in.close();
//        return data;
//    }
//
//    private static byte[] inputStream2ByteArray(InputStream in) throws IOException {
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        byte[] buffer = new byte[1024 * 4];
//        int n = 0;
//        while ((n = in.read(buffer)) != -1) {
//            out.write(buffer, 0, n);
//        }
//        return out.toByteArray();
//    }
//}
//
//// 解析json
//class JsonParse {
//    Header header;
//    Payload payload;
//}
//
//class Header {
//    int code;
//    String message;
//    String sid;
//}
//
//class Payload {
//    Result result;
//}
//
//class Result {
//    String text;
//}
//
