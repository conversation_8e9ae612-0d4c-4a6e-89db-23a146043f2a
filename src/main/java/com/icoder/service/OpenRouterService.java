package com.icoder.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

@Service
public class OpenRouterService implements AIInterface{


    @Value("${openRouter.api.url}")
    private String openRouterApiUrl;

    @Value("${openRouter.api.key}")
    private String openRouterApiKey;


    private final RestTemplate restTemplate;

    public OpenRouterService() {
        this.restTemplate = new RestTemplate();
        this.restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }

    public String generateSolution(String problem, String codeLanguage, String model) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + openRouterApiKey);
        headers.set("User-Agent", "MyApp/1.0");
        headers.set("Accept", "application/json");

        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" +
                    "如果问题是编程相关的，请用" +
                    (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") +
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "请解答以下问题：" +
                "问题描述: " + problem +
                "\n\n生成一个解决方案，格式如下：" +
                "{" +
                " \"thoughts\": [\"分析问题，列出解决思路的要点\"]," +
                " \"code\": \"该字段必须是一个字符串，如果是编程问题，在这里提供代码实现，并包含中文注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                " \"time_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                " \"space_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                "}" +
                "\n\n格式要求：" +
                "1. 在code字段中使用实际的换行符，且必须是字符串" +
                "2. 如果是编程问题，请正确缩进代码，使用空格" +
                "3. 如果是编程问题，请用中文给出清晰的代码注释" +
                "4. 无论问题类型，输出必须是纯JSON格式且必须是上面的结构，不需要其他无关字段，不要包含任何Markdown标记(如```json)" +
                "5. 确保thoughts字段包含问题分析的关键点，与解法无关的思路不需要列出" +
                "6. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        Map<String, Object> requestBody = new HashMap<>();

        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");
        requestBody.put("model", model);
        requestBody.put("messages", new Object[]{message, userMessage});


        requestBody.put("response_format", responseFormat);
        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

        String response = restTemplate.postForObject(openRouterApiUrl, requestEntity, String.class);

        // 去除 Markdown 格式
        response = removeMarkdownFormatting(response);


        return response;
    }

    /**
     * 流式生成解决方案，与generateSolution参数一致，但以流式方式返回结果
     * 
     * @param problem 问题描述
     * @param codeLanguage 代码语言
     * @param onChunk 处理每个响应块的回调函数
     * @return CompletableFuture<String> 包含完整响应的Future
     */
    public CompletableFuture<String> generateSolutionStreamAccurate(String problem, String codeLanguage, Consumer<String> onChunk) {
        CompletableFuture<String> future = new CompletableFuture<>();

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + openRouterApiKey);
            headers.set("User-Agent", "MyApp/1.0");
            headers.set("Accept", "application/json");

            Map<String, Object> message = new HashMap<>();
            message.put("role", "system");
            message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" +
                    "如果问题是编程相关的，请用" +
                    (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") +
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", "先精确提取问题，仔细分析出题者意图，解答以下问题：" +
                    "问题描述: " + problem);
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> responseFormat = new HashMap<>();
//            responseFormat.put("type", "json_object");
            requestBody.put("model", "google/gemini-2.0-flash-001");
            requestBody.put("messages", new Object[]{message, userMessage});
            requestBody.put("response_format", responseFormat);
            requestBody.put("stream", true); // 启用流式响应
            
            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // 使用异步处理
            CompletableFuture.runAsync(() -> {
                try {
                    // 使用 restTemplate 发送请求并获取响应
                    ResponseEntity<String> response = restTemplate.exchange(
                            openRouterApiUrl,
                            HttpMethod.POST,
                            requestEntity,
                            String.class
                    );

                    if (response.getStatusCode() != HttpStatus.OK) {
                        future.completeExceptionally(new RuntimeException("HTTP Error: " + response.getStatusCode()));
                        return;
                    }

                    // 处理响应内容
                    String responseBody = response.getBody();
                    if (responseBody != null) {
                        processStreamResponse(responseBody, onChunk, future);
                    } else {
                        future.complete("");
                    }
                } catch (Exception e) {
                    future.completeExceptionally(e);
                }
            });

        } catch (Exception e) {
            future.completeExceptionally(e);
        }

        return future;
    }


    public CompletableFuture<String> generateSolutionStream(String problem, String codeLanguage, Consumer<String> onChunk) {
        CompletableFuture<String> future = new CompletableFuture<>();

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + openRouterApiKey);
            headers.set("User-Agent", "MyApp/1.0");
            headers.set("Accept", "application/json");

            Map<String, Object> message = new HashMap<>();
            message.put("role", "system");
            message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" +
                    "如果问题是编程相关的，请用" +
                    (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") +
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。 + 以下是问题描述：" +  problem);
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> responseFormat = new HashMap<>();
//            responseFormat.put("type", "json_object");
            requestBody.put("model", "google/gemini-2.0-flash-001");
            requestBody.put("messages", new Object[]{message});
            requestBody.put("response_format", responseFormat);
            requestBody.put("stream", true); // 启用流式响应

            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

            // 使用异步处理
            CompletableFuture.runAsync(() -> {
                try {
                    // 使用 restTemplate 发送请求并获取响应
                    ResponseEntity<String> response = restTemplate.exchange(
                            openRouterApiUrl,
                            HttpMethod.POST,
                            requestEntity,
                            String.class
                    );

                    if (response.getStatusCode() != HttpStatus.OK) {
                        future.completeExceptionally(new RuntimeException("HTTP Error: " + response.getStatusCode()));
                        return;
                    }

                    // 处理响应内容
                    String responseBody = response.getBody();
                    if (responseBody != null) {
                        processStreamResponse(responseBody, onChunk, future);
                    } else {
                        future.complete("");
                    }
                } catch (Exception e) {
                    future.completeExceptionally(e);
                }
            });

        } catch (Exception e) {
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 处理流式响应数据
     */
    private void processStreamResponse(String responseBody, Consumer<String> onChunk, CompletableFuture<String> future) {
        StringBuilder fullResponse = new StringBuilder();

        // 按行分割响应
        String[] lines = responseBody.split("\n\n");

        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("data: ")) {
                String jsonData = line.substring(6); // 移除 "data: " 前缀

                if (jsonData.equals("[DONE]")) {
                    continue;
                }

                try {
                    // 从JSON中提取内容
                    if (jsonData.contains("\"choices\"") && jsonData.contains("\"delta\"") && jsonData.contains("\"content\"")) {
                        int contentStart = jsonData.indexOf("\"content\"");
                        if (contentStart > 0) {
                            int valueStart = jsonData.indexOf(":", contentStart) + 1;
                            int valueEnd = jsonData.indexOf("\"", valueStart + 2);
                            if (valueStart > 0 && valueEnd > valueStart) {
                                String content = jsonData.substring(valueStart + 1, valueEnd);

                                if (!content.isEmpty()) {
                                    fullResponse.append(content);
                                    onChunk.accept(content);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    System.err.println("解析JSON时出错: " + e.getMessage());
                }
            }
        }

        // 处理完成，返回完整响应
        String completeResponse = fullResponse.toString();
        completeResponse = removeMarkdownFormatting(completeResponse);
        future.complete(completeResponse);
    }

    private String removeMarkdownFormatting(String response) {
        if (response == null) {
            return null;
        }
        return response.replaceAll("```json", "").replaceAll("```", "");
    }

}