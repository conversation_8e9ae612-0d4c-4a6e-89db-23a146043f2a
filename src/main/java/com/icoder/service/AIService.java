package com.icoder.service;


import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.icoder.dto.ConfigResponse;
import com.icoder.enums.ModelEnum;
import com.icoder.model.ApiKey;
import com.icoder.model.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Consumer;

@Service
public class AIService {


    private static final List<String> Code_Languages = Arrays.asList("Java", "Python", "Python3", "JavaScript", "C++", "C", "Go", "SQL", "Ruby", "Typescript", "Kotlin", "<PERSON>", "Rust");

    @Autowired
    private UsageRecordService usageRecordService;

    @Autowired
    private ApiKeyService apiKeyService;

    @Autowired
    private DeepSeekService deepSeekService;

    @Autowired
    private OpenRouterService openRouterService;

    @Autowired
    private HuoShanService huoShanService;


    private final Map<String, AIInterface> aiServiceMap = new HashMap<>();



    @PostConstruct
    public void init() {
        // 火山引擎
        aiServiceMap.put("deepseek-r1-250528", huoShanService);
        aiServiceMap.put("deepseek-v3-250324", huoShanService);
        aiServiceMap.put("doubao-1-5-thinking-pro-250415", huoShanService);
        aiServiceMap.put("doubao-seed-1-6-flash-250615", huoShanService);


        // DeepSeek
        aiServiceMap.put("deepseek-chat", deepSeekService);
        aiServiceMap.put("deepseek-reasoner", deepSeekService);


        // openrouter
        aiServiceMap.put("anthropic/claude-sonnet-4", openRouterService);
        aiServiceMap.put("anthropic/claude-3.7-sonnet:thinking", openRouterService);
        aiServiceMap.put("google/gemini-2.5-flash-preview:thinking", openRouterService);
        aiServiceMap.put("google/gemini-2.5-flash-preview", openRouterService);

        aiServiceMap.put("x-ai/grok-4", openRouterService);
    }


    public String generateSolutionByModel(String problem, String model, Integer cost, String imageUrl, String key, String codeLanguage, HttpServletRequest request) {
        long start = System.currentTimeMillis();
        ApiKey apiKey = apiKeyService.getApiKey(key);
        if (StringUtils.isNotBlank(codeLanguage)) {
            codeLanguage = apiKey.getCodeLanguage();
        }
        String response = generateSolutionByModelName(problem, model, codeLanguage);
        long end = System.currentTimeMillis();

        // 记录usage，次数记录
        if (Objects.nonNull(response)) {
            try {
                // 记录使用次数
                usageRecordService.recordUsage(key, model, problem, imageUrl, response, request, end - start, cost);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return response;
    }

    /**
     * 根据模型名称动态选择适当的服务生成解决方案
     * @param problem 问题描述
     * @param modelName 模型名称
     * @param codeLanguage 代码语言
     * @return 生成的解决方案
     */
    private String generateSolutionByModelName(String problem, String modelName, String codeLanguage) {
        AIInterface aiInterface = aiServiceMap.get(modelName);
        if (aiInterface != null) {
            return aiInterface.generateSolution(problem, codeLanguage, modelName);
        }
        return deepSeekService.generateSolution(problem, codeLanguage, "deepseek-chat");
    }


    public CompletionStage<String> processAudioStream(String problem, String key, Boolean isAccurate, Consumer<String> onChunk) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        ApiKey apiKey = apiKeyService.getApiKey(key);
        String codeLanguage = apiKey.getCodeLanguage();

        CompletableFuture<String> future;
        String modelName;

        if (isAccurate) {
            future = huoShanService.generateSolutionStreamAccurate(problem, codeLanguage, onChunk);
            modelName = "语音精确模式";
        } else {
            future = huoShanService.generateSolutionStream(problem, codeLanguage, onChunk);
            modelName = "语音极速模式";
        }

        // 在流式处理完成后记录使用情况并消耗1积分
        return future.whenComplete((response, throwable) -> {
            long endTime = System.currentTimeMillis(); // 记录结束时间
            long duration = endTime - startTime; // 计算耗时

            if (throwable == null && Objects.nonNull(response)) {
                try {
                    // 记录使用次数，消耗1积分，包含耗时记录
                    usageRecordService.recordUsage(key, modelName, problem, response, duration, 1);
                } catch (Exception e) {
                    System.err.println("记录音频流处理使用情况时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        });
    }

    public String recognizeByModel(List<String> imageUrls) {
        String problem = huoShanService.recognizeByModel(imageUrls);

        JsonObject jsonObject = JsonParser.parseString(problem).getAsJsonObject();
        JsonArray choices = jsonObject.getAsJsonArray("choices");
        if (choices != null && choices.size() > 0) {
            JsonObject firstChoice = choices.get(0).getAsJsonObject();
            JsonObject message = firstChoice.getAsJsonObject("message");

            return message.get("content").getAsString();

        }
        return "";
    }

    public String recognizeByPictures(List<String> imageUrls, String modelName, Integer cost, String key, HttpServletRequest request) {
        long start = System.currentTimeMillis();
        ApiKey apiKey = apiKeyService.getApiKey(key);
        String codeLanguage = apiKey.getCodeLanguage();
        String response = huoShanService.recognizeByPictures(imageUrls, codeLanguage, modelName);
        long end = System.currentTimeMillis();

        // 记录usage，次数记录
        if (Objects.nonNull(response)) {
            try {
                // 记录使用次数
                usageRecordService.recordUsage(key, modelName, "picture", String.join(",", imageUrls), response, request, end - start, cost);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return response;

    }

    public String recognizeAndGenerateSolutionByModel(List<String> imageUrls, String model, String apiKey, String codeLanguage, HttpServletRequest request) {
        ModelEnum modelEnum = ModelEnum.getModelByAlias(model);
        // 直接支持识别图片的模型
        if (modelEnum.isPicture()) {
            return recognizeByPictures(imageUrls, modelEnum.getModelName(),modelEnum.getCost(), apiKey, request);
        }
        // 需要将图片转为文字的模型
        String problem = recognizeByModel(imageUrls);
        return generateSolutionByModel(problem, modelEnum.getModelName(), modelEnum.getCost(), String.join(",", imageUrls), apiKey, codeLanguage, request);
    }

    public ConfigResponse initConfig(String apiKey) {
        // key 不存在或者已过期或者剩余积分不足2个，则返回 null
        ApiKey key = validateApiKeyForConfig(apiKey);
        if (Objects.isNull(key)) {
            return null;
        }

        // 获取模型列表
        User user = key.getUser();

        ConfigResponse configResponse = new ConfigResponse();

        if (user.getRoles().contains("ADMIN") || user.getRoles().contains("USER")) {
            // 管理员可以使用所有模型
            String[] allModelAlias = ModelEnum.getAllModelAlias();
            configResponse.setModels(Arrays.asList(allModelAlias));

        } else {
            // 普通用户只能使用普通模型
            String[] normalModelAlias = ModelEnum.getAllNormalModelAlias();
            configResponse.setModels(Arrays.asList(normalModelAlias));
        }

        configResponse.setCodeLanguages(Code_Languages);

        ConfigResponse.VoiceConfig voiceConfig = new ConfigResponse.VoiceConfig();
        voiceConfig.setVoiceEnabled(key.isVoiceEnabled());
        voiceConfig.setVoiceAppId(key.getVoiceAppId());
        voiceConfig.setVoiceAccessKeyId(key.getVoiceAccessKeyId());

        configResponse.setVoiceConfig(voiceConfig);
        return configResponse;
    }

    /**
     * 验证API Key是否有效用于配置初始化
     * key 不存在或者已过期或者剩余积分不足2个，则返回 null
     */
    private ApiKey validateApiKeyForConfig(String apiKeyValue) {
        try {
            ApiKey apiKey = apiKeyService.getApiKey(apiKeyValue);

            // 检查key是否激活
            if (!apiKey.isActive()) {
                return null;
            }

            // 检查剩余积分是否不足2个
            if (apiKey.getRemainingCalls() < 2) {
                return null;
            }

            // 检查是否已过期
            if (apiKey.getExpiresAt() != null && apiKey.getExpiresAt().isBefore(LocalDateTime.now())) {
                return null;
            }

            return apiKey;
        } catch (RuntimeException e) {
            // key 不存在时，getApiKey会抛出RuntimeException
            return null;
        }
    }
}
