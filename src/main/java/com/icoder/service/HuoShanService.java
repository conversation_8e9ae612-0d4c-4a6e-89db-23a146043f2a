package com.icoder.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.StreamUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

@Service
public class HuoShanService implements AIInterface{
    @Value("${huoshan.api.url}")
    private String apiUrl;

    @Value("${huoshan.api.key}")
    private String apiKey;




    public String generateSolution(String problem, String codeLanguage, String modelName) {

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" +
                    "如果问题是编程相关的，请用" +
                    (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") +
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "请解答以下问题：" +
                "问题描述: " + problem +
                "\n\n生成一个解决方案，格式如下：" +
                "{" +
                " \"thoughts\": [\"分析问题，列出解决思路的要点\"]," +
                " \"code\": \"该字段必须是一个字符串，如果是算法问题，在这里提供代码实现，并包含注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                " \"time_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                " \"space_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                "}" +
                "\n\n格式要求：" +
                "1. 在code字段中使用实际的换行符，且必须是字符串" +
                "2. 如果是编程问题，请正确缩进代码，使用空格" +
                "3. 如果是编程问题，请包含清晰的代码注释" +
                "4. 无论问题类型，输出必须是纯JSON格式，不要包含任何Markdown标记(如```json)" +
                "5. 确保thoughts字段包含问题分析的关键点" +
                "6. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        Map<String, Object> requestBody = new HashMap<>();

        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");


        requestBody.put("model", modelName);
        requestBody.put("messages", new Object[]{message, userMessage});

        if (modelName.contains("deepseek-v3")) {
            requestBody.put("response_format", responseFormat);
        }

        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);
        String response = restTemplate.postForObject(apiUrl, requestEntity, String.class);

        // 去除 Markdown 格式
        response = removeMarkdownFormatting(response);


        return response;
    }




    private String removeMarkdownFormatting(String response) {
        if (response == null) {
            return null;
        }
        // 只移除可能导致JSON解析错误的markdown标记，保留其他markdown格式
        return response.replaceAll("```json", "").replaceAll("^```$", "");
    }

    public String recognizeByModel(List<String> imageUrls) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        List<Map<String, Object>> imageContents = new ArrayList<>();
        for (String url : imageUrls) {
            Map<String, Object> imageContent = new HashMap<>();
            imageContent.put("type", "image_url");

            Map<String, String> imageUrl = new HashMap<>();
            imageUrl.put("url", url);

            imageContent.put("image_url", imageUrl);
            imageContents.add(imageContent);
        }

        // Create messages
        List<Map<String, Object>> messages = new ArrayList<>();
        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");

        List<Object> content = new ArrayList<>();
        Map<String, String> textContent = new HashMap<>();
        textContent.put("type", "text");
        textContent.put("text", "请从图片或代码编辑器截图中精准提取题目内容，完全忽略与题目无关的所有信息（如IDE界面元素、导航栏、工具栏、控制台输出、装饰图案、广告、冗余注释等）。提取需遵循以下规则：" +
                "1. 题型识别与提取范围：- 若为选择题：必须包含「题干（问题描述）+ 全部选项（带选项标识，如 A/B/C、①/② 等）」 - 若为算法题：提取完整的「题号+问题描述+输入输出要求+限制条件+示例」，仅包含算法题本身内容 - 若为编程题：保留完整题目描述、函数要求、参数说明、返回值说明和示例" +
                "2. 格式与精度要求：- 严格保留题目原文的文字、标点、换行、列表结构和代码缩进 - 对于代码块，保持原有的语法高亮格式和缩进结构 - 若图片存在手写标注或模糊文字，优先提取清晰可辨的部分，模糊部分标注「[模糊]」" +
                "3. 代码编辑器特殊处理：- 忽略IDE左侧的文件导航栏、右侧的工具栏和底部的控制台/终端区域 - 只提取编辑区域中的题目内容 - 保留代码注释中的关键指导信息" +
                "4. 多图片处理：- 如果上传了多张图片，请将所有图片中的问题内容合并输出，确保内容不重复 - 若多张图片表示同一问题的不同部分，请按逻辑顺序组织完整问题 - 若多张图片表示不同问题的不同部分，请分别提取后一起输出" +
                "请将提取结果以纯文本形式输出，提供完整题目内容。");
        content.add(textContent);
        content.addAll(imageContents);

        userMessage.put("content", content);
        messages.add(userMessage);


        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "doubao-1.5-vision-lite-250315");
        requestBody.put("messages", messages);

        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);
        String response = restTemplate.postForObject(apiUrl, requestEntity, String.class);

        return response;

    }


    public String recognizeByPictures(List<String> imageUrls,  String codeLanguage, String modelName) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        List<Map<String, Object>> imageContents = new ArrayList<>();
        for (String url : imageUrls) {
            Map<String, Object> imageContent = new HashMap<>();
            imageContent.put("type", "image_url");

            Map<String, String> imageUrl = new HashMap<>();
            imageUrl.put("url", url);

            imageContent.put("image_url", imageUrl);
            imageContents.add(imageContent);
        }

        // Create messages
        List<Map<String, Object>> messages = new ArrayList<>();
        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" +
                "如果问题是编程相关的，请用" +
                (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") +
                "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");


        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");

        List<Object> content = new ArrayList<>();
        Map<String, String> textContent = new HashMap<>();
        textContent.put("type", "text");
        textContent.put("text", "请从图片或代码编辑器截图中精准提取题目内容，完全忽略与题目无关的所有信息（如IDE界面元素、导航栏、工具栏、控制台输出、装饰图案、广告、冗余注释等）。提取需遵循以下规则：" +
                "1. 题型识别与提取范围：- 若为选择题：必须包含「题干（问题描述）+ 全部选项（带选项标识，如 A/B/C、①/② 等）」 - 若为算法题：提取完整的「题号+问题描述+输入输出要求+限制条件+示例」，仅包含算法题本身内容 - 若为编程题：保留完整题目描述、函数要求、参数说明、返回值说明和示例" +
                "2. 格式与精度要求：- 严格保留题目原文的文字、标点、换行、列表结构和代码缩进 - 对于代码块，保持原有的语法高亮格式和缩进结构 - 若图片存在手写标注或模糊文字，优先提取清晰可辨的部分，模糊部分标注「[模糊]」" +
                "3. 代码编辑器特殊处理：- 忽略IDE左侧的文件导航栏、右侧的工具栏和底部的控制台/终端区域 - 只提取编辑区域中的题目内容 - 保留代码注释中的关键指导信息" +
                "4. 多图片处理：- 如果上传了多张图片，请将所有图片中的问题内容合并输出，确保内容不重复 - 若多张图片表示同一问题的不同部分，请按逻辑顺序组织完整问题 - 若多张图片表示不同问题的不同部分，请分别提取后一起输出" +
                "请将提取结果以纯文本形式输出，提供完整题目内容。" +
                        "\n\n生成一个解决方案，格式如下：" +
                        "{" +
                        " \"thoughts\": [\"分析问题，列出解决思路的要点\"]," +
                        " \"code\": \"该字段必须是一个字符串，如果是算法问题，在这里提供代码实现，并包含注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                        " \"time_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                        " \"space_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                        "}" +
                        "\n\n格式要求：" +
                        "1. 在code字段中使用实际的换行符，且必须是字符串" +
                        "2. 如果是编程问题，请正确缩进代码，使用空格" +
                        "3. 如果是编程问题，请包含清晰的代码注释" +
                        "4. 如果是编程问题，一定要用" + codeLanguage + "编程语言给出代码" +
                        "5. 无论问题类型，输出必须是纯JSON格式，不要包含任何Markdown标记(如```json)" +
                        "6. 确保thoughts字段包含问题分析的关键点" +
                        "7. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        content.add(textContent);
        content.addAll(imageContents);

        userMessage.put("content", content);
        messages.add(userMessage);


        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", modelName);
        requestBody.put("messages", messages);

        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);
        String response = restTemplate.postForObject(apiUrl, requestEntity, String.class);

        return response;

    }

    /**
     * 流式生成解决方案，与generateSolution参数一致，但以流式方式返回结果
     *
     * @param problem 问题描述
     * @param codeLanguage 代码语言
     * @param onChunk 处理每个响应块的回调函数
     * @return CompletableFuture<String> 包含完整响应的Future
     */
    public CompletableFuture<String> generateSolutionStreamAccurate(String problem, String codeLanguage, Consumer<String> onChunk) {
        CompletableFuture<String> future = new CompletableFuture<>();

        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("User-Agent", "MyApp/1.0");
            headers.set("Accept", "text/event-stream");

            Map<String, Object> message = new HashMap<>();
            message.put("role", "system");
            message.put("content", "你是一个顶级的编程和学术问题解答专家，具备以下核心能力：" +
                    "1. 精准的问题识别：能从复杂文本中准确提取核心问题，过滤干扰信息" +
                    "2. 深度意图分析：准确理解出题者的考查目标和期望答案类型" +
                    "3. 专业解答能力：提供准确、完整、深入的专业解答" +
                    "4. 语言编程：熟练使用" + (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") + "等编程语言" +
                    "5. 质量保证：确保答案的正确性、完整性和实用性，绝不遗漏关键信息");

            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", "【精确模式 - 深度分析解答】\n\n" +
                    "🎯 处理流程：\n" +
                    "第一步：智能问题提取与语音识别优化\n" +
                    "• 扫描全文，识别所有问题标识符（问、求、计算、实现、设计等）\n" +
                    "• 自动识别并纠正语音识别常见错误：\n" +
                    "  - 技术术语纠正：SQL注入→SQL注入、MyBatis→MyBatis、Redis→Redis等\n" +
                    "  - 同音字纠正：算法→算法、数据库→数据库、编程→编程等\n" +
                    "  - 英文术语识别：API、HTTP、JSON、XML等可能被识别为中文的情况\n" +
                    "  - 数字和符号：识别可能被误读的数字、运算符、括号等\n" +
                    "• 智能识别题目，处理语音识别中的谐音和误读问题\n" +
                    "• 上下文语义分析：结合前后文判断词汇的正确含义\n" +
                    "• 定位最后一个完整问题: -如果存在提问方，则优先定位提问方的最后一个问题；-如果没有提问方，则定位最后一个完整问题，这通常是核心问题\n" +
                    "• 过滤掉示例说明、背景描述、无关讨论等干扰内容\n" +
                    "• 保留与核心问题相关的约束条件和要求\n\n" +
                    "第二步：深度意图分析与语义理解\n" +
                    "• 分析问题类型：算法设计、代码实现、概念解释、选择判断、计算求解等\n" +
                    "• 识别考查重点：时间复杂度、空间优化、边界处理、特殊情况等\n" +
                    "• 判断答案深度：是否需要详细推导、多种解法、性能分析等\n" +
                    "• 确定输出格式：代码+注释、步骤说明、理论阐述等\n" +
                    "• 语义消歧：对于可能有多种理解的表述，选择最符合技术语境的解释\n\n" +
                    "第三步：专业详细解答\n" +
                    "• 提供完整准确的解答，确保逻辑清晰、步骤完整\n" +
                    "• 编程题：给出完整可运行代码，包含详细注释和复杂度分析\n" +
                    "• 算法题：说明思路、实现步骤、时间空间复杂度、边界处理\n" +
                    "• 理论题：深入解释概念、提供实例、分析应用场景\n" +
                    "• 选择题：分析每个选项，给出正确答案和详细理由\n" +
                    "• 如果发现明显的语音识别错误，在回答中予以纠正并说明\n\n" +
                    "📝 输入内容：\n" + problem + "\n\n" +
                    "请严格按照上述流程，提供高质量的专业解答。特别注意识别和纠正可能的语音识别错误：");
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "doubao-seed-1-6-flash-250615");
            requestBody.put("messages", new Object[]{message, userMessage});
            requestBody.put("stream", true); // 启用流式响应

            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

            // 使用异步处理
            CompletableFuture.runAsync(() -> {
                try {
                    org.springframework.web.client.ResponseExtractor<Void> responseExtractor = response -> {
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))) {
                            
                            StringBuilder buffer = new StringBuilder();
                            String line;
                            
                            while ((line = reader.readLine()) != null) {
                                if (line.startsWith("data:")) {
                                    String jsonData = line.substring(5).trim();
                                    
                                    if (jsonData.equals("[DONE]")) {
                                        continue;
                                    }

                                    buffer.append(jsonData);
                                    onChunk.accept(jsonData);
                                } else if (line.isEmpty() && buffer.length() > 0) {
                                    // 处理缓冲区
                                    buffer.setLength(0);
                                }
                            }
                            
                            // 处理完成，返回完整响应
                            String completeResponse = buffer.toString();
                            // 对于流式响应，保留Markdown格式，不进行移除处理
                            future.complete(completeResponse);
                        } catch (IOException e) {
                            future.completeExceptionally(e);
                        }
                        return null;
                    };

                    // 执行流式请求
                    restTemplate.execute(
                        apiUrl,
                        HttpMethod.POST,
                        request -> {
                            request.getHeaders().putAll(headers);
                            StreamUtils.copy(
                                new ByteArrayInputStream(
                                    new ObjectMapper().writeValueAsBytes(requestBody)
                                ),
                                request.getBody()
                            );
                        },
                        responseExtractor
                    );
                } catch (Exception e) {
                    future.completeExceptionally(e);
                }
            });
        } catch (Exception e) {
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 流式生成解决方案
     */
    public CompletableFuture<String> generateSolutionStream(String problem, String codeLanguage, Consumer<String> onChunk) {
        CompletableFuture<String> future = new CompletableFuture<>();

        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("User-Agent", "MyApp/1.0");
            headers.set("Accept", "text/event-stream");

            Map<String, Object> message = new HashMap<>();
            message.put("role", "system");
            message.put("content", "你是一个顶级的编程和学术问题解答专家，具备以下核心能力：" +
                    "1. 精准的问题识别：能从复杂文本中准确提取核心问题，过滤干扰信息" +
                    "2. 深度意图分析：准确理解出题者的考查目标和期望答案类型" +
                    "3. 专业解答能力：提供准确、完整、深入的专业解答" +
                    "4. 多语言编程：熟练使用" + (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") + "等编程语言" +
                    "5. 质量保证：确保答案的正确性、完整性和实用性，绝不遗漏关键信息");

            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", "【极速模式 - 快速分析解答】\n\n" +
                    "🎯 处理流程：\n" +
                   "第一步：智能问题提取与语音识别优化\n" +
                    "• 扫描全文，识别所有问题标识符（问、求、计算、实现、设计等）\n" +
                    "• 自动识别并纠正语音识别常见错误：\n" +
                    "  - 技术术语纠正：SQL注入→SQL注入、MyBatis→MyBatis、Redis→Redis等\n" +
                    "  - 同音字纠正：算法→算法、数据库→数据库、编程→编程等\n" +
                    "  - 英文术语识别：API、HTTP、JSON、XML等可能被识别为中文的情况\n" +
                    "  - 数字和符号：识别可能被误读的数字、运算符、括号等\n" +
                    "• 智能识别题目，处理语音识别中的谐音和误读问题\n" +
                    "• 上下文语义分析：结合前后文判断词汇的正确含义\n" +
                    "• 定位最后一个完整问题: -如果存在提问方，则优先定位提问方的最后一个问题；-如果没有提问方，则定位最后一个完整问题，这通常是核心问题\n" +
                    "• 过滤掉示例说明、背景描述、无关讨论等干扰内容\n" +
                    "• 保留与核心问题相关的约束条件和要求\n\n" +
                    "第二步：专业简洁解答\n" +
                    "• 提供完整准确的解答，确保逻辑清晰、步骤完整\n" +
                    "• 理论题：深入解释概念、提供实例、分析应用场景\n" +
                    "📝 输入内容：\n" + problem + "\n\n" +
                    "请严格按照上述流程，提供高质量的专业简洁解答，针对每一点，用最少的文案给出关键的解释");

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "doubao-seed-1-6-flash-250615");
            requestBody.put("messages", new Object[]{message, userMessage});
            Map<String, String> thinkingMap = new HashMap<>();
            thinkingMap.put("type", "disabled");
            requestBody.put("thinking", thinkingMap);
            requestBody.put("stream", true); // 启用流式响应

            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

            // 使用异步处理
            CompletableFuture.runAsync(() -> {
                try {
                    org.springframework.web.client.ResponseExtractor<Void> responseExtractor = response -> {
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))) {
                            
                            StringBuilder buffer = new StringBuilder();
                            String line;
                            
                            while ((line = reader.readLine()) != null) {
                                if (line.startsWith("data:")) {
                                    String jsonData = line.substring(5).trim();
                                    
                                    if (jsonData.equals("[DONE]")) {
                                        continue;
                                    }

                                    buffer.append(jsonData);
                                    onChunk.accept(jsonData);
                                } else if (line.isEmpty() && buffer.length() > 0) {
                                    // 处理缓冲区
                                    buffer.setLength(0);
                                }
                            }
                            
                            // 处理完成，返回完整响应
                            String completeResponse = buffer.toString();
                            // 对于流式响应，保留Markdown格式，不进行移除处理
                            future.complete(completeResponse);
                        } catch (IOException e) {
                            future.completeExceptionally(e);
                        }
                        return null;
                    };

                    // 执行流式请求
                    restTemplate.execute(
                        apiUrl,
                        HttpMethod.POST,
                        request -> {
                            request.getHeaders().putAll(headers);
                            StreamUtils.copy(
                                new ByteArrayInputStream(
                                    new ObjectMapper().writeValueAsBytes(requestBody)
                                ),
                                request.getBody()
                            );
                        },
                        responseExtractor
                    );
                } catch (Exception e) {
                    future.completeExceptionally(e);
                }
            });
        } catch (Exception e) {
            future.completeExceptionally(e);
        }

        return future;
    }
}