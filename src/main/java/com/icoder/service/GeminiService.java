package com.icoder.service;

import cn.hutool.json.JSONUtil;
import com.icoder.model.ApiKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

@Service
public class GeminiService {


    @Value("${gemini.api.url}")
    private String geminiApiUrl;

    @Value("${gemini.api.key}")
    private String geminiApiKey;


    private final String[] GEMINI_MODELS = {"gemini-2.0-flash-exp", "gemini-2.5-flash-preview-04-17", "gemini-2.0-flash"};


    private final RestTemplate restTemplate;

    public GeminiService() {
        this.restTemplate = new RestTemplate();
        this.restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }

    /**
     * 根据模型名称调用适当的 Gemini 接口，提供更好的可扩展性
     * 
     * @param problem 问题描述
     * @param modelName 模型名称 (gemini/gemini-pro-1.5:free 或其他模型)
     * @param codeLanguage 代码语言
     * @return 生成的解决方案
     */
    public String generateSolutionByGemini(String problem, String modelName, String codeLanguage) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + geminiApiKey);
        headers.set("User-Agent", "MyApp/1.0");
        headers.set("Accept", "application/json");

        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" +
                  "如果问题是编程相关的，请用" +
                  (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") +
                  "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "请解答以下问题：" +
                "问题描述: " + problem +
                "\n\n生成一个解决方案，格式如下：" +
                "{" +
                " \"thoughts\": [\"分析问题，用中文列出解决思路的要点\"]," +
                " \"code\": \"如果是编程问题，在这里提供代码实现，并包含中文注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                " \"time_complexity\": \"如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                " \"space_complexity\": \"如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                "}" +
                "\n\n格式要求：" +
                "1. 在code字段中使用实际的换行符，且必须是字符串" +
                "2. 如果是编程问题，请正确缩进代码，使用空格" +
                "3. 如果是编程问题，请用中文给出清晰的代码注释" +
                "4. 无论问题类型，输出必须是纯JSON格式，不要包含任何Markdown标记(如```json)" +
                "5. 确保thoughts字段包含问题分析的关键点" +
                "6. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");
        
        // 从 modelName 中提取实际的模型名称
        String actualModel = extractModelName(modelName);
        
        requestBody.put("model", actualModel);
        requestBody.put("messages", new Object[]{message, userMessage});
        requestBody.put("response_format", responseFormat);
        
        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

        String response = restTemplate.postForObject(geminiApiUrl, requestEntity, String.class);
        
        // 去除 Markdown 格式
        response = removeMarkdownFormatting(response);

        return response;
    }

    /**
     * 从完整的模型标识符中提取模型名称
     * 例如: "gemini/gemini-pro-1.5:free" -> "gemini-2.5-flash-preview-04-17"
     */
    private String extractModelName(String modelName) {
        // 默认使用最新的 Gemini 模型
        String defaultModel = "gemini-2.5-flash-preview-04-17";
        
        if (StringUtils.isEmpty(modelName)) {
            return defaultModel;
        }
        
        // 处理格式如 "gemini/gemini-pro-1.5:free"
        if (modelName.contains("/")) {
            String[] parts = modelName.split("/");
            if (parts.length > 1) {
                // 我们不直接使用分割后的名称，而是映射到支持的 Gemini 模型
                if (parts[1].contains("pro")) {
                    return "gemini-2.5-flash-preview-04-17"; // 使用高级模型
                } else if (parts[1].contains("lite")) {
                    return "gemini-2.0-flash"; // 使用轻量模型
                }
            }
        }
        
        // 尝试在已知的模型列表中匹配
        for (String supportedModel : GEMINI_MODELS) {
            if (modelName.contains(supportedModel)) {
                return supportedModel;
            }
        }
        
        return defaultModel;
    }

    public String generateSolutionByGeminiChat(String problem, String codeLanguage) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + geminiApiKey);
        headers.set("User-Agent", "MyApp/1.0");
        headers.set("Accept", "application/json");

        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" +
                    "如果问题是编程相关的，请用" +
                    (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") +
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "请解答以下问题：" +
                "问题描述: " + problem +
                "\n\n生成一个解决方案，格式如下：" +
                "{" +
                " \"thoughts\": [\"分析问题，用中文列出解决思路的要点\"]," +
                " \"code\": \"如果是编程问题，在这里提供代码实现，并包含中文注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                " \"time_complexity\": \"如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                " \"space_complexity\": \"如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                "}" +
                "\n\n格式要求：" +
                "1. 在code字段中使用实际的换行符，且必须是字符串" +
                "2. 如果是编程问题，请正确缩进代码，使用空格" +
                "3. 如果是编程问题，请用中文给出清晰的代码注释" +
                "4. 无论问题类型，输出必须是纯JSON格式，不要包含任何Markdown标记(如```json)" +
                "5. 确保thoughts字段包含问题分析的关键点" +
                "6. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        Map<String, Object> requestBody = new HashMap<>();

        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");
        requestBody.put("model", "gemini-2.5-flash-preview-04-17");
        requestBody.put("messages", new Object[]{message, userMessage});


        requestBody.put("response_format", responseFormat);
        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

        String response = restTemplate.postForObject(geminiApiUrl, requestEntity, String.class);

        // 去除 Markdown 格式
        response = removeMarkdownFormatting(response);


        return response;
    }



    private String removeMarkdownFormatting(String response) {
        if (response == null) {
            return null;
        }
        return response.replaceAll("```json", "").replaceAll("```", "");
    }


    public CompletableFuture<String> processAudioStream(String audioFilePath, String prompt, Consumer<String> onChunk) {
        CompletableFuture<String> future = new CompletableFuture<>();

        try {
            // Read audio file and convert to Base64
            byte[] audioBytes = Files.readAllBytes(new File(audioFilePath).toPath());
            String base64Audio = Base64.getEncoder().encodeToString(audioBytes);

            // Get MIME type
            String mimeType = getMimeTypeFromFilePath(audioFilePath);

            // Build request body
            String requestBody = buildRequestBody(base64Audio, mimeType, prompt);

            // Create HTTP headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + geminiApiKey);
            headers.set("User-Agent", "SpringBootApp/1.0");
            headers.set("Accept", "application/json");

            // Create request entity
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            // Use a separate thread to make the request and process the stream
            CompletableFuture.runAsync(() -> {
                try {
                    // Make a request with streaming response
                    ResponseEntity<String> response = restTemplate.exchange(
                            geminiApiUrl,
                            HttpMethod.POST,
                            requestEntity,
                            String.class
                    );

                    if (response.getStatusCode() != HttpStatus.OK) {
                        future.completeExceptionally(new RuntimeException("HTTP Error: " + response.getStatusCode()));
                        return;
                    }

                    // Process the response body
                    String responseBody = response.getBody();
                    if (responseBody != null) {
                        processStreamResponse(responseBody, onChunk, future);
                    } else {
                        future.complete("");
                    }
                } catch (Exception e) {
                    future.completeExceptionally(e);
                }
            });

        } catch (IOException e) {
            future.completeExceptionally(e);
        }

        return future;
    }

    private void processStreamResponse(String responseBody, Consumer<String> onChunk, CompletableFuture<String> future) {
        StringBuilder fullResponse = new StringBuilder();

        // Split the response by lines
        String[] lines = responseBody.split("\n\n");

        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("data: ")) {
                String jsonData = line.substring(6); // Remove "data: " prefix

                if (jsonData.equals("[DONE]")) {
                    continue;
                }

                try {
                    // For simplicity, we're using string operations to extract content
                    // In production, use a proper JSON parser
                    if (jsonData.contains("\"choices\"") && jsonData.contains("\"delta\"") && jsonData.contains("\"content\"")) {
                        int contentStart = jsonData.indexOf("\"content\"");
                        if (contentStart > 0) {
                            int valueStart = jsonData.indexOf(":", contentStart) + 1;
                            int valueEnd = jsonData.indexOf("\"", valueStart + 2);
                            if (valueStart > 0 && valueEnd > valueStart) {
                                String content = jsonData.substring(valueStart + 1, valueEnd);

                                if (!content.isEmpty()) {
                                    fullResponse.append(content);
                                    System.out.println("Received chunk: " + content);
                                    onChunk.accept(content);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error parsing JSON: " + e.getMessage());
                }
            }
        }

        future.complete(fullResponse.toString());
    }

    // For true streaming, you'd need to use a custom ClientHttpRequest implementation
    // Here's a more advanced solution using RestTemplate with a custom request factory
    public CompletableFuture<String> processAudioStreamAdvanced(String audioFilePath, String prompt, Consumer<String> onChunk) {
        CompletableFuture<String> future = new CompletableFuture<>();

        try {
            // Read audio file and convert to Base64
            byte[] audioBytes = Files.readAllBytes(new File(audioFilePath).toPath());
            String base64Audio = Base64.getEncoder().encodeToString(audioBytes);

            // Get MIME type
            String mimeType = getMimeTypeFromFilePath(audioFilePath);

            // Build request body
            String requestBody = buildRequestBody(base64Audio, mimeType, prompt);

            // Create command to use curl for streaming
            String[] command = {
                    "curl",
                    "-X", "POST",
                    "-H", "Content-Type: application/json",
                    "-H", "Authorization: Bearer " + geminiApiKey,
                    "-H", "User-Agent: SpringBootApp/1.0",
                    "-H", "Accept: application/json",
                    "-d", requestBody,
                    geminiApiUrl
            };

            // Execute the curl command
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();

            // Read the output stream
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            // Process the output line by line
            CompletableFuture.runAsync(() -> {
                try {
                    StringBuilder buffer = new StringBuilder();
                    StringBuilder fullResponseBuilder = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        buffer.append(line).append("\n");

                        // Process complete data chunks
                        if (line.isEmpty() && buffer.length() > 0) {
                            String chunk = buffer.toString().trim();
                            buffer = new StringBuilder();

                            if (chunk.startsWith("data: ")) {
                                String jsonData = chunk.substring(6).trim();

                                if (jsonData.equals("[DONE]")) {
                                    continue;
                                }

                                try {
                                    // Extract content from JSON
                                    if (jsonData.contains("\"choices\"") && jsonData.contains("\"delta\"") && jsonData.contains("\"content\"")) {
                                        int contentStart = jsonData.indexOf("\"content\"");
                                        if (contentStart > 0) {
                                            int valueStart = jsonData.indexOf(":", contentStart) + 1;
                                            int valueEnd = jsonData.indexOf("\"", valueStart + 2);
                                            if (valueStart > 0 && valueEnd > valueStart) {
                                                String content = jsonData.substring(valueStart + 1, valueEnd);

                                                if (!content.isEmpty()) {
                                                    fullResponseBuilder.append(content);
                                                    onChunk.accept(content);
                                                }
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    System.err.println("Error parsing JSON: " + e.getMessage());
                                }
                            }
                        }
                    }

                    future.complete(fullResponseBuilder.toString());
                } catch (IOException e) {
                    future.completeExceptionally(e);
                }
            });

        } catch (IOException e) {
            future.completeExceptionally(e);
        }

        return future;
    }

    private String buildRequestBody(String base64Audio, String mimeType, String prompt) {
        return "{\n" +
                "  \"model\": \"gemini-2.0-flash-lite\",\n" +
                "  \"messages\": [\n" +
                "    {\n" +
                "      \"role\": \"system\",\n" +
                "      \"content\": \"你是一个专业的助手，擅长解答各类问题。请根据音频内容，给出详细解答。\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"role\": \"user\",\n" +
                "      \"content\": [\n" +
                "        { \"type\": \"text\", \"text\": \"" + prompt + "\" },\n" +
                "        { \"type\": \"input_audio\", \"input_audio\": { \"data\": \"" + base64Audio + "\", \"format\": \"" + mimeType + "\" }}\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"stream\": true\n" +
                "}";
    }

    private String getMimeTypeFromFilePath(String filePath) {
        String extension = filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "mp3": return "audio/mpeg";
            case "wav": return "audio/wav";
            case "ogg": return "audio/ogg";
            case "m4a": return "audio/mp4";
            case "flac": return "audio/flac";
            case "webm": return "audio/webm";
            default: return "audio/mpeg";
        }
    }
}