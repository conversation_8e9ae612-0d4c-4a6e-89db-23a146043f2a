package com.icoder.service;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service
public class QRCodeService {

    @Value("${payment.wechat.qrcode.template:wxp://f2f0XXXXXXXXXX}")
    private String wechatQRTemplate;

    @Value("${payment.alipay.qrcode.template:https://qr.alipay.com/XXXXXXXXXX}")
    private String alipayQRTemplate;

    /**
     * 生成微信支付二维码数据
     * 注意：这是一个简化的实现，实际项目中需要集成微信支付SDK
     */
    public String generateWeChatPayQRCode(String orderNumber, BigDecimal amount) {
        // 这里应该调用微信支付API生成真实的二维码
        // 目前生成一个包含订单信息的二维码
        String qrContent = String.format("微信支付\n订单号: %s\n金额: ￥%.2f\n请使用微信扫码支付", orderNumber, amount);

        try {
            String qrCodeBase64 = generateQRCodeBase64(qrContent, 300, 300);
            return "data:image/png;base64," + qrCodeBase64;
        } catch (Exception e) {
            // 如果生成失败，返回模拟数据
            String mockQRCodeBase64 = generateMockQRCodeBase64(qrContent);
            return "data:image/png;base64," + mockQRCodeBase64;
        }
    }

    /**
     * 生成支付宝支付二维码数据
     */
    public String generateAlipayQRCode(String orderNumber, BigDecimal amount) {
        // 这里应该调用支付宝API生成真实的二维码
        String qrContent = String.format("支付宝支付\n订单号: %s\n金额: ￥%.2f\n请使用支付宝扫码支付", orderNumber, amount);

        try {
            String qrCodeBase64 = generateQRCodeBase64(qrContent, 300, 300);
            return "data:image/png;base64," + qrCodeBase64;
        } catch (Exception e) {
            // 如果生成失败，返回模拟数据
            String mockQRCodeBase64 = generateMockQRCodeBase64(qrContent);
            return "data:image/png;base64," + mockQRCodeBase64;
        }
    }

    /**
     * 使用ZXing生成二维码的base64数据
     */
    private String generateQRCodeBase64(String content, int width, int height) throws WriterException, IOException {
        // 设置二维码参数
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);

        // 生成二维码
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 创建BufferedImage
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
            }
        }

        // 转换为base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 生成模拟的二维码base64数据（备用方案）
     */
    private String generateMockQRCodeBase64(String content) {
        // 这是一个1x1像素的透明PNG图片的base64编码
        String mockBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==";
        return mockBase64;
    }

    /**
     * 验证二维码是否有效
     */
    public boolean isQRCodeValid(String qrCodeData) {
        return qrCodeData != null && qrCodeData.startsWith("data:image/");
    }
}
