package com.icoder.service;

import com.icoder.model.User;
import com.icoder.model.VerificationToken;
import com.icoder.repository.UserRepository;
import com.icoder.repository.VerificationTokenRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class UserService implements UserDetailsService {

    private UserRepository userRepository;
    private VerificationTokenRepository tokenRepository;
    private PasswordEncoder passwordEncoder;
    private EmailService emailService;

    @Autowired
    public UserService(UserRepository userRepository,
                      VerificationTokenRepository tokenRepository,
                      @Lazy PasswordEncoder passwordEncoder,
                      EmailService emailService) {
        this.userRepository = userRepository;
        this.tokenRepository = tokenRepository;
        this.passwordEncoder = passwordEncoder;
        this.emailService = emailService;
    }

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        return userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));
    }

    @Transactional
    public User registerUser(User user, String inviteCode) {
        // Check if user with this email already exists
        if (userRepository.findByEmail(user.getEmail()).isPresent()) {
            throw new RuntimeException("Email is already in use");
        }
        
        // Process invite code if provided
        if (inviteCode != null && !inviteCode.isEmpty()) {
            Optional<User> referrer = userRepository.findByInviteCode(inviteCode);
            if (referrer.isPresent()) {
                user.setReferredBy(referrer.get().getEmail());
            }
        }
        
        // Generate a unique invite code for this user
        user.setInviteCode(generateInviteCode());
        
        return userRepository.save(user);
    }

    private String generateInviteCode() {
        return UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    @Transactional
    public String createVerificationToken(User user, String tokenType) {
        // Check if token already exists
        Optional<VerificationToken> existingToken = tokenRepository.findByUserAndTokenType(user, tokenType);
        if (existingToken.isPresent()) {
            tokenRepository.delete(existingToken.get());
        }

        // Create verification token
        String token = UUID.randomUUID().toString();
        VerificationToken verificationToken = new VerificationToken();
        verificationToken.setToken(token);
        verificationToken.setUser(user);
        verificationToken.setExpiryDate(LocalDateTime.now().plusHours(24));
        verificationToken.setTokenType(tokenType);
        tokenRepository.save(verificationToken);
        
        return token;
    }

    @Transactional
    public boolean verifyEmail(String token) {
        Optional<VerificationToken> verificationToken = tokenRepository.findByToken(token);
        if (verificationToken.isPresent() && !verificationToken.get().isExpired()) {
            User user = verificationToken.get().getUser();
            user.setEnabled(true);
            userRepository.save(user);
            tokenRepository.delete(verificationToken.get());
            return true;
        }
        return false;
    }

    public String generateEmailVerificationCode() {
        Random random = new Random();
        int code = 100000 + random.nextInt(900000); // 6-digit code
        return String.valueOf(code);
    }

    /**
     * Sends a verification email with a 6-digit code to the specified email address
     */
    @Transactional
    public void sendVerificationEmail(String email) {
        // Generate a 6-digit verification code
        String code = generateEmailVerificationCode();
        
        // Find or create a temporary user for this email
        User user;
        Optional<User> userOpt = userRepository.findByEmail(email);
        
        if (userOpt.isPresent()) {
            user = userOpt.get();
        } else {
            // Create a temporary user record for this email
            user = new User();
            user.setEmail(email);
            user.setPassword(passwordEncoder.encode(UUID.randomUUID().toString())); // Random temporary password
            user.setFullName(email);
            user.setEnabled(false);
            user.setCreatedAt(LocalDateTime.now());
            
            // Set default role
            Set<String> roles = new HashSet<>();
            roles.add("USER");
            user.setRoles(roles);
            
            user = userRepository.save(user);
        }
        
        // Create or update verification token
        Optional<VerificationToken> existingToken = tokenRepository.findByUserAndTokenType(user, "EMAIL_VERIFICATION");
        if (existingToken.isPresent()) {
            // Update existing token
            VerificationToken token = existingToken.get();
            token.setToken(code);
            token.setExpiryDate(LocalDateTime.now().plusMinutes(5)); // Set expiry to 1 hour
            tokenRepository.save(token);
        } else {
            // Create new token
            VerificationToken verificationToken = new VerificationToken();
            verificationToken.setToken(code);
            verificationToken.setUser(user);
            verificationToken.setExpiryDate(LocalDateTime.now().plusMinutes(5));
            verificationToken.setTokenType("EMAIL_VERIFICATION");
            tokenRepository.save(verificationToken);
        }
        
        // Send email with verification code
        String subject = "Your Verification Code";
        String content = "Hello,\n\n"
                + "Your verification code is: " + code + "\n\n"
                + "This code will expire in 5 minutes.\n\n"
                + "If you did not request this code, please ignore this email.\n\n"
                + "Best regards,\nThe Offer-Helper Team";
        
        emailService.sendEmail(email, subject, content);
    }

    @Transactional
    public boolean verifyEmailCode(String email, String code) {
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            Optional<VerificationToken> tokenOpt = tokenRepository.findByUserAndTokenType(user, "EMAIL_VERIFICATION");
            if (tokenOpt.isPresent() && tokenOpt.get().getToken().equals(code) && !tokenOpt.get().isExpired()) {
                user.setEnabled(true);
                userRepository.save(user);
                tokenRepository.delete(tokenOpt.get());
                return true;
            }
        }
        return false;
    }

    @Transactional
    public void updateLastLogin(String email) {
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setLastLogin(LocalDateTime.now());
            userRepository.save(user);
        }
    }

    /**
     * Get all users (admin function)
     */
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }
    
    /**
     * Get user by ID
     */
    public User getUserById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with id: " + id));
    }
    
    /**
     * Update user role
     */
    @Transactional
    public void updateUserRole(Long userId, String role) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with id: " + userId));
        
        Set<String> roles = new HashSet<>(user.getRoles());
        roles.add(role.toUpperCase());
        user.setRoles(roles);
        
        userRepository.save(user);
    }

    /**
     * 更新用户密码
     * @param email 用户邮箱
     * @param newPassword 新密码（未加密）
     * @return 更新后的用户对象
     */
    @Transactional
    public User updatePassword(String email, String newPassword) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));
        
        // 加密新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        
        // 保存更新后的用户
        return userRepository.save(user);
    }
} 