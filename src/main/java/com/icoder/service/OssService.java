package com.icoder.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class OssService {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    public String uploadFile(String filePath, String objectName) throws IOException {
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try (InputStream inputStream = new java.io.FileInputStream(filePath)) {
            // 上传文件。
            ossClient.putObject(bucketName, objectName, inputStream);
        } finally {
            // 关闭OSSClient。
            ossClient.shutdown();
        }

        // 返回文件在OSS上的URL
        return "http://" + bucketName + "." + endpoint + "/" + objectName;
    }


    public List<String> uploadFiles(MultipartFile[] files, String prefix) throws IOException {
        List<String> uploadedUrls = new ArrayList<>();
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            for (MultipartFile file : files) {
                String objectName = prefix + "/" + file.getOriginalFilename();
                ossClient.putObject(bucketName, objectName, file.getInputStream());
                String fileUrl = "http://" + bucketName + "." + endpoint + "/" + objectName;
                uploadedUrls.add(fileUrl);
            }
        } finally {
            ossClient.shutdown();
        }

        return uploadedUrls;
    }

}
