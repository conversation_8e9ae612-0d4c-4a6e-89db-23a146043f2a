package com.icoder.service;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.icoder.dto.UsageRecordDto;
import com.icoder.model.ApiKey;
import com.icoder.model.UsageRecord;
import com.icoder.model.User;
import com.icoder.repository.ApiKeyRepository;
import com.icoder.repository.UsageRecordRepository;
import com.icoder.repository.UserRepository;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class UsageRecordService {

    private final UsageRecordRepository usageRecordRepository;
    private final ApiKeyRepository apiKeyRepository;
    private final UserRepository userRepository;

    @Autowired
    public UsageRecordService(UsageRecordRepository usageRecordRepository,
                             ApiKeyRepository apiKeyRepository,
                             UserRepository userRepository) {
        this.usageRecordRepository = usageRecordRepository;
        this.apiKeyRepository = apiKeyRepository;
        this.userRepository = userRepository;
    }

    @Transactional
    public UsageRecord recordUsage(String keyValue, String endpoint, String requestData, String imageUrl,
                                  String responseData, HttpServletRequest request, Long duration, Integer cost) {
        ApiKey apiKey = apiKeyRepository.findByKeyValue(keyValue)
                .orElseThrow(() -> new RuntimeException("API key not found: " + keyValue));

        try {
            // 解析 JSON 字符串
            JsonObject jsonObject = JsonParser.parseString(responseData).getAsJsonObject();
            JsonArray choices = jsonObject.getAsJsonArray("choices");
            if (choices != null && choices.size() > 0) {
                JsonObject firstChoice = choices.get(0).getAsJsonObject();
                JsonObject message = firstChoice.getAsJsonObject("message");

                // 设置 reasoning_content 为空字符串
                message.addProperty("reasoning_content", "");
            }
            // 转回字符串
            responseData = jsonObject.toString();

        } catch (Exception e) {
            System.out.println("Error parsing responseData JSON: " + e.getMessage());
        }

        UsageRecord usageRecord = new UsageRecord();
        usageRecord.setApiKey(apiKey);
        usageRecord.setEndpoint(endpoint);
        usageRecord.setRequestData(requestData);
        usageRecord.setResponseData(responseData);
        usageRecord.setIpAddress(request.getRemoteAddr());
        usageRecord.setUserAgent(request.getHeader("User-Agent"));
        usageRecord.setImageUrl(imageUrl);
        usageRecord.setDuration(duration);

        apiKey.setRemainingCalls(apiKey.getRemainingCalls() - cost);
        apiKeyRepository.save(apiKey);
        return usageRecordRepository.save(usageRecord);
    }


    @Transactional
    public UsageRecord recordUsage(String keyValue, String endpoint, String requestData,
                                   String responseData, Long duration, Integer cost) {
        ApiKey apiKey = apiKeyRepository.findByKeyValue(keyValue)
                .orElseThrow(() -> new RuntimeException("API key not found: " + keyValue));

        UsageRecord usageRecord = new UsageRecord();
        usageRecord.setApiKey(apiKey);
        usageRecord.setEndpoint(endpoint);
        usageRecord.setRequestData(requestData);
        usageRecord.setResponseData(responseData);
        usageRecord.setDuration(duration);

        apiKey.setRemainingCalls(apiKey.getRemainingCalls() - cost);
        apiKeyRepository.save(apiKey);
        return usageRecordRepository.save(usageRecord);
    }

    public List<UsageRecordDto> getUserUsageRecords(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        return usageRecordRepository.findByUserId(user.getId()).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 分页获取用户的调用记录
     */
    public Page<UsageRecordDto> getUserUsageRecords(String email, Pageable pageable) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        return usageRecordRepository.findByApiKey_User_IdOrderByUsedAtDesc(user.getId(), pageable)
                .map(this::convertToDto);
    }

    /**
     * 分页获取用户指定API Key的调用记录
     */
    public Page<UsageRecordDto> getUserUsageRecordsByApiKey(String email, Long apiKeyId, Pageable pageable) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        return usageRecordRepository.findByApiKey_IdAndApiKey_User_IdOrderByUsedAtDesc(apiKeyId, user.getId(), pageable)
                .map(this::convertToDto);
    }

    public List<UsageRecordDto> getAllUsageRecords() {
        return usageRecordRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 分页获取所有调用记录（管理员功能）
     */
    public Page<UsageRecordDto> getAllUsageRecords(Pageable pageable) {
        return usageRecordRepository.findAllByOrderByUsedAtDesc(pageable)
                .map(this::convertToDto);
    }

    public UsageRecordDto convertToDto(UsageRecord usageRecord) {
        UsageRecordDto dto = new UsageRecordDto();
        dto.setId(usageRecord.getId());
        dto.setApiKeyId(usageRecord.getApiKey().getId());
        dto.setApiKeyValue(usageRecord.getApiKey().getKeyValue());
        dto.setUsedAt(usageRecord.getUsedAt());
        dto.setDuration(usageRecord.getDuration());
        dto.setEndpoint(usageRecord.getEndpoint());
        return dto;
    }

    @Transactional
    public UsageRecord updateUsageRecordNotes(Long id, String notes) {
        UsageRecord usageRecord = usageRecordRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Usage record not found with id: " + id));

        usageRecord.setNotes(notes);
        return usageRecordRepository.save(usageRecord);
    }

    /**
     * Find usage records by user ID
     */
    public List<UsageRecordDto> findByUserId(Long userId) {
        return usageRecordRepository.findByUserId(userId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Check if a user is the owner of a usage record
     */
    public boolean isUsageRecordOwner(Long usageRecordId, Long userId) {
        Optional<UsageRecord> recordOpt = usageRecordRepository.findById(usageRecordId);
        if (recordOpt.isPresent()) {
            return recordOpt.get().getApiKey().getUser().getId().equals(userId);
        }
        return false;
    }

    /**
     * 获取用户的总调用数
     */
    public Long getUserTotalCallCount(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        return usageRecordRepository.countByUserId(user.getId());
    }
}