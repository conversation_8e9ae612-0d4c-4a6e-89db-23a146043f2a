package com.icoder.service;

import com.icoder.model.ApiKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class DeepSeekService implements AIInterface{

    @Autowired
    private UsageRecordService usageRecordService;

    @Autowired
    private ApiKeyService apiKeyService;

    @Value("${deepseek.api.url}")
    private String apiUrl;

    @Value("${deepseek.api.key}")
    private String apiKey;

    /**
     * 根据模型名称调用适当的 DeepSeek 接口，提供更好的可扩展性
     * 
     * @param problem 问题描述
     * @param modelName 模型名称 (deepseek/deepseek-chat-v3-0324:free 或其他模型)
     * @param codeLanguage 代码语言
     * @return 生成的解决方案
     */
    public String generateSolution(String problem, String codeLanguage, String modelName) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" + 
                    "如果问题是编程相关的，请用" + 
                    (StringUtils.isNotEmpty(codeLanguage) ? codeLanguage : "Java") + 
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "请解答以下问题：" +
                "问题描述: " + problem +
                "\n\n生成一个解决方案，格式如下：" +
                "{" +
                " \"thoughts\": [\"分析问题，列出解决思路的要点\"]," +
                " \"code\": \"该字段必须是一个字符串，如果是算法问题，在这里提供代码实现，并包含注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                " \"time_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                " \"space_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                "}" +
                "\n\n格式要求：" +
                "1. 在code字段中使用实际的换行符，且必须是字符串" +
                "2. 如果是编程问题，请正确缩进代码，使用空格" +
                "3. 如果是编程问题，请包含清晰的代码注释" +
                "4. 无论问题类型，输出必须是纯JSON格式，不要包含任何Markdown标记(如```json)" +
                "5. 确保thoughts字段包含问题分析的关键点" +
                "6. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");

        requestBody.put("model", modelName);
        requestBody.put("messages", new Object[]{message, userMessage});
        if (modelName.contains("chat") || modelName.contains("v3")) {
            requestBody.put("response_format", responseFormat);
        }

        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

        String response = restTemplate.postForObject(apiUrl, requestEntity, String.class);
        
        // 去除 Markdown 格式
        response = removeMarkdownFormatting(response);

        return response;
    }

    public String generateSolutionByDeepSeekChat(String problem, String key, String imageUrl, HttpServletRequest request) {
        long start = System.currentTimeMillis();
        ApiKey apiKey1 = apiKeyService.getApiKey(key);
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" + 
                    "如果问题是编程相关的，请用" + 
                    (StringUtils.isNotEmpty(apiKey1.getCodeLanguage()) ? apiKey1.getCodeLanguage() : "Java") + 
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "请解答以下问题：" +
                "问题描述: " + problem +
                "\n\n生成一个解决方案，格式如下：" +
                "{" +
                " \"thoughts\": [\"分析问题，列出解决思路的要点\"]," +
                " \"code\": \"该字段必须是一个字符串，如果是算法问题，在这里提供代码实现，并包含注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                " \"time_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                " \"space_complexity\": \"该字段必须是一个字符串，如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                "}" +
                "\n\n格式要求：" +
                "1. 在code字段中使用实际的换行符，且必须是字符串" +
                "2. 如果是编程问题，请正确缩进代码，使用空格" +
                "3. 如果是编程问题，请包含清晰的代码注释" +
                "4. 无论问题类型，输出必须是纯JSON格式，不要包含任何Markdown标记(如```json)" +
                "5. 确保thoughts字段包含问题分析的关键点" +
                "6. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        Map<String, Object> requestBody = new HashMap<>();

        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");


        requestBody.put("model", "deepseek-chat");
        requestBody.put("messages", new Object[]{message, userMessage});
        requestBody.put("response_format", responseFormat);

        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

        String response = restTemplate.postForObject(apiUrl, requestEntity, String.class);

        // 去除 Markdown 格式
        response = removeMarkdownFormatting(response);

        long end = System.currentTimeMillis();
        // 记录usage，次数记录
        if (Objects.nonNull(response)) {
            usageRecordService.recordUsage(key, "deepseek-chat", problem, imageUrl, response, request, end - start, 5);
        }

        return response;
    }

    public String generateSolutionByDeepSeekR1(String problem, String key, String imageUrl, HttpServletRequest request) {
        long start = System.currentTimeMillis();

        ApiKey apiKey1 = apiKeyService.getApiKey(key);

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        Map<String, Object> message = new HashMap<>();
        message.put("role", "system");
        message.put("content", "你是一个专业的助手，擅长解决各类问题。请根据提供的问题，给出详细解答。" + 
                    "如果问题是编程相关的，请用" + 
                    (StringUtils.isNotEmpty(apiKey1.getCodeLanguage()) ? apiKey1.getCodeLanguage() : "Java") + 
                    "语言编写代码。如果问题是非编程问题，请提供清晰、准确、有条理的解答。");

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "请解答以下问题：" +
                "问题描述: " + problem +
                "\n\n生成一个解决方案，格式如下：" +
                "{" +
                " \"thoughts\": [\"分析问题，列出解决思路的要点\"]," +
                " \"code\": \"如果是编程问题，在这里提供代码实现，并包含注释；如果是选择题，在这里提供简单说明和具体答案；如果是简答题，在这里给出详细的答案\"," +
                " \"time_complexity\": \"如果是算法问题，说明时间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"," +
                " \"space_complexity\": \"如果是算法问题，说明空间复杂度；如果不适用，可以写'不适用'或提供其他相关分析\"" +
                "}" +
                "\n\n格式要求：" +
                "1. 在code字段中使用实际的换行符，且必须是字符串" +
                "2. 如果是编程问题，请正确缩进代码，使用空格" +
                "3. 如果是编程问题，请包含清晰的代码注释" +
                "4. 无论问题类型，输出必须是纯JSON格式，不要包含任何Markdown标记(如```json)" +
                "5. 确保thoughts字段包含问题分析的关键点" +
                "6. 对于非编程问题，可以在code字段中提供详细内容，或在thoughts字段中提供详细解答");
        Map<String, Object> requestBody = new HashMap<>();

        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");


        requestBody.put("model", "deepseek-reasoner");
        requestBody.put("messages", new Object[]{message, userMessage});

        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);

        String response = restTemplate.postForObject(apiUrl, requestEntity, String.class);

        // 去除 Markdown 格式
        response = removeMarkdownFormatting(response);

        long end = System.currentTimeMillis();
        // 记录usage，次数记录
        if (Objects.nonNull(response)) {
            usageRecordService.recordUsage(key, "deepseek-reasoner", problem, imageUrl, response, request, end-start, 5);
        }
        return response;
    }

    private String removeMarkdownFormatting(String response) {
        if (response == null) {
            return null;
        }
        return response.replaceAll("```json", "").replaceAll("```", "");
    }


}