package com.icoder.service;

import com.icoder.model.Order;
import com.icoder.model.User;
import com.icoder.repository.OrderRepository;
import com.icoder.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class OrderService {

    private final OrderRepository orderRepository;
    private final UserRepository userRepository;
    private final EmailService emailService;
    private final QRCodeService qrCodeService;

    @Value("${app.admin.email:<EMAIL>}")
    private String adminEmail;

    @Value("${app.base.url:http://localhost:3000}")
    private String baseUrl;

    @Autowired
    public OrderService(OrderRepository orderRepository,
                       UserRepository userRepository,
                       EmailService emailService,
                       QRCodeService qrCodeService) {
        this.orderRepository = orderRepository;
        this.userRepository = userRepository;
        this.emailService = emailService;
        this.qrCodeService = qrCodeService;
    }

    @Transactional
    public Order createOrder(String userEmail, String planName, BigDecimal amount, 
                           Integer credits, String paymentMethod, String description) {
        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + userEmail));

        Order order = new Order();
        order.setOrderNumber(generateOrderNumber());
        order.setUser(user);
        order.setPlanName(planName);
        order.setAmount(amount);
        order.setCredits(credits);
        order.setPaymentMethod(paymentMethod);
        order.setStatus("PENDING");
        order.setDescription(description);
        order.setExpiredAt(LocalDateTime.now().plusHours(2)); // 2小时后过期
        order.setAdminConfirmToken(UUID.randomUUID().toString());

        // 生成二维码数据
        String qrCodeData = generateQRCodeData(order);
        order.setQrCodeData(qrCodeData);

        Order savedOrder = orderRepository.save(order);

        // 发送邮件通知管理员
        sendAdminNotificationEmail(savedOrder);

        return savedOrder;
    }

    private String generateOrderNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomSuffix = UUID.randomUUID().toString().substring(0, 6).toUpperCase();
        return "ORD" + timestamp + randomSuffix;
    }

    private String generateQRCodeData(Order order) {
        // 这里应该集成真实的微信支付API来生成二维码
        // 目前返回一个模拟的二维码数据
        return qrCodeService.generateWeChatPayQRCode(order.getOrderNumber(), order.getAmount());
    }

    private void sendAdminNotificationEmail(Order order) {
        String subject = "新订单通知 - " + order.getOrderNumber();
        String confirmUrl = baseUrl + "/api/orders/confirm/" + order.getAdminConfirmToken();
        
        String content = String.format(
            "您好，管理员！\n\n" +
            "有新的订单需要处理：\n\n" +
            "订单号：%s\n" +
            "用户邮箱：%s\n" +
            "套餐名称：%s\n" +
            "金额：￥%.2f\n" +
            "积分：%d\n" +
            "支付方式：%s\n" +
            "创建时间：%s\n\n" +
            "请点击以下链接确认支付并为用户添加积分：\n" +
            "%s\n\n" +
            "此链接将在订单过期后失效。\n\n" +
            "系统自动发送，请勿回复。",
            order.getOrderNumber(),
            order.getUser().getEmail(),
            order.getPlanName(),
            order.getAmount(),
            order.getCredits(),
            order.getPaymentMethod(),
            order.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            confirmUrl
        );

        emailService.sendEmail(adminEmail, subject, content);
    }

    public Optional<Order> findByOrderNumber(String orderNumber) {
        return orderRepository.findByOrderNumber(orderNumber);
    }

    public List<Order> getUserOrders(String userEmail) {
        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + userEmail));
        return orderRepository.findByUserOrderByCreatedAtDesc(user);
    }

    @Transactional
    public boolean confirmPayment(String adminConfirmToken) {
        Optional<Order> orderOpt = orderRepository.findByAdminConfirmToken(adminConfirmToken);
        if (orderOpt.isPresent()) {
            Order order = orderOpt.get();
            if ("PENDING".equals(order.getStatus()) && 
                order.getExpiredAt().isAfter(LocalDateTime.now())) {
                
                // 更新订单状态
                order.setStatus("PAID");
                order.setPaidAt(LocalDateTime.now());
                orderRepository.save(order);

                // 为用户添加积分
                User user = order.getUser();
                user.setTotalCalls(user.getTotalCalls() + order.getCredits());
                user.setRemainingCalls(user.getRemainingCalls() + order.getCredits());
                userRepository.save(user);

                return true;
            }
        }
        return false;
    }

    @Transactional
    public void expireOldOrders() {
        List<Order> expiredOrders = orderRepository.findByStatusAndExpiredAtBefore("PENDING", LocalDateTime.now());
        for (Order order : expiredOrders) {
            order.setStatus("EXPIRED");
            orderRepository.save(order);
        }
    }
}
