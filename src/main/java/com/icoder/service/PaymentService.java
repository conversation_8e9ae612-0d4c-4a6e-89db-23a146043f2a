package com.icoder.service;

import com.icoder.dto.PaymentRequestDto;
import com.icoder.dto.PaymentResponseDto;
import com.icoder.model.ApiKey;
import com.icoder.model.PaymentRecord;
import com.icoder.model.User;
import com.icoder.repository.ApiKeyRepository;
import com.icoder.repository.PaymentRecordRepository;
import com.icoder.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Service
public class PaymentService {

    private final PaymentRecordRepository paymentRecordRepository;
    private final UserRepository userRepository;
    private final ApiKeyRepository apiKeyRepository;
    private final ApiKeyService apiKeyService;

    @Value("${payment.wechat.appId}")
    private String wechatAppId;

    @Value("${payment.wechat.mchId}")
    private String wechatMchId;

    @Value("${payment.wechat.mchKey}")
    private String wechatMchKey;

    @Value("${payment.wechat.notifyUrl}")
    private String wechatNotifyUrl;

    @Value("${payment.alipay.appId}")
    private String alipayAppId;

    @Value("${payment.alipay.privateKey}")
    private String alipayPrivateKey;

    @Value("${payment.alipay.publicKey}")
    private String alipayPublicKey;

    @Value("${payment.alipay.notifyUrl}")
    private String alipayNotifyUrl;

    @Value("${payment.alipay.returnUrl}")
    private String alipayReturnUrl;

    @Autowired
    public PaymentService(PaymentRecordRepository paymentRecordRepository,
                         UserRepository userRepository,
                         ApiKeyRepository apiKeyRepository,
                         ApiKeyService apiKeyService) {
        this.paymentRecordRepository = paymentRecordRepository;
        this.userRepository = userRepository;
        this.apiKeyRepository = apiKeyRepository;
        this.apiKeyService = apiKeyService;
    }

    @Transactional
    public PaymentResponseDto createPayment(String email, PaymentRequestDto paymentRequest) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        ApiKey apiKey = apiKeyRepository.findById(paymentRequest.getApiKeyId())
                .orElseThrow(() -> new RuntimeException("API key not found with id: " + paymentRequest.getApiKeyId()));

        if (!apiKey.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("API key does not belong to the user");
        }

        // Calculate calls to add based on amount
        int callsToAdd = calculateCallsToAdd(paymentRequest.getAmount());

        // Create payment record
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setUser(user);
        paymentRecord.setApiKey(apiKey);
        paymentRecord.setPaymentMethod(paymentRequest.getPaymentMethod());
        paymentRecord.setTransactionId(generateTransactionId());
        paymentRecord.setAmount(paymentRequest.getAmount());
        paymentRecord.setCallsAdded(callsToAdd);
        paymentRecord.setStatus("PENDING");
        paymentRecordRepository.save(paymentRecord);

        // Generate payment URL or QR code
        PaymentResponseDto response = new PaymentResponseDto();
        response.setTransactionId(paymentRecord.getTransactionId());
        response.setStatus("PENDING");

        if ("WECHAT".equals(paymentRequest.getPaymentMethod())) {
            // Implement WeChat payment integration
            // This is a simplified example - in a real application, you would use the WeChat Pay SDK
            response.setPaymentUrl("https://example.com/wechat-pay?transaction=" + paymentRecord.getTransactionId());
            response.setQrCode("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==");
        } else if ("ALIPAY".equals(paymentRequest.getPaymentMethod())) {
            // Implement Alipay integration
            // This is a simplified example - in a real application, you would use the Alipay SDK
            response.setPaymentUrl("https://example.com/alipay?transaction=" + paymentRecord.getTransactionId());
        } else {
            throw new RuntimeException("Unsupported payment method: " + paymentRequest.getPaymentMethod());
        }

        return response;
    }

    private String generateTransactionId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private int calculateCallsToAdd(BigDecimal amount) {
        // Simple calculation: 1 unit of currency = 10 API calls
        return amount.multiply(BigDecimal.valueOf(10)).intValue();
    }

    @Transactional
    public void processPaymentCallback(String transactionId, String status) {
        PaymentRecord paymentRecord = paymentRecordRepository.findByTransactionId(transactionId)
                .orElseThrow(() -> new RuntimeException("Payment record not found with transaction ID: " + transactionId));

        if ("SUCCESS".equals(status)) {
            paymentRecord.setStatus("COMPLETED");
            paymentRecordRepository.save(paymentRecord);

            // Add calls to the API key
            apiKeyService.addCallsToApiKey(paymentRecord.getApiKey().getId(), paymentRecord.getCallsAdded());
        } else if ("FAIL".equals(status)) {
            paymentRecord.setStatus("FAILED");
            paymentRecordRepository.save(paymentRecord);
        }
    }

    public List<PaymentRecord> getUserPayments(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));
        
        return paymentRecordRepository.findByUser(user);
    }
} 