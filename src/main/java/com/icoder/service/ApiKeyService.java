package com.icoder.service;

import com.icoder.dto.ApiKeyDto;
import com.icoder.model.ApiKey;
import com.icoder.model.User;
import com.icoder.repository.ApiKeyRepository;
import com.icoder.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class ApiKeyService {

    private final ApiKeyRepository apiKeyRepository;
    private final UserRepository userRepository;

    @Value("${apikey.default.expiration.days:365}")
    private int defaultExpirationDays;

    @Value("${apikey.default.calls:100}")
    private int defaultCalls;

    @Autowired
    public ApiKeyService(ApiKeyRepository apiKeyRepository, UserRepository userRepository) {
        this.apiKeyRepository = apiKeyRepository;
        this.userRepository = userRepository;
    }

    @Transactional
    public ApiKey createApiKey(String email, String keyName) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        ApiKey apiKey = new ApiKey();
        apiKey.setKeyValue(generateApiKey());
        apiKey.setKeyName(keyName != null ? keyName : "默认API Key");
        apiKey.setRemainingCalls(defaultCalls);
        apiKey.setTotalCalls(defaultCalls);
        apiKey.setActive(true);
        apiKey.setCreatedAt(LocalDateTime.now());
        apiKey.setExpiresAt(LocalDateTime.now().plusDays(defaultExpirationDays));
        apiKey.setUser(user);

        return apiKeyRepository.save(apiKey);
    }

    @Transactional
    public ApiKey createApiKey(String email) {
        return createApiKey(email, null);
    }

    @Transactional
    public ApiKey createApiKeyWithCredits(String email, String keyName, Integer remainingCalls) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        // 检查用户是否有剩余积分
        if (user.getRemainingCalls() <= 0) {
            throw new RuntimeException("剩余积分不足，请先充值");
        }

        // 校验积分
        if (remainingCalls == null) {
            remainingCalls = user.getRemainingCalls(); // 默认为用户所有剩余积分
        }

        if (remainingCalls <= 0) {
            throw new RuntimeException("分配积分必须大于0");
        }

        if (remainingCalls > user.getRemainingCalls()) {
            throw new RuntimeException("积分不能超过用户剩余积分，当前剩余: " + user.getRemainingCalls());
        }

        ApiKey apiKey = new ApiKey();
        apiKey.setKeyValue(generateApiKey());
        apiKey.setKeyName(keyName != null && !keyName.trim().isEmpty() ? keyName.trim() : "默认API Key");
        apiKey.setRemainingCalls(remainingCalls);
        apiKey.setTotalCalls(remainingCalls);
        apiKey.setActive(true);
        apiKey.setCreatedAt(LocalDateTime.now());
        apiKey.setExpiresAt(LocalDateTime.now().plusDays(defaultExpirationDays));
        apiKey.setUser(user);

        // 扣除用户积分
        user.setRemainingCalls(user.getRemainingCalls() - remainingCalls);
        userRepository.save(user);

        return apiKeyRepository.save(apiKey);
    }

    private String generateApiKey() {
        return "sk-" + UUID.randomUUID().toString().replace("-", "");
    }

    public List<ApiKeyDto> getUserApiKeys(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        return apiKeyRepository.findByUser(user).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }


    public ApiKey getApiKey(String keyValue) {
        return apiKeyRepository.findByKeyValue(keyValue)
                .orElseThrow(() -> new RuntimeException("API key not found with key: " + keyValue));
    }

    public ApiKeyDto convertToDto(ApiKey apiKey) {
        ApiKeyDto dto = new ApiKeyDto();
        dto.setId(apiKey.getId());
        dto.setKeyValue(apiKey.getKeyValue());
        dto.setKeyName(apiKey.getKeyName());
        dto.setRemainingCalls(apiKey.getRemainingCalls());
        dto.setTotalCalls(apiKey.getTotalCalls());
        dto.setActive(apiKey.isActive());
        dto.setCreatedAt(apiKey.getCreatedAt());
        dto.setExpiresAt(apiKey.getExpiresAt());
        dto.setUserId(apiKey.getUser().getId());
        dto.setVoiceEnabled(apiKey.isVoiceEnabled());
        dto.setVoiceAppId(apiKey.getVoiceAppId());
        dto.setVoiceAccessKeyId(apiKey.getVoiceAccessKeyId());
        return dto;
    }

    @Transactional
    public boolean validateAndDecrementApiKey(String keyValue) {
        Optional<ApiKey> apiKeyOpt = apiKeyRepository.findByKeyValue(keyValue);
        if (apiKeyOpt.isPresent()) {
            ApiKey apiKey = apiKeyOpt.get();
            if (apiKey.isActive() && apiKey.getRemainingCalls() > 0 && 
                    (apiKey.getExpiresAt() == null || apiKey.getExpiresAt().isAfter(LocalDateTime.now()))) {
                apiKey.setRemainingCalls(apiKey.getRemainingCalls() - 1);
                apiKeyRepository.save(apiKey);
                return true;
            }
        }
        return false;
    }

    @Transactional
    public ApiKey updateApiKey(Long id, Integer remainingCalls, Boolean active) {
        ApiKey apiKey = apiKeyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("API key not found with id: " + id));

        if (remainingCalls != null) {
            apiKey.setRemainingCalls(remainingCalls);
            if (remainingCalls > apiKey.getTotalCalls()) {
                apiKey.setTotalCalls(remainingCalls);
            }
        }

        if (active != null) {
            apiKey.setActive(active);
        }

        return apiKeyRepository.save(apiKey);
    }

    @Transactional
    public ApiKey addCallsToApiKey(Long id, int callsToAdd) {
        ApiKey apiKey = apiKeyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("API key not found with id: " + id));

        apiKey.setRemainingCalls(apiKey.getRemainingCalls() + callsToAdd);
        apiKey.setTotalCalls(apiKey.getTotalCalls() + callsToAdd);

        return apiKeyRepository.save(apiKey);
    }

    @Transactional
    public ApiKey updateApiKeyWithUserCredits(Long id, String keyName, Integer creditsChange, Boolean active,
                                            Boolean voiceEnabled, String voiceAppId, String voiceAccessKeyId, String userEmail) {
        ApiKey apiKey = apiKeyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("API Key not found"));

        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // 更新key名称
        if (keyName != null && !keyName.trim().isEmpty()) {
            apiKey.setKeyName(keyName.trim());
        }

        // 处理积分变化并与用户积分联动
        if (creditsChange != null && creditsChange != 0) {
            int newKeyCredits = apiKey.getRemainingCalls() + creditsChange;
            if (newKeyCredits < 0) {
                throw new RuntimeException("API Key积分不能为负数");
            }

            // 检查用户剩余积分是否足够（如果是增加key积分）
            if (creditsChange > 0 && user.getRemainingCalls() < creditsChange) {
                throw new RuntimeException("用户剩余积分不足");
            }

            // 更新key积分
            apiKey.setRemainingCalls(newKeyCredits);

            // 更新用户积分（key增加积分时，用户积分减少；key减少积分时，用户积分增加）
            user.setRemainingCalls(user.getRemainingCalls() - creditsChange);
            userRepository.save(user);
        }

        // 更新启用状态
        if (active != null) {
            apiKey.setActive(active);
        }

        // 更新语音功能
        if (voiceEnabled != null) {
            apiKey.setVoiceEnabled(voiceEnabled);
        }
        if (voiceAppId != null) {
            apiKey.setVoiceAppId(voiceAppId.trim().isEmpty() ? null : voiceAppId.trim());
        }
        if (voiceAccessKeyId != null) {
            apiKey.setVoiceAccessKeyId(voiceAccessKeyId.trim().isEmpty() ? null : voiceAccessKeyId.trim());
        }

        return apiKeyRepository.save(apiKey);
    }

    @Transactional
    public ApiKey updateApiKeyWithDirectCredits(Long id, String keyName, Integer newRemainingCalls, Boolean active,
                                              Boolean voiceEnabled, String voiceAppId, String voiceAccessKeyId, String userEmail) {
        ApiKey apiKey = apiKeyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("API Key not found"));

        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // 更新key名称
        if (keyName != null && !keyName.trim().isEmpty()) {
            apiKey.setKeyName(keyName.trim());
        }

        // 处理积分直接设置并与用户积分联动
        if (newRemainingCalls != null) {
            // 校验新积分不能为负数
            if (newRemainingCalls < 0) {
                throw new RuntimeException("API Key积分不能为负数");
            }

            // 计算积分变化
            int creditsChange = newRemainingCalls - apiKey.getRemainingCalls();

            // 如果是增加积分，检查用户剩余积分是否足够
            if (creditsChange > 0 && user.getRemainingCalls() < creditsChange) {
                throw new RuntimeException("用户剩余积分不足，当前剩余: " + user.getRemainingCalls() + "，需要: " + creditsChange);
            }

            // 更新key积分
            apiKey.setRemainingCalls(newRemainingCalls);

            // 更新用户积分（key增加积分时，用户积分减少；key减少积分时，用户积分增加）
            user.setRemainingCalls(user.getRemainingCalls() - creditsChange);
            userRepository.save(user);
        }

        // 更新启用状态
        if (active != null) {
            apiKey.setActive(active);
        }

        // 更新语音功能
        if (voiceEnabled != null) {
            apiKey.setVoiceEnabled(voiceEnabled);
        }
        if (voiceAppId != null) {
            apiKey.setVoiceAppId(voiceAppId.trim().isEmpty() ? null : voiceAppId.trim());
        }
        if (voiceAccessKeyId != null) {
            apiKey.setVoiceAccessKeyId(voiceAccessKeyId.trim().isEmpty() ? null : voiceAccessKeyId.trim());
        }

        return apiKeyRepository.save(apiKey);
    }

    /**
     * Checks if a user is the owner of an API key
     */
    public boolean isApiKeyOwner(Long apiKeyId, Long userId) {
        Optional<ApiKey> apiKeyOpt = apiKeyRepository.findById(apiKeyId);
        if (apiKeyOpt.isPresent()) {
            return apiKeyOpt.get().getUser().getId().equals(userId);
        }
        return false;
    }

    /**
     * Checks if an API key is valid without decrementing usage
     */
    public ApiKey isApiKeyValid(String keyValue) {
        Optional<ApiKey> apiKeyOpt = apiKeyRepository.findByKeyValue(keyValue);
        if (apiKeyOpt.isPresent()) {
            ApiKey apiKey = apiKeyOpt.get();
            if (apiKey.isActive() && apiKey.getRemainingCalls() > 4 &&
                    (apiKey.getExpiresAt() == null || apiKey.getExpiresAt().isAfter(LocalDateTime.now()))) {
                return apiKey;
            }
        }
        return null;
    }

    /**
     * Get all API keys (admin function)
     */
    public List<ApiKeyDto> getAllApiKeys() {
        return apiKeyRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get user by email
     */
    public User getUserByEmail(String email) {
        return userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));
    }

    /**
     * Enable voice feature for API key
     */
    @Transactional
    public ApiKey enableVoiceFeature(Long id, String voiceAppId, String voiceAccessKeyId, String userEmail) {
        ApiKey apiKey = apiKeyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("API Key not found"));

        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // 校验必填字段
        if (voiceAppId == null || voiceAppId.trim().isEmpty()) {
            throw new RuntimeException("语音AppId不能为空");
        }
        if (voiceAccessKeyId == null || voiceAccessKeyId.trim().isEmpty()) {
            throw new RuntimeException("语音AccessKeyId不能为空");
        }

        // 校验用户总积分
        if (user.getTotalCalls() <= 125) {
            throw new RuntimeException("累计充值需大于100才能开通语音功能");
        }

        // 开通语音功能
        apiKey.setVoiceEnabled(true);
        apiKey.setVoiceAppId(voiceAppId.trim());
        apiKey.setVoiceAccessKeyId(voiceAccessKeyId.trim());

        return apiKeyRepository.save(apiKey);
    }
} 