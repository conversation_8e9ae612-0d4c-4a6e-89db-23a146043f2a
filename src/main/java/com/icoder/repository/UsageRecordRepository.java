package com.icoder.repository;

import com.icoder.model.ApiKey;
import com.icoder.model.UsageRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface UsageRecordRepository extends JpaRepository<UsageRecord, Long> {
    List<UsageRecord> findByApiKey(ApiKey apiKey);

    @Query("SELECT ur FROM UsageRecord ur JOIN ur.apiKey ak JOIN ak.user u WHERE u.id = :userId")
    List<UsageRecord> findByUserId(Long userId);

    List<UsageRecord> findByApiKeyAndUsedAtBetween(ApiKey apiKey, LocalDateTime start, LocalDateTime end);

    // 分页查询方法
    Page<UsageRecord> findByApiKey_User_IdOrderByUsedAtDesc(Long userId, Pageable pageable);

    Page<UsageRecord> findByApiKey_IdAndApiKey_User_IdOrderByUsedAtDesc(Long apiKeyId, Long userId, Pageable pageable);

    Page<UsageRecord> findAllByOrderByUsedAtDesc(Pageable pageable);

    // 计算用户总调用数
    @Query("SELECT COUNT(ur) FROM UsageRecord ur JOIN ur.apiKey ak JOIN ak.user u WHERE u.id = :userId")
    Long countByUserId(Long userId);
}