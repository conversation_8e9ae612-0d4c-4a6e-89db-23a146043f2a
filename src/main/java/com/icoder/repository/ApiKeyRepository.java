package com.icoder.repository;

import com.icoder.model.ApiKey;
import com.icoder.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ApiKeyRepository extends JpaRepository<ApiKey, Long> {
    List<ApiKey> findByUser(User user);
    Optional<ApiKey> findByKeyValue(String keyValue);
    List<ApiKey> findByUserAndActive(User user, boolean active);
} 