package com.icoder.repository;

import com.icoder.model.Order;
import com.icoder.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    Optional<Order> findByOrderNumber(String orderNumber);
    Optional<Order> findByAdminConfirmToken(String adminConfirmToken);
    List<Order> findByUser(User user);
    List<Order> findByUserOrderByCreatedAtDesc(User user);
    List<Order> findByStatus(String status);
    List<Order> findByStatusAndExpiredAtBefore(String status, LocalDateTime expiredAt);
    Optional<Order> findByTransactionId(String transactionId);
}
