package com.icoder.repository;

import com.icoder.model.PaymentRecord;
import com.icoder.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentRecordRepository extends JpaRepository<PaymentRecord, Long> {
    List<PaymentRecord> findByUser(User user);
    Optional<PaymentRecord> findByTransactionId(String transactionId);
} 