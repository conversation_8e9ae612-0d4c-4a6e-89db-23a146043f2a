package com.icoder.security;

import com.icoder.enums.ModelEnum;
import com.icoder.model.ApiKey;
import com.icoder.service.ApiKeyService;
import com.icoder.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;

@Component
public class ApiKeyAuthFilter extends OncePerRequestFilter {

    @Autowired
    private ApiKeyService apiKeyService;

    @Autowired
    private UserService userService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String apiKey = request.getHeader("Authorization");
        String servletPath = request.getServletPath();
        logger.info("start auth key: " + apiKey);

        if (apiKey != null && servletPath.contains("codeMoss")) {
            // 根据不同的请求方法进行特定的积分验证
            ApiKey apiKeyValid = validateApiKeyForEndpoint(apiKey, servletPath, request);

            if (Objects.nonNull(apiKeyValid)) {
                UserDetails userDetails = userService.loadUserByUsername(apiKeyValid.getUser().getUsername());
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                SecurityContextHolder.getContext().setAuthentication(authentication);
            } else {
                // 如果验证失败，返回401错误
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("{\"code\": 1, \"message\": \"API Key无效或积分不足\"}");
                response.setContentType("application/json;charset=UTF-8");
                return;
            }
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 根据不同的端点验证API Key和积分
     */
    private ApiKey validateApiKeyForEndpoint(String apiKeyValue, String servletPath, HttpServletRequest request) {
        try {
            ApiKey apiKey = apiKeyService.getApiKey(apiKeyValue);

            // 基础验证：检查key是否激活和未过期
            if (!apiKey.isActive() ||
                (apiKey.getExpiresAt() != null && apiKey.getExpiresAt().isBefore(LocalDateTime.now()))) {
                return null;
            }

            // 根据不同的请求方法进行特定的积分验证
            if (servletPath.contains("generateSolutionByModelV6") || servletPath.contains("recognizeAndGenerateSolutionByModelV6")) {
                return validateForModelEndpoint(apiKey, request);
            } else if (servletPath.contains("processAudio")) {
                return validateForAudioEndpoint(apiKey);
            } else {
                // 其他端点使用原有的验证逻辑（剩余积分 > 4）
                return apiKey.getRemainingCalls() > 4 ? apiKey : null;
            }

        } catch (RuntimeException e) {
            // key 不存在时，getApiKey会抛出RuntimeException
            return null;
        }
    }

    /**
     * 验证模型相关端点的积分要求
     */
    private ApiKey validateForModelEndpoint(ApiKey apiKey, HttpServletRequest request) {
        String modelParam = request.getParameter("model");
        if (modelParam == null || modelParam.isEmpty()) {
            return null;
        }

        try {
            ModelEnum modelEnum = ModelEnum.getModelByAlias(modelParam);
            Integer requiredCredits = modelEnum.getCost();

            // 检查剩余积分是否满足该模型的消耗积分
            if (apiKey.getRemainingCalls() >= requiredCredits) {
                return apiKey;
            }

            return null;
        } catch (Exception e) {
            // 如果获取模型信息失败，返回null
            return null;
        }
    }

    /**
     * 验证音频处理端点的积分要求
     */
    private ApiKey validateForAudioEndpoint(ApiKey apiKey) {
        // processAudio 端点要求剩余积分至少1个
        return apiKey.getRemainingCalls() >= 1 ? apiKey : null;
    }
} 