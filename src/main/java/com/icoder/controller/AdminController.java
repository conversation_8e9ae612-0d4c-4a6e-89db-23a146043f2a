//package com.icoder.controller;
//
//import com.icoder.dto.ApiKeyDto;
//import com.icoder.dto.UpdateApiKeyDto;
//import com.icoder.dto.UsageRecordDto;
//import com.icoder.dto.UserRegistrationDto;
//import com.icoder.model.ApiKey;
//import com.icoder.model.User;
//import com.icoder.service.ApiKeyService;
//import com.icoder.service.UsageRecordService;
//import com.icoder.service.UserService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
//@RestController
//@RequestMapping("/admin")
////@PreAuthorize("hasRole('ADMIN')")
//public class AdminController {
//
//    private final UserService userService;
//    private final ApiKeyService apiKeyService;
//    private final UsageRecordService usageRecordService;
//
//    private final PasswordEncoder encoder;
//
//    @Autowired
//    public AdminController(UserService userService,
//                           ApiKeyService apiKeyService,
//                           PasswordEncoder encoder,
//                           UsageRecordService usageRecordService) {
//        this.userService = userService;
//        this.apiKeyService = apiKeyService;
//        this.encoder = encoder;
//        this.usageRecordService = usageRecordService;
//    }
//
//    @GetMapping("/users")
//    public ResponseEntity<List<User>> getAllUsers() {
//        List<User> users = userService.getAllUsers();
//        return ResponseEntity.ok(users);
//    }
//    @PostMapping("/users")
//    public ResponseEntity<User> createUser(@RequestBody UserRegistrationDto userRegistrationDto) {
//        User user = new User();
//        user.setEmail(userRegistrationDto.getEmail());
//        user.setFullName(userRegistrationDto.getFullName());
//        user.setInviteCode(userRegistrationDto.getInviteCode());
//        user.setPassword(encoder.encode(userRegistrationDto.getPassword()));
//
//        Set<String> roles = new HashSet<>();
//        roles.add("USER");
//        user.setRoles(roles);
//
//        User registeredUser = userService.registerUser(user, userRegistrationDto.getInviteCode());
//        return ResponseEntity.ok(registeredUser);
//    }
//
//    @GetMapping("/users/{userId}/keys")
//    public ResponseEntity<List<ApiKeyDto>> getUserApiKeys(@PathVariable Long userId) {
//        User user = userService.getUserById(userId);
//        List<ApiKeyDto> apiKeys = apiKeyService.getUserApiKeys(user.getEmail());
//        return ResponseEntity.ok(apiKeys);
//    }
//
//    @GetMapping("/users/{userId}/usage")
//    public ResponseEntity<List<UsageRecordDto>> getUserUsageRecords(@PathVariable Long userId) {
//        List<UsageRecordDto> usageRecords = usageRecordService.findByUserId(userId);
//        return ResponseEntity.ok(usageRecords);
//    }
//
//    @PutMapping("/keys/{id}")
//    public ResponseEntity<ApiKeyDto> updateApiKey(@PathVariable Long id,
//                                                @RequestBody UpdateApiKeyDto updateApiKeyDto) {
//        ApiKey apiKey = apiKeyService.updateApiKey(id, updateApiKeyDto.getRemainingCalls(), updateApiKeyDto.getActive());
//        return ResponseEntity.ok(apiKeyService.convertToDto(apiKey));
//    }
//
//    @PostMapping("/users/{userId}/keys")
//    public ResponseEntity<ApiKeyDto> createApiKeyForUser(@PathVariable Long userId) {
//        User user = userService.getUserById(userId);
//        ApiKey apiKey = apiKeyService.createApiKey(user.getEmail());
//        return ResponseEntity.ok(apiKeyService.convertToDto(apiKey));
//    }
//
//    @PutMapping("/users/{userId}/role")
//    public ResponseEntity<?> updateUserRole(@PathVariable Long userId, @RequestParam String role) {
//        userService.updateUserRole(userId, role);
//        return ResponseEntity.ok("User role updated successfully");
//    }
//}
