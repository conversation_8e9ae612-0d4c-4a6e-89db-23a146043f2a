package com.icoder.controller;

import com.icoder.dto.UpdateUsageRecordDto;
import com.icoder.dto.UsageRecordDto;
import com.icoder.model.UsageRecord;
import com.icoder.model.User;
import com.icoder.service.ApiKeyService;
import com.icoder.service.UsageRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/usage")
public class UsageRecordController {

    private final UsageRecordService usageRecordService;
    private final ApiKeyService apiKeyService;

    @Autowired
    public UsageRecordController(UsageRecordService usageRecordService, ApiKeyService apiKeyService) {
        this.usageRecordService = usageRecordService;
        this.apiKeyService = apiKeyService;
    }

    /**
     * 获取当前用户的调用记录
     * @param authentication 认证信息
     * @param apiKeyId 可选的API Key ID筛选
     * @param page 页码，从0开始
     * @param size 每页大小
     * @param sortBy 排序字段，默认为usedAt
     * @param sortDir 排序方向，默认为desc
     * @return 调用记录列表
     */
    @GetMapping
    public ResponseEntity<?> getUserUsageRecords(Authentication authentication,
                                                @RequestParam(required = false) Long apiKeyId,
                                                @RequestParam(defaultValue = "0") int page,
                                                @RequestParam(defaultValue = "20") int size,
                                                @RequestParam(defaultValue = "usedAt") String sortBy,
                                                @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            User user = (User) authentication.getPrincipal();

            // 创建分页和排序对象
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));

            // 获取调用记录
            Page<UsageRecordDto> usageRecords;
            if (apiKeyId != null) {
                // 按API Key筛选
                usageRecords = usageRecordService.getUserUsageRecordsByApiKey(user.getEmail(), apiKeyId, pageable);
            } else {
                // 获取所有记录
                usageRecords = usageRecordService.getUserUsageRecords(user.getEmail(), pageable);
            }

            // 获取用户总调用数
            Long totalCallCount = usageRecordService.getUserTotalCallCount(user.getEmail());

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "获取调用记录成功");

            Map<String, Object> data = new HashMap<>();
            data.put("records", usageRecords.getContent());
            data.put("totalElements", usageRecords.getTotalElements());
            data.put("totalPages", usageRecords.getTotalPages());
            data.put("currentPage", usageRecords.getNumber());
            data.put("pageSize", usageRecords.getSize());
            data.put("hasNext", usageRecords.hasNext());
            data.put("hasPrevious", usageRecords.hasPrevious());

            // 添加用户总调用数信息
            data.put("totalCallCount", totalCallCount);

            response.put("data", data);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "获取调用记录失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    /**
     * 更新调用记录的备注
     */
    @PutMapping("/{id}/notes")
    public ResponseEntity<?> updateUsageRecordNotes(@PathVariable Long id,
                                                   @RequestBody UpdateUsageRecordDto updateDto,
                                                   Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();

            // Check if user is admin or the owner of the usage record
            boolean isAdmin = user.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ADMIN"));

            if (!isAdmin && !usageRecordService.isUsageRecordOwner(id, user.getId())) {
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "无权限操作此调用记录");
                return ResponseEntity.status(403).body(response);
            }

            UsageRecord usageRecord = usageRecordService.updateUsageRecordNotes(id, updateDto.getNotes());
            UsageRecordDto usageRecordDto = usageRecordService.convertToDto(usageRecord);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "更新备注成功");
            response.put("data", usageRecordDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "更新备注失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    // Admin endpoints
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllUsageRecords(@RequestParam(defaultValue = "0") int page,
                                               @RequestParam(defaultValue = "20") int size,
                                               @RequestParam(defaultValue = "usedAt") String sortBy,
                                               @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            // 创建分页和排序对象
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));

            Page<UsageRecordDto> usageRecords = usageRecordService.getAllUsageRecords(pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "获取所有调用记录成功");

            Map<String, Object> data = new HashMap<>();
            data.put("records", usageRecords.getContent());
            data.put("totalElements", usageRecords.getTotalElements());
            data.put("totalPages", usageRecords.getTotalPages());
            data.put("currentPage", usageRecords.getNumber());
            data.put("pageSize", usageRecords.getSize());
            data.put("hasNext", usageRecords.hasNext());
            data.put("hasPrevious", usageRecords.hasPrevious());

            response.put("data", data);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "获取调用记录失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PutMapping("/admin/{id}/notes")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> adminUpdateUsageRecordNotes(@PathVariable Long id,
                                                        @RequestBody UpdateUsageRecordDto updateDto) {
        try {
            UsageRecord usageRecord = usageRecordService.updateUsageRecordNotes(id, updateDto.getNotes());
            UsageRecordDto usageRecordDto = usageRecordService.convertToDto(usageRecord);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "更新备注成功");
            response.put("data", usageRecordDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "更新备注失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
