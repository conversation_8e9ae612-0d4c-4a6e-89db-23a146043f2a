package com.icoder.controller;

import com.icoder.dto.ApiKeyDto;
import com.icoder.dto.UpdateApiKeyDto;
import com.icoder.dto.UpdateApiKeyRequest;
import com.icoder.dto.CreateApiKeyRequest;
import com.icoder.dto.VoiceEnableRequest;
import com.icoder.model.ApiKey;
import com.icoder.model.User;
import com.icoder.service.ApiKeyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/keys")
public class ApiKeyController {

    private final ApiKeyService apiKeyService;

    @Autowired
    public ApiKeyController(ApiKeyService apiKeyService) {
        this.apiKeyService = apiKeyService;
    }

    /**
     * 获取当前用户的所有API Keys
     */
    @GetMapping
    public ResponseEntity<?> getUserApiKeys(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<ApiKeyDto> apiKeys = apiKeyService.getUserApiKeys(user.getEmail());

            // 重新从数据库获取最新的用户信息
            User latestUser = apiKeyService.getUserByEmail(user.getEmail());

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "获取API Keys成功");
            response.put("data", apiKeys);

            // 添加用户积分信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("remainingCalls", latestUser.getRemainingCalls());
            userInfo.put("totalCalls", latestUser.getTotalCalls());
            response.put("userInfo", userInfo);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "获取API Keys失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 创建新的API Key
     */
    @PostMapping
    public ResponseEntity<?> createApiKey(Authentication authentication,
                                         @RequestBody(required = false) CreateApiKeyRequest request) {
        try {
            User user = (User) authentication.getPrincipal();
            String keyName = null;
            Integer remainingCalls = null;

            if (request != null) {
                keyName = request.getKeyName();
                remainingCalls = request.getRemainingCalls();
            }

            ApiKey apiKey = apiKeyService.createApiKeyWithCredits(user.getEmail(), keyName, remainingCalls);
            ApiKeyDto apiKeyDto = apiKeyService.convertToDto(apiKey);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "API Key创建成功");
            response.put("data", apiKeyDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "创建API Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 更新API Key
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateApiKey(@PathVariable Long id,
                                         @RequestBody UpdateApiKeyDto updateApiKeyDto,
                                         Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();

            // Check if user is admin or the owner of the API key
            boolean isAdmin = user.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ADMIN"));

            if (!isAdmin && !apiKeyService.isApiKeyOwner(id, user.getId())) {
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "无权限操作此API Key");
                return ResponseEntity.status(403).body(response);
            }

            ApiKey apiKey = apiKeyService.updateApiKey(id, updateApiKeyDto.getRemainingCalls(), updateApiKeyDto.getActive());
            ApiKeyDto apiKeyDto = apiKeyService.convertToDto(apiKey);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "API Key更新成功");
            response.put("data", apiKeyDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "更新API Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 更新API Key（支持积分联动）
     */
    @PutMapping("/{id}/advanced")
    public ResponseEntity<?> updateApiKeyAdvanced(@PathVariable Long id,
                                                 @RequestBody UpdateApiKeyRequest request,
                                                 Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();

            // 检查权限
            if (!apiKeyService.isApiKeyOwner(id, user.getId())) {
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "无权限操作此API Key");
                return ResponseEntity.status(403).body(response);
            }

            ApiKey apiKey = apiKeyService.updateApiKeyWithDirectCredits(
                id,
                request.getKeyName(),
                request.getRemainingCalls(),
                request.getActive(),
                request.getVoiceEnabled(),
                request.getVoiceAppId(),
                request.getVoiceAccessKeyId(),
                user.getEmail()
            );

            ApiKeyDto apiKeyDto = apiKeyService.convertToDto(apiKey);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "API Key更新成功");
            response.put("data", apiKeyDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "更新API Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 删除/停用API Key
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deactivateApiKey(@PathVariable Long id, Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();

            // Check if user is admin or the owner of the API key
            boolean isAdmin = user.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ADMIN"));

            if (!isAdmin && !apiKeyService.isApiKeyOwner(id, user.getId())) {
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "无权限操作此API Key");
                return ResponseEntity.status(403).body(response);
            }

            apiKeyService.updateApiKey(id, null, false);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "API Key删除成功");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "删除API Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    // Admin endpoints
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllApiKeys() {
        try {
            List<ApiKeyDto> apiKeys = apiKeyService.getAllApiKeys();

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "获取所有API Keys成功");
            response.put("data", apiKeys);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "获取API Keys失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PutMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> adminUpdateApiKey(@PathVariable Long id,
                                              @RequestBody UpdateApiKeyDto updateApiKeyDto) {
        try {
            ApiKey apiKey = apiKeyService.updateApiKey(id, updateApiKeyDto.getRemainingCalls(), updateApiKeyDto.getActive());
            ApiKeyDto apiKeyDto = apiKeyService.convertToDto(apiKey);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "API Key更新成功");
            response.put("data", apiKeyDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "更新API Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @DeleteMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> adminDeactivateApiKey(@PathVariable Long id) {
        try {
            apiKeyService.updateApiKey(id, null, false);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "API Key删除成功");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "删除API Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 开通语音功能
     */
    @PutMapping("/{id}/voice/enable")
    public ResponseEntity<?> enableVoice(@PathVariable Long id,
                                        @RequestBody VoiceEnableRequest request,
                                        Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();

            // 检查权限
            if (!apiKeyService.isApiKeyOwner(id, user.getId())) {
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "无权限操作此API Key");
                return ResponseEntity.status(403).body(response);
            }

            // 检查用户总积分是否大于125
            if (user.getTotalCalls() <= 125) {
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "累计充值需大于100才能开通语音功能");
                return ResponseEntity.status(400).body(response);
            }

            ApiKey apiKey = apiKeyService.enableVoiceFeature(
                id,
                request.getVoiceAppId(),
                request.getVoiceAccessKeyId(),
                user.getEmail()
            );

            ApiKeyDto apiKeyDto = apiKeyService.convertToDto(apiKey);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "语音功能开通成功");
            response.put("data", apiKeyDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "开通语音功能失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
