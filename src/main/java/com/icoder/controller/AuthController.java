package com.icoder.controller;

import com.icoder.dto.EmailVerificationRequest;
import com.icoder.dto.JwtResponse;
import com.icoder.dto.LoginRequest;
import com.icoder.dto.RegisterRequest;
import com.icoder.model.User;
import com.icoder.security.JwtUtils;
import com.icoder.service.ApiKeyService;
import com.icoder.service.UserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/auth")
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final UserService userService;
    private final ApiKeyService apiKeyService;
    private final PasswordEncoder encoder;
    private final JwtUtils jwtUtils;

    @Autowired
    public AuthController(AuthenticationManager authenticationManager,
                         UserService userService,
                         ApiKeyService apiKeyService,
                         PasswordEncoder encoder,
                         JwtUtils jwtUtils) {
        this.authenticationManager = authenticationManager;
        this.userService = userService;
        this.apiKeyService = apiKeyService;
        this.encoder = encoder;
        this.jwtUtils = jwtUtils;
    }

    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getEmail(), loginRequest.getPassword()));

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = jwtUtils.generateJwtToken(authentication);
        
        User userDetails = (User) authentication.getPrincipal();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(item -> item.getAuthority())
                .collect(Collectors.toList());

        // Update last login time
        userService.updateLastLogin(userDetails.getEmail());

        return ResponseEntity.ok(new JwtResponse(
                jwt,
                "Bearer",
                userDetails.getId(),
                userDetails.getEmail(),
                userDetails.getFullName(),
                roles));
    }

    @PostMapping("/register")
    public ResponseEntity<?> registerUser(@Valid @RequestBody RegisterRequest registerRequest) {
        // Create new user
        User user = new User();
        user.setEmail(registerRequest.getEmail());
        user.setPassword(encoder.encode(registerRequest.getPassword()));
        user.setFullName(registerRequest.getFullName());
        
        // Set default role to USER
        Set<String> roles = new HashSet<>();
        roles.add("USER");
        user.setRoles(roles);

        // Verify email code if provided
        if (registerRequest.getVerificationCode() != null && !registerRequest.getVerificationCode().isEmpty()) {
            boolean isVerified = userService.verifyEmailCode(registerRequest.getEmail(), registerRequest.getVerificationCode());
            if (!isVerified) {
                return ResponseEntity.badRequest().body("Invalid verification code");
            }
        } else {
            return ResponseEntity.badRequest().body("Verification code is required");
        }

        // Process invite code if provided
        User savedUser = userService.registerUser(user, registerRequest.getInviteCode());
        
        // Create default API key for the user
        apiKeyService.createApiKey(savedUser.getEmail());

        return ResponseEntity.ok("User registered successfully!");
    }

    @PostMapping("/send-verification")
    public ResponseEntity<?> sendVerificationEmail(@Valid @RequestBody EmailVerificationRequest request) {
        userService.sendVerificationEmail(request.getEmail());
        return ResponseEntity.ok("Verification email sent successfully");
    }

    @PostMapping("/verify-email")
    public ResponseEntity<?> verifyEmail(@RequestParam("email") String email, 
                                        @RequestParam("code") String code) {
        boolean isVerified = userService.verifyEmailCode(email, code);
        if (isVerified) {
            return ResponseEntity.ok("Email verified successfully");
        } else {
            return ResponseEntity.badRequest().body("Invalid verification code");
        }
    }

    /**
     * 获取当前登录用户的详细信息
     * @param authentication 当前认证信息
     * @return 用户详细信息
     */
    @GetMapping("/userInfo")
    public ResponseEntity<?> getUserInfo(Authentication authentication) {
        if (authentication == null) {
            return ResponseEntity.status(401).body("User not authenticated");
        }

        User user = (User) authentication.getPrincipal();

        // 构建用户信息响应
        Map<String, Object> response = new HashMap<>();
        response.put("code", 0); // 成功状态码

        Map<String, Object> data = new HashMap<>();
        data.put("id", user.getId());
        data.put("email", user.getEmail());
        data.put("nickname", user.getFullName());
        data.put("userPic", ""); // 默认为空，可以根据需要添加头像URL
        data.put("roles", user.getRoles());
        data.put("enabled", user.isEnabled());
        data.put("createdAt", user.getCreatedAt());
        data.put("lastLogin", user.getLastLogin());
        data.put("remainingCalls", user.getRemainingCalls());
        data.put("totalCalls", user.getTotalCalls());

        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    /**
     * 修改用户密码
     * @param passwordData 包含旧密码和新密码的请求体
     * @param authentication 当前认证信息
     * @return 修改结果
     */
    @PutMapping("/updatePwd")
    public ResponseEntity<?> updatePassword(@RequestBody Map<String, String> passwordData,
                                            Authentication authentication) {
        if (authentication == null) {
            return ResponseEntity.status(401).body("User not authenticated");
        }

        User user = (User) authentication.getPrincipal();
        String oldPassword = passwordData.get("old");
        String newPassword = passwordData.get("new");
        String confirmPassword = passwordData.get("confirm");

        // 验证请求参数
        if (oldPassword == null || newPassword == null || confirmPassword == null) {
            return ResponseEntity.badRequest().body("Missing required fields");
        }

        // 验证新密码和确认密码是否一致
        if (!newPassword.equals(confirmPassword)) {
            return ResponseEntity.badRequest().body("New password and confirm password do not match");
        }

        // 验证旧密码是否正确
        if (!encoder.matches(oldPassword, user.getPassword())) {
            return ResponseEntity.badRequest().body("Old password is incorrect");
        }

        // 更新密码
        try {
            userService.updatePassword(user.getEmail(), newPassword);

            // 构建成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "Password updated successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Failed to update password: " + e.getMessage());
        }
    }

    /**
     * 发送邮件验证码（用于登录）
     */
    @PostMapping("/send-code")
    public ResponseEntity<?> sendLoginCode(@Valid @RequestBody EmailVerificationRequest request) {
        try {
            // 验证邮箱格式
            String email = request.getEmail();
            if (email == null || email.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("message", "邮箱地址不能为空");
                response.put("code", 1);
                return ResponseEntity.badRequest().body(response);
            }

            // 发送验证码
            userService.sendVerificationEmail(email);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "验证码已发送到您的邮箱");
            response.put("code", 0);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("message", "发送验证码失败: " + e.getMessage());
            response.put("code", 1);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 邮件验证码登录
     */
    @PostMapping("/verify-login")
    public ResponseEntity<?> verifyLogin(@RequestBody Map<String, String> request) {
        try {
            String email = request.get("email");
            String code = request.get("code");

            if (email == null || email.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("message", "邮箱地址不能为空");
                response.put("code", 1);
                return ResponseEntity.badRequest().body(response);
            }

            if (code == null || code.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("message", "验证码不能为空");
                response.put("code", 1);
                return ResponseEntity.badRequest().body(response);
            }

            // 验证验证码
            boolean isValid = userService.verifyEmailCode(email, code);
            if (!isValid) {
                Map<String, Object> response = new HashMap<>();
                response.put("message", "验证码无效或已过期");
                response.put("code", 1);
                return ResponseEntity.badRequest().body(response);
            }

            // 获取或创建用户
            User user = (User) userService.loadUserByUsername(email);

            // 生成JWT Token
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                user, null, user.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtUtils.generateJwtToken(authentication);

            // 更新最后登录时间
            userService.updateLastLogin(email);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("message", "登录成功");
            response.put("code", 0);
            response.put("token", jwt);

            Map<String, Object> userData = new HashMap<>();
            userData.put("id", user.getId());
            userData.put("email", user.getEmail());
            userData.put("name", user.getFullName());
            userData.put("roles", user.getRoles());
            userData.put("enabled", user.isEnabled());
            userData.put("createdAt", user.getCreatedAt());
            userData.put("lastLogin", user.getLastLogin());
            userData.put("remainingCalls", user.getRemainingCalls());
            userData.put("totalCalls", user.getTotalCalls());

            response.put("user", userData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("message", "登录失败: " + e.getMessage());
            response.put("code", 1);
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 用户退出登录
     * @param authentication 当前认证信息
     * @return 退出结果
     */
    @PostMapping("/logout")
    public ResponseEntity<?> logout(Authentication authentication) {
        try {
            if (authentication != null) {
                User user = (User) authentication.getPrincipal();
                // 记录退出登录日志
                System.out.println("User " + user.getEmail() + " logged out at " + new Date());

                // 清除当前的安全上下文
                SecurityContextHolder.clearContext();
            }

            // 构建成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("message", "退出登录成功");
            response.put("code", 0);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("message", "退出登录失败: " + e.getMessage());
            response.put("code", 1);
            return ResponseEntity.status(500).body(response);
        }
    }
}
