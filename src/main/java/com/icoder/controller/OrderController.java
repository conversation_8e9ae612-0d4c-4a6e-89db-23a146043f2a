package com.icoder.controller;

import com.icoder.dto.CreateOrderRequest;
import com.icoder.dto.OrderDto;
import com.icoder.model.Order;
import com.icoder.model.User;
import com.icoder.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/orders")
public class OrderController {

    private final OrderService orderService;

    @Autowired
    public OrderController(OrderService orderService) {
        this.orderService = orderService;
    }

    /**
     * 创建订单
     */
    @PostMapping("/create")
    public ResponseEntity<?> createOrder(@Valid @RequestBody CreateOrderRequest request,
                                       Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            
            Order order = orderService.createOrder(
                user.getEmail(),
                request.getPlanName(),
                request.getAmount(),
                request.getCredits(),
                request.getPaymentMethod(),
                request.getDescription()
            );

            OrderDto orderDto = convertToDto(order);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "订单创建成功");
            response.put("data", orderDto);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "订单创建失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 查询订单状态
     */
    @GetMapping("/{orderNumber}/status")
    public ResponseEntity<?> getOrderStatus(@PathVariable String orderNumber,
                                          Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Optional<Order> orderOpt = orderService.findByOrderNumber(orderNumber);
            
            if (orderOpt.isPresent()) {
                Order order = orderOpt.get();
                // 验证订单属于当前用户
                if (!order.getUser().getId().equals(user.getId())) {
                    Map<String, Object> response = new HashMap<>();
                    response.put("code", 1);
                    response.put("message", "无权访问此订单");
                    return ResponseEntity.status(403).body(response);
                }

                Map<String, Object> response = new HashMap<>();
                response.put("code", 0);
                response.put("message", "查询成功");
                response.put("data", Map.of(
                    "orderNumber", order.getOrderNumber(),
                    "status", order.getStatus(),
                    "amount", order.getAmount(),
                    "credits", order.getCredits(),
                    "createdAt", order.getCreatedAt(),
                    "paidAt", order.getPaidAt(),
                    "expiredAt", order.getExpiredAt()
                ));

                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("code", 1);
                response.put("message", "订单不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/my")
    public ResponseEntity<?> getUserOrders(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<Order> orders = orderService.getUserOrders(user.getEmail());
            
            List<OrderDto> orderDtos = orders.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "查询成功");
            response.put("data", orderDtos);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 管理员确认支付（通过邮件链接访问）
     */
    @GetMapping("/confirm/{token}")
    public ResponseEntity<String> confirmPayment(@PathVariable String token) {
        try {
            boolean success = orderService.confirmPayment(token);
            if (success) {
                return ResponseEntity.ok(
                    "<html><body>" +
                    "<h2>支付确认成功</h2>" +
                    "<p>订单已确认支付，用户积分已添加。</p>" +
                    "<p>您可以关闭此页面。</p>" +
                    "</body></html>"
                );
            } else {
                return ResponseEntity.badRequest().body(
                    "<html><body>" +
                    "<h2>支付确认失败</h2>" +
                    "<p>订单不存在、已过期或已处理。</p>" +
                    "</body></html>"
                );
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(
                "<html><body>" +
                "<h2>支付确认失败</h2>" +
                "<p>处理过程中发生错误: " + e.getMessage() + "</p>" +
                "</body></html>"
            );
        }
    }

    private OrderDto convertToDto(Order order) {
        OrderDto dto = new OrderDto();
        dto.setId(order.getId());
        dto.setOrderNumber(order.getOrderNumber());
        dto.setPlanName(order.getPlanName());
        dto.setAmount(order.getAmount());
        dto.setCredits(order.getCredits());
        dto.setPaymentMethod(order.getPaymentMethod());
        dto.setStatus(order.getStatus());
        dto.setCreatedAt(order.getCreatedAt());
        dto.setPaidAt(order.getPaidAt());
        dto.setExpiredAt(order.getExpiredAt());
        dto.setQrCodeData(order.getQrCodeData());
        dto.setPaymentUrl(order.getPaymentUrl());
        dto.setDescription(order.getDescription());
        return dto;
    }
}
