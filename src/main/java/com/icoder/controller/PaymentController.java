package com.icoder.controller;

import com.icoder.dto.PaymentRequestDto;
import com.icoder.dto.PaymentResponseDto;
import com.icoder.model.PaymentRecord;
import com.icoder.model.User;
import com.icoder.service.ApiKeyService;
import com.icoder.service.PaymentService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/payment")
public class PaymentController {

    private final PaymentService paymentService;
    private final ApiKeyService apiKeyService;

    @Autowired
    public PaymentController(PaymentService paymentService, ApiKeyService apiKeyService) {
        this.paymentService = paymentService;
        this.apiKeyService = apiKeyService;
    }

    @PostMapping("/create")
    public ResponseEntity<PaymentResponseDto> createPayment(@Valid @RequestBody PaymentRequestDto paymentRequest,
                                                          Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        
        // Verify that the API key belongs to the user
        if (!apiKeyService.isApiKeyOwner(paymentRequest.getApiKeyId(), user.getId())) {
            return ResponseEntity.status(403).build();
        }
        
        PaymentResponseDto response = paymentService.createPayment(user.getEmail(), paymentRequest);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/history")
    public ResponseEntity<List<PaymentRecord>> getPaymentHistory(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<PaymentRecord> payments = paymentService.getUserPayments(user.getEmail());
        return ResponseEntity.ok(payments);
    }

    // Payment notification endpoints (called by payment providers)
    @PostMapping("/wechat/notify")
    public ResponseEntity<?> wechatPaymentNotification(@RequestBody Map<String, String> notification) {
        // Validate the notification signature
        // Process the payment notification
        String transactionId = notification.get("out_trade_no");
        String status = "SUCCESS".equals(notification.get("result_code")) ? "SUCCESS" : "FAIL";
        
        paymentService.processPaymentCallback(transactionId, status);
        
        return ResponseEntity.ok().build();
    }

    @PostMapping("/alipay/notify")
    public ResponseEntity<?> alipayPaymentNotification(@RequestBody Map<String, String> notification) {
        // Validate the notification signature
        // Process the payment notification
        String transactionId = notification.get("out_trade_no");
        String status = "TRADE_SUCCESS".equals(notification.get("trade_status")) ? "SUCCESS" : "FAIL";
        
        paymentService.processPaymentCallback(transactionId, status);
        
        return ResponseEntity.ok("success");
    }
}
