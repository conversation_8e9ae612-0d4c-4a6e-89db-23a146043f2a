package com.icoder.controller;

import com.icoder.model.User;
import com.icoder.service.ApiKeyService;
import com.icoder.service.UsageRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    private final ApiKeyService apiKeyService;
    private final UsageRecordService usageRecordService;

    @Autowired
    public DashboardController(ApiKeyService apiKeyService, UsageRecordService usageRecordService) {
        this.apiKeyService = apiKeyService;
        this.usageRecordService = usageRecordService;
    }

    /**
     * 获取用户仪表板数据
     * @param authentication 认证信息
     * @return 仪表板数据，包括积分信息、API Key数量、调用统计等
     */
    @GetMapping("/stats")
    public ResponseEntity<?> getDashboardStats(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            
            // 获取用户的API Key数量
            int apiKeyCount = apiKeyService.getUserApiKeys(user.getEmail()).size();
            
            // 获取用户的总调用数
            Long totalCallCount = usageRecordService.getUserTotalCallCount(user.getEmail());
            
            // 构建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("message", "获取仪表板数据成功");
            
            Map<String, Object> data = new HashMap<>();
            
            // 用户基本信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("email", user.getEmail());
            userInfo.put("name", user.getFullName());
            userInfo.put("remainingCalls", user.getRemainingCalls());
            userInfo.put("totalCalls", user.getTotalCalls());
            userInfo.put("lastLogin", user.getLastLogin());
            data.put("userInfo", userInfo);
            
            // 统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("remainingCalls", user.getRemainingCalls());
            stats.put("totalCalls", user.getTotalCalls());
            stats.put("apiKeyCount", apiKeyCount);
            stats.put("totalCallCount", totalCallCount);
            stats.put("usedCalls", (user.getTotalCalls() - user.getRemainingCalls()));
            data.put("stats", stats);
            
            response.put("data", data);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 1);
            response.put("message", "获取仪表板数据失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
