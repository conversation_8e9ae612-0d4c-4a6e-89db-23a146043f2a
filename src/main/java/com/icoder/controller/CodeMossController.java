package com.icoder.controller;


import com.icoder.dto.ConfigResponse;
import com.icoder.enums.ModelEnum;
import com.icoder.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@RestController
@RequestMapping("/codeMoss")
public class CodeMossController {

    private final ExecutorService executorService = Executors.newCachedThreadPool();

    private final OssService ossService;

    private final AIService aiService;

    public CodeMossController(AIService aiService, OssService ossService) {
        this.aiService = aiService;
        this.ossService = ossService;

    }

    @GetMapping("/generateSolutionByModel")
    public ResponseEntity<String> generateSolutionByText(
            @RequestParam(value = "problem") String problem,
            @RequestParam(value = "model") String model,
            @RequestParam(value = "codeLanguage", required = false) String codeLanguage,
            HttpServletRequest request) {
        String apiKey = request.getHeader("Authorization");
        ModelEnum modelEnum = ModelEnum.getModelByAlias(model);
        String deepseekResponse = aiService.generateSolutionByModel(problem, modelEnum.getModelName(), modelEnum.getCost(), "", apiKey, codeLanguage, request);
        return new ResponseEntity<>(deepseekResponse, HttpStatus.OK);
    }

    @PostMapping("/recognizeAndGenerateSolutionByModel")
    public ResponseEntity<String> recognizeAndGenerateSolutionByModel(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "model") String model,
            @RequestParam(value = "codeLanguage", required = false) String codeLanguage,
            HttpServletRequest request) {
        try {
            String apiKey = request.getHeader("Authorization");

            List<String> imageUrls = ossService.uploadFiles(files, getLastEightChars( apiKey));

            String response = aiService.recognizeAndGenerateSolutionByModel(imageUrls, model, apiKey, codeLanguage, request);
            if (StringUtils.isEmpty(response)) {
                return ResponseEntity.badRequest().body("Error during OCR recognition: " + "识别结果为空");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body("Error during OCR recognition: " + e.getMessage());
        }
    }

    @GetMapping("/generateSolutionByModelV6")
    public ResponseEntity<String> generateSolutionByModelV6(
            @RequestParam(value = "problem") String problem,
            @RequestParam(value = "model") String model,
            @RequestParam(value = "codeLanguage", required = false) String codeLanguage,
            HttpServletRequest request) {
        String apiKey = request.getHeader("Authorization");
        ModelEnum modelEnum = ModelEnum.getModelByAlias(model);
        try {
            String deepseekResponse = aiService.generateSolutionByModel(problem, modelEnum.getModelName(), modelEnum.getCost(), "", apiKey, codeLanguage, request);
            return ResponseEntity.ok(encrypt(deepseekResponse));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body("Error during OCR recognition: " + e.getMessage());
        }
    }

    @PostMapping("/recognizeAndGenerateSolutionByModelV6")
    public ResponseEntity<String> recognizeAndGenerateSolutionByModelV6(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "model") String model,
            @RequestParam(value = "codeLanguage", required = false) String codeLanguage,
            HttpServletRequest request) {
        try {
            String apiKey = request.getHeader("Authorization");

            List<String> imageUrls = ossService.uploadFiles(files, getLastEightChars( apiKey));

            String response = aiService.recognizeAndGenerateSolutionByModel(imageUrls, model, apiKey, codeLanguage, request);
            if (StringUtils.isEmpty(response)) {
                return ResponseEntity.badRequest().body("Error during OCR recognition: " + "识别结果为空");
            }
            return ResponseEntity.ok(encrypt(response));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body("Error during OCR recognition: " + e.getMessage());
        }
    }


    // AES 加密方法
    private String encrypt(String data) throws Exception {
        String secretKey = "3257871232342431"; // 16-byte key for AES-128
        SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }



    @GetMapping("/initConfig")
    public ResponseEntity<ConfigResponse> initConfig(HttpServletRequest request) {
        String apiKey = request.getHeader("Authorization");

        ConfigResponse config = aiService.initConfig(apiKey);

        return ResponseEntity.ok(config);
    }

    @PostMapping(value = "/processAudio", produces = MediaType.TEXT_EVENT_STREAM_VALUE + ";charset=UTF-8")
    public SseEmitter processAudio(@RequestParam("problem") String problem, @RequestParam("isAccurate") Boolean isAccurate, HttpServletRequest request) {
        System.out.println("processAudio: " + problem);
        SseEmitter emitter = new SseEmitter(180000L);
        String apiKey = request.getHeader("Authorization");
        executorService.execute(() -> {
            try {
                aiService.processAudioStream(problem, apiKey, isAccurate, chunk -> {
                    try {
                        emitter.send(SseEmitter.event()
                                .name("message")
                                .data(chunk));
                    } catch (IOException e) {
                        System.err.println("发送流式数据时出错: " + e.getMessage());
                        emitter.completeWithError(e);
                    }
                }).thenAccept(fullResponse -> {
                    emitter.complete();
                }).exceptionally(ex -> {
                    emitter.completeWithError(ex);
                    return null;
                });

            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }
    
    @GetMapping(value = "/processAudio", produces = MediaType.TEXT_EVENT_STREAM_VALUE + ";charset=UTF-8")
    public SseEmitter processAudioGet(@RequestParam("problem") String problem, 
                                  @RequestParam(value = "isAccurate", defaultValue = "true") Boolean isAccurate,
                                  @RequestParam(value = "apiKey", required = false) String apiKeyParam,
                                  HttpServletRequest request) {
        SseEmitter emitter = new SseEmitter(180000L);
        // 优先从请求头获取API Key，如果没有则从URL参数获取
        String apiKey = request.getHeader("Authorization");
        if (apiKey == null || apiKey.isEmpty()) {
            apiKey = apiKeyParam;
        }

        if (apiKey == null || apiKey.isEmpty()) {
            emitter.completeWithError(new RuntimeException("API Key不能为空"));
            return emitter;
        }

        String finalApiKey = apiKey;
        executorService.execute(() -> {
            try {
                aiService.processAudioStream(problem, finalApiKey, isAccurate, chunk -> {
                    try {
                        emitter.send(SseEmitter.event()
                                .name("message")
                                .data(chunk));
                    } catch (IOException e) {
                        System.err.println("发送流式数据时出错(GET): " + e.getMessage());
                        emitter.completeWithError(e);
                    }
                }).thenAccept(fullResponse -> {
                    emitter.complete();
                }).exceptionally(ex -> {
                    emitter.completeWithError(ex);
                    return null;
                });

            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }


    public static String getLastEightChars(String str) {
        if (str == null || str.length() <= 8) {
            return str;
        }
        return str.substring(str.length() - 8);
    }



    private String saveFile(MultipartFile file) throws IOException {
        String tempFilePath = System.getProperty("java.io.tmpdir") + "/" + file.getOriginalFilename();
        file.transferTo(new java.io.File(tempFilePath));
        return tempFilePath;
    }

    private void deleteFile(String filePath) {
        new java.io.File(filePath).delete();
    }

}
