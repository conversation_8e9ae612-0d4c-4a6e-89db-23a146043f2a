package com.icoder.enums;

import java.util.Objects;

public enum ModelEnum {
    DOUBAO_SEED_1_6_FLASH("doubao-seed-1-6-flash-250615", "doubao-seed", "火山引擎", true, 2, false),
    DOUBAO_1_5("doubao-1-5-vision-pro-32k-250115",  "doubao-pro", "火山引擎", true, 4, false),
    DOUBAO_1_5_THINKING("doubao-1-5-thinking-pro-250415", "doubao-thinking", "火山引擎", false, 4, false),
    DEEPSEEK_V3_250324("deepseek-v3-250324", "deepseek-v3", "火山引擎", false, 5, false),
    DEEPSEEK_R1_250120("deepseek-r1-250528", "deepseek-r1", "火山引擎", false, 5, false),
    CLAUDE_3_7_SONNET("anthropic/claude-sonnet-4", "claude", "openrouter", false, 8, true),
//    CLAUDE_3_7_SONNET_THINKING("anthropic/claude-3.7-sonnet:thinking", "claude-3.7-thinking", "openrouter", false, 8, true),
    GEMINI_2_5_FLASH_PREVIEW("google/gemini-2.5-flash-preview", "gemini-2.5", "openrouter", false, 6, true),
//    GEMINI_2_5_FLASH_PREVIEW_THINKING("google/gemini-2.5-flash-preview:thinking", "gemini", "openrouter", false, 6, true)
    GROK_4("x-ai/grok-4", "grok-4", "openrouter", false, 8, true)
    ;

    String modelName;
    String modelAlias;
    String modelSource;

    Boolean picture;

    Integer cost;

    // 是否是高级模型
    Boolean advanced;


    ModelEnum(String modelName, String modelAlias, String modelSource, Boolean picture, Integer cost, Boolean advanced) {
        this.modelName = modelName;
        this.modelAlias = modelAlias;
        this.modelSource = modelSource;
        this.picture = picture;
        this.cost = cost;
        this.advanced = advanced;
    }

    public String getModelName() {
        return modelName;
    }

    public Boolean isPicture() {
        return picture;
    }

    public Integer getCost() {
        return cost;
    }

    public static ModelEnum getModelByAlias(String modelAlias) {
        for (ModelEnum modelEnum : ModelEnum.values()) {
            if (Objects.equals(modelEnum.modelAlias, modelAlias)) {
                return modelEnum;
            }
        }
        return ModelEnum.DEEPSEEK_V3_250324;
    }


    public static String[] getAllNormalModelAlias() {
        return new String[]{
//                ModelEnum.DOUBAO_SEED_1_6_FLASH.modelAlias,
                ModelEnum.DOUBAO_1_5.modelAlias,
                ModelEnum.DOUBAO_1_5_THINKING.modelAlias,
                ModelEnum.DEEPSEEK_V3_250324.modelAlias,
                ModelEnum.DEEPSEEK_R1_250120.modelAlias
        };
    }

    // 获取所有模型
    public static String[] getAllModelAlias() {
        return new String[]{
//                ModelEnum.DOUBAO_SEED_1_6_FLASH.modelAlias,
                ModelEnum.DOUBAO_1_5.modelAlias,
                ModelEnum.DOUBAO_1_5_THINKING.modelAlias,
                ModelEnum.DEEPSEEK_V3_250324.modelAlias,
                ModelEnum.DEEPSEEK_R1_250120.modelAlias,
                ModelEnum.GEMINI_2_5_FLASH_PREVIEW.modelAlias,
                ModelEnum.CLAUDE_3_7_SONNET.modelAlias,
                ModelEnum.GROK_4.modelAlias
//                ModelEnum.CLAUDE_3_7_SONNET_THINKING.modelAlias,
//                ModelEnum.GEMINI_2_5_FLASH_PREVIEW_THINKING.modelAlias
        };
    }

}
