package com.icoder.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderDto {
    private Long id;
    private String orderNumber;
    private String planName;
    private BigDecimal amount;
    private Integer credits;
    private String paymentMethod;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime paidAt;
    private LocalDateTime expiredAt;
    private String qrCodeData;
    private String paymentUrl;
    private String description;
}
