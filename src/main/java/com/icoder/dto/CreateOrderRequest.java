package com.icoder.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

@Data
public class CreateOrderRequest {
    @NotBlank(message = "套餐名称不能为空")
    private String planName;

    @NotNull(message = "金额不能为空")
    @Positive(message = "金额必须大于0")
    private BigDecimal amount;

    @NotNull(message = "积分数量不能为空")
    @Positive(message = "积分数量必须大于0")
    private Integer credits;

    @NotBlank(message = "支付方式不能为空")
    private String paymentMethod; // "WECHAT", "ALIPAY"

    private String description;
}
