package com.icoder.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentRequestDto {
    @NotBlank(message = "Payment method is required")
    private String paymentMethod; // "WECHAT" or "ALIPAY"
    
    @NotNull(message = "Amount is required")
    @Min(value = 1, message = "Amount must be at least 1")
    private BigDecimal amount;
    
    @NotNull(message = "API key ID is required")
    private Long apiKeyId;
} 