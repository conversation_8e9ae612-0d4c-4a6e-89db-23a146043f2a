package com.icoder.scheduler;

import com.icoder.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class OrderScheduler {

    private static final Logger logger = LoggerFactory.getLogger(OrderScheduler.class);

    private final OrderService orderService;

    @Autowired
    public OrderScheduler(OrderService orderService) {
        this.orderService = orderService;
    }

    /**
     * 每10分钟清理一次过期订单
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600000毫秒
    public void cleanupExpiredOrders() {
        try {
            logger.info("开始清理过期订单...");
            orderService.expireOldOrders();
            logger.info("过期订单清理完成");
        } catch (Exception e) {
            logger.error("清理过期订单时发生错误", e);
        }
    }
}
