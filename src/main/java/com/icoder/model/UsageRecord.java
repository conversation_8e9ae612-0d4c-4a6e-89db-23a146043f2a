package com.icoder.model;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "usage_records")
public class UsageRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "api_key_id", nullable = false)
    private ApiKey apiKey;

    @Column(nullable = false)
    private LocalDateTime usedAt = LocalDateTime.now();

    @Column(nullable = false)
    private String endpoint;

    @Column(length = 1000)
    private String requestData;

    @Column(length = 1000)
    private String responseData;

    @Column
    private String ipAddress;

    @Column
    private String userAgent;

    @Column
    private String notes;

    @Column
    private String imageUrl;

    // 耗时
    @Column
    private Long duration;
} 