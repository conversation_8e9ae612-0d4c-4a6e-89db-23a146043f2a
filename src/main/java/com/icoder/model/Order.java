package com.icoder.model;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "orders")
public class Order {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String orderNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(nullable = false)
    private String planName;

    @Column(nullable = false)
    private BigDecimal amount;

    @Column(nullable = false)
    private Integer credits;

    @Column(nullable = false)
    private String paymentMethod; // "WECHAT", "ALIPAY"

    @Column(nullable = false)
    private String status; // "PENDING", "PAID", "CANCELLED", "EXPIRED"

    @Column(nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column
    private LocalDateTime paidAt;

    @Column
    private LocalDateTime expiredAt;

    @Column(length = 1000)
    private String qrCodeData; // 二维码数据

    @Column(length = 500)
    private String paymentUrl; // 支付链接

    @Column(length = 1000)
    private String description; // 订单描述

    @Column
    private String transactionId; // 第三方支付交易ID

    @Column
    private String adminConfirmToken; // 管理员确认令牌
}
