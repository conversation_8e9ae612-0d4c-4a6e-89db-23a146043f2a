# 购买功能实现说明

## 功能概述

本次实现了完整的购买功能，包括：

1. **订单创建** - 用户点击购买后创建订单
2. **邮件通知** - 自动发送邮件给管理员
3. **二维码展示** - 显示支付二维码
4. **状态轮询** - 前端轮询订单支付状态
5. **支付确认** - 管理员确认支付并添加积分

## 后端实现

### 新增的文件

1. **Order.java** - 订单实体类
2. **OrderRepository.java** - 订单数据访问层
3. **OrderService.java** - 订单业务逻辑
4. **OrderController.java** - 订单API控制器
5. **QRCodeService.java** - 二维码生成服务
6. **CreateOrderRequest.java** - 创建订单请求DTO
7. **OrderDto.java** - 订单响应DTO
8. **SchedulingConfig.java** - 定时任务配置
9. **OrderScheduler.java** - 订单清理定时任务

### API 端点

- `POST /api/orders/create` - 创建订单
- `GET /api/orders/{orderNumber}/status` - 查询订单状态
- `GET /api/orders/my` - 获取用户订单列表
- `GET /api/orders/confirm/{token}` - 管理员确认支付

### 数据库表

需要创建 `orders` 表：

```sql
CREATE TABLE `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_number` varchar(255) NOT NULL UNIQUE,
  `user_id` bigint NOT NULL,
  `plan_name` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `credits` int NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `status` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL,
  `paid_at` datetime DEFAULT NULL,
  `expired_at` datetime DEFAULT NULL,
  `qr_code_data` text,
  `payment_url` varchar(500),
  `description` text,
  `transaction_id` varchar(255),
  `admin_confirm_token` varchar(255),
  PRIMARY KEY (`id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_admin_confirm_token` (`admin_confirm_token`),
  CONSTRAINT `fk_orders_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
);
```

## 前端实现

### 新增的文件

1. **purchase-modal.tsx** - 购买弹窗组件
2. **purchase/page.tsx** - 购买功能测试页面

### 更新的文件

1. **recharge/page.tsx** - 充值页面集成购买弹窗
2. **api.ts** - 添加订单相关API接口

## 配置说明

### 后端配置 (application.properties)

```properties
# 管理员邮箱
app.admin.email=<EMAIL>
# 前端基础URL
app.base.url=http://localhost:3000
```

### 依赖添加

在 `pom.xml` 中添加了 ZXing 依赖用于生成二维码：

```xml
<dependency>
    <groupId>com.google.zxing</groupId>
    <artifactId>core</artifactId>
    <version>3.5.1</version>
</dependency>
<dependency>
    <groupId>com.google.zxing</groupId>
    <artifactId>javase</artifactId>
    <version>3.5.1</version>
</dependency>
```

## 测试流程

1. **启动后端服务**
   ```bash
   cd coder-moss-api-master
   mvn spring-boot:run
   ```

2. **启动前端服务**
   ```bash
   cd coder-portal-master
   npm run dev
   ```

3. **测试购买功能**
   - 访问 `http://localhost:3000/dashboard/recharge`
   - 点击任意套餐的"立即购买"按钮
   - 选择支付方式并确认购买
   - 查看生成的订单和二维码
   - 检查管理员邮箱是否收到通知邮件

4. **测试管理员确认**
   - 点击邮件中的确认链接
   - 验证用户积分是否增加
   - 验证订单状态是否更新为已支付

5. **测试轮询功能**
   - 在支付页面等待
   - 管理员确认支付后页面应自动跳转到成功页面

## 注意事项

1. **二维码生成** - 目前使用模拟数据，实际项目中需要集成真实的微信/支付宝支付API
2. **邮件配置** - 确保邮件服务配置正确
3. **定时任务** - 每10分钟自动清理过期订单
4. **安全性** - 管理员确认链接包含随机token，确保安全性
5. **错误处理** - 包含完整的错误处理和用户提示

## 后续优化建议

1. 集成真实的支付API（微信支付、支付宝）
2. 添加支付回调处理
3. 增加订单详情页面
4. 添加退款功能
5. 优化二维码样式和品牌信息
6. 添加支付超时处理
7. 增加订单统计和报表功能
