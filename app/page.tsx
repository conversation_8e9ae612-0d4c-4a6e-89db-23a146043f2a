"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  Check,
  ChevronRight,
  Menu,
  X,
  Moon,
  Sun,
  ArrowRight,
  Star,
  Zap,
  Shield,
  Users,
  BarChart,
  Layers,
  Keyboard,
  Bot,
  Code,
  ExternalLink,
  MessageSquareHeart,
  AlertCircle,
  Copy,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext } from "@/components/ui/carousel"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { useTheme } from "next-themes"
import { useLanguage } from "@/components/language-provider"
import { LanguageSwitcher } from "@/components/language-switcher"
import { FloatingQRCodes } from "@/components/floating-qr-codes"
import { Span } from "next/dist/trace"
import TestimonialsCarousel from "@/components/testimonials-carousel"

export default function LandingPage() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const { t } = useLanguage()
  const [activeDialog, setActiveDialog] = useState<string | null>(null)

  useEffect(() => {
    setMounted(true)
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  const features = [
    {
      title: t("smartAutomation"),
      description: t("smartAutomationDescription"),
      icon: <Shield className="size-5" />,
    },
    {
      title: t("advancedAnalytics"),
      description: t("advancedAnalyticsDescription"),
      icon: <Keyboard className="size-5" />,
    },
    {
      title: t("teamCollaboration"),
      description: t("teamCollaborationDescription"),
      icon: <Bot className="size-5" />,
    },
    {
      title: t("enterpriseSecurity"),
      description: t("enterpriseSecurityDescription"),
      icon: <Image className="size-5" />,
    },
    {
      title: t("seamlessIntegration"),
      description: t("seamlessIntegrationDescription"),
      icon: <Code className="size-5" />,
    },
    {
      title: t("support"),
      description: t("supportDescription"),
      icon: <Star className="size-5" />,
    },
  ]

  return (
    <div className="flex min-h-[100dvh] flex-col">
      {/* Floating QR codes on the right side */}
      <FloatingQRCodes />
      
      <header
        className={`sticky top-0 z-50 w-full backdrop-blur-lg transition-all duration-300 ${isScrolled ? "bg-background/80 shadow-sm" : "bg-transparent"}`}
      >
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-2 font-bold">
            <div className="size-8 rounded-lg bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center text-primary-foreground">
              C
            </div>
            <span>Offer Helper</span>
          </div>
          <nav className="hidden md:flex gap-8">
            <Link
              href="#features"
              className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              {t("features")}
            </Link>
            <Link
              href="#howItWorks"
              className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              {t("howItWorks")}
            </Link>
            <Link
              href="#testimonials"
              className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              {t("testimonials")}
            </Link>
            <Link
              href="#pricing"
              className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              {t("pricing")}
            </Link>
            <Link
              href="#faq"
              className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              FAQ
            </Link>
          </nav>
          <div className="hidden md:flex gap-4 items-center">
            {/* <LanguageSwitcher /> */}
            <Button variant="ghost" size="icon" onClick={toggleTheme} className="rounded-full">
              {mounted && theme === "dark" ? <Sun className="size-[18px]" /> : <Moon className="size-[18px]" />}
              <span className="sr-only">Toggle theme</span>
            </Button>
            <Link href="/login">
              <Button
                variant="ghost"
                className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground"
              >
                {t("login")}
              </Button>
            </Link>
            <Link
              href="#howItWorks"
            >
              <Button className="rounded-full text-base px-5 py-2">
                {t("getStarted")}
                <ChevronRight className="ml-1 size-4" />
              </Button>
            </Link>
          </div>
          <div className="flex items-center gap-4 md:hidden">
            <LanguageSwitcher />
            <Button variant="ghost" size="icon" onClick={toggleTheme} className="rounded-full">
              {mounted && theme === "dark" ? <Sun className="size-[18px]" /> : <Moon className="size-[18px]" />}
            </Button>
            <Button variant="ghost" size="icon" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
              {mobileMenuOpen ? <X className="size-5" /> : <Menu className="size-5" />}
              <span className="sr-only">Toggle menu</span>
            </Button>
          </div>
        </div>
        {/* Mobile menu */}
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden absolute top-16 inset-x-0 bg-background/95 backdrop-blur-lg border-b"
          >
            <div className="container py-4 flex flex-col gap-4">
              <Link href="#features" className="py-2 text-sm font-medium" onClick={() => setMobileMenuOpen(false)}>
                {t("features")}
              </Link>
              <Link href="#testimonials" className="py-2 text-sm font-medium" onClick={() => setMobileMenuOpen(false)}>
                {t("testimonials")}
              </Link>
              <Link href="#pricing" className="py-2 text-sm font-medium" onClick={() => setMobileMenuOpen(false)}>
                {t("pricing")}
              </Link>
              <Link href="#faq" className="py-2 text-sm font-medium" onClick={() => setMobileMenuOpen(false)}>
                FAQ
              </Link>
              <div className="flex flex-col gap-2 pt-2 border-t">
                <Link href="/login">
                  <Button
                    variant="ghost"
                    className="py-2 text-sm font-medium justify-start"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t("login")}
                  </Button>
                </Link>
                <Button className="rounded-full">
                  {t("getStarted")}
                  <ChevronRight className="ml-1 size-4" />
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </header>
      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-10 overflow-hidden">
          <div className="container px-4 md:px-6 relative">
            <div className="absolute inset-0 -z-10 h-full w-full bg-white dark:bg-black bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#1f1f1f_1px,transparent_1px),linear-gradient(to_bottom,#1f1f1f_1px,transparent_1px)] bg-[size:4rem_4rem] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center max-w-3xl mx-auto mb-12"
            >
              {/* <Badge className="mb-4 rounded-full px-4 py-1.5 text-sm font-medium" variant="secondary">
                {t("launchingSoon")}
              </Badge> */}
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70">
                {t("heroTitle")}
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                {t("heroSubtitle")}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg" 
                  className="rounded-full h-12 px-8 text-base"
                  onClick={() => {
                    document.getElementById('howItWorks')?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  {t("getStarted")}
                  <ArrowRight className="ml-2 size-4" />
                </Button>
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="rounded-full h-12 px-8 text-base"
                  onClick={() => {
                    document.getElementById('howItWorks')?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  {t("bookDemo")}
                </Button>
              </div>
              <div className="flex items-center justify-center gap-4 mt-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Check className="size-4 text-primary" />
                  <span>{t("noCreditCard")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Check className="size-4 text-primary" />
                  <span>{t("trialPeriod")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Check className="size-4 text-primary" />
                  <span>{t("model")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Check className="size-4 text-primary" />
                  <span>{t("cancelAnytime")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Check className="size-4 text-primary" />
                  <span>{t("production")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Check className="size-4 text-primary" />
                  <span>{t("allTypesOfQuestions")}</span>
                </div>
              </div>
            </motion.div>

            {/* Written Test Mode Video */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="relative mx-auto max-w-5xl mb-16"
            >
              <div className="text-center mb-6">
                <h3 className="text-2xl md:text-3xl font-bold mb-2 text-primary">{t("writtenTestMode")}</h3>
                <p className="text-muted-foreground max-w-2xl mx-auto">{t("writtenTestDescription")}</p>
              </div>
              <div className="rounded-xl overflow-hidden shadow-2xl bg-gradient-to-b from-background to-muted/20">
                <video
                  src="/portal.mp4"
                  width={1280}
                  height={720}
                  controls
                  autoPlay
                  muted
                  loop
                  className="w-full h-auto"
                  playsInline
                />
              </div>
              <div className="absolute -bottom-6 -right-6 -z-10 h-[300px] w-[300px] rounded-full bg-gradient-to-br from-primary/30 to-secondary/30 blur-3xl opacity-70"></div>
              <div className="absolute -top-6 -left-6 -z-10 h-[300px] w-[300px] rounded-full bg-gradient-to-br from-secondary/30 to-primary/30 blur-3xl opacity-70"></div>
            </motion.div>

            {/* Voice Mode Video */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
              className="relative mx-auto max-w-5xl"
            >
               <div className="text-center mb-6">
                <h3 className="text-2xl md:text-3xl font-bold mb-2 text-primary">{t("voiceMode")}</h3>
                <p className="text-muted-foreground max-w-2xl mx-auto">{t("voiceModeDescription")}</p>
              </div>
    
              <div className="rounded-xl overflow-hidden shadow-2xl bg-gradient-to-b from-background to-muted/20">
                <video
                  src="/voice.mp4"
                  width={1280}
                  height={720}
                  controls
                  autoPlay
                  muted
                  loop
                  className="w-full h-auto"
                  playsInline
                />
              </div>
              <div className="absolute -bottom-6 -right-6 -z-10 h-[300px] w-[300px] rounded-full bg-gradient-to-br from-secondary/30 to-primary/30 blur-3xl opacity-70"></div>
              <div className="absolute -top-6 -left-6 -z-10 h-[300px] w-[300px] rounded-full bg-gradient-to-br from-primary/30 to-secondary/30 blur-3xl opacity-70"></div>
            </motion.div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-20">
          <div className="container px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="flex flex-col items-center justify-center space-y-4 text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold tracking-tight">{t("featuresTitle")}</h2>
              <p className="max-w-[800px] text-muted-foreground md:text-lg">{t("featuresSubtitle")}</p>
            </motion.div>

            <motion.div
              variants={container}
              initial="hidden"
              whileInView="show"
              viewport={{ once: true }}
              className="grid gap-6 sm:grid-cols-2 lg:grid-cols-2"
            >
              <motion.div variants={item}>
                <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-md hover:-translate-y-1">
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="text-4xl mb-4">🛡️</div>
                    <h3 className="text-xl font-bold mb-2">{t("invisibilityFeature")}</h3>
                    <p className="text-muted-foreground">
                      {t("invisibilityDescription1")} <span className="text-primary font-medium">{t("tencentMeeting")}</span>, 
                      <span className="text-primary font-medium">{t("dingTalkMeeting")}</span> {t("invisibilityDescription2")}
                    </p>
                    <div className="flex flex-wrap gap-2 mt-4">
                      <Badge variant="outline" className="bg-primary/10">腾讯会议</Badge>
                      <Badge variant="outline" className="bg-primary/10">飞书会议</Badge>
                      <Badge variant="outline" className="bg-primary/10">钉钉会议</Badge>
                      <Badge variant="outline" className="bg-primary/10">牛客网</Badge>
                      <Badge variant="outline" className="bg-primary/10">赛马网</Badge>
                      <Badge variant="outline" className="bg-primary/10">华为笔试平台</Badge>
                      <Badge variant="outline" className="bg-primary/10">阿里笔试平台</Badge>
                      <Badge variant="outline" className="bg-primary/10">虾皮笔试平台</Badge>
                      <Badge variant="outline" className="bg-primary/10">萝卜笔试平台</Badge>
                      <Badge variant="outline" className="bg-primary/10">Zoom</Badge>
                      <Badge variant="outline" className="bg-primary/10">Teams</Badge>
                      <Badge variant="outline" className="bg-primary/10">Goolge Mettings</Badge>
                      <Badge variant="outline" className="bg-primary/10">HackerRank</Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={item}>
                <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-md hover:-translate-y-1">
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="text-4xl mb-4">📸</div>
                    <h3 className="text-xl font-bold mb-2">{t("screenshotFeature")}</h3>
                    <p className="text-muted-foreground">
                      {t("screenshotDescription")}
                    </p>
                    <div className="flex flex-wrap gap-2 mt-4">
                    <Badge variant="outline" className="bg-primary/10">Java</Badge>
                    <Badge variant="outline" className="bg-primary/10">Python</Badge>
                    <Badge variant="outline" className="bg-primary/10">JavaScript</Badge>
                    <Badge variant="outline" className="bg-primary/10">C/C++</Badge>
                    <Badge variant="outline" className="bg-primary/10">C≠</Badge>
                    <Badge variant="outline" className="bg-primary/10">Go</Badge>
                    <Badge variant="outline" className="bg-primary/10">Rust</Badge>
                    <Badge variant="outline" className="bg-primary/10">PHP</Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div variants={item}>
                <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-md hover:-translate-y-1">
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="text-4xl mb-4">⌨️</div>
                    <h3 className="text-xl font-bold mb-2">{t("shortcutFeature")}</h3>
                    <p className="text-muted-foreground">
                      {t("shortcutDescription1")} <span className="text-primary font-medium">{t("shortcutHighlight")}</span> {t("shortcutDescription2")}
                    </p>
                    <div className="flex flex-wrap gap-2 mt-4">
                    <Badge variant="outline" className="bg-primary/10">选择题</Badge>
                    <Badge variant="outline" className="bg-primary/10">行测题</Badge>
                    <Badge variant="outline" className="bg-primary/10">简答题</Badge>
                    <Badge variant="outline" className="bg-primary/10">算法题</Badge>
                    <Badge variant="outline" className="bg-primary/10">逻辑思维题</Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div variants={item}>
                <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-md hover:-translate-y-1">
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="text-4xl mb-4">🤖</div>
                    <h3 className="text-xl font-bold mb-2">{t("aiFeature")}</h3>
                    <p className="text-muted-foreground">
                      {t("aiDescription")}
                    </p>
                    <div className="flex flex-wrap gap-2 mt-4">
                      <Badge variant="outline" className="bg-primary/10">Doubao-1-5-vision-pro</Badge>
                      <Badge variant="outline" className="bg-primary/10">Doubao-1-5-thinking-pro</Badge>
                      <Badge variant="outline" className="bg-primary/10">DeepSeek R1</Badge>
                      <Badge variant="outline" className="bg-primary/10">DeepSeek V3</Badge>
                      <Badge variant="outline" className="bg-primary/10">Gemini-2.5</Badge>
                      <Badge variant="outline" className="bg-primary/10">Claude-4</Badge>
                      <Badge variant="outline" className="bg-primary/10">Grok-4</Badge>
                    </div>
                    
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>
            
          </div>
        </section>

        {/* How It Works Section */}
        <section className="w-full py-20 bg-muted/30 relative overflow-hidden" id="howItWorks">
          <div className="absolute inset-0 -z-10 h-full w-full bg-white dark:bg-black bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#1f1f1f_1px,transparent_1px),linear-gradient(to_bottom,#1f1f1f_1px,transparent_1px)] bg-[size:4rem_4rem] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_50%,#000_40%,transparent_100%)]"></div>
          
          {/* Background decorative elements */}
          <div className="absolute top-40 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl opacity-60 animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-80 h-80 bg-secondary/5 rounded-full blur-3xl opacity-60 animate-pulse" style={{ animationDuration: '15s' }}></div>

          <div className="container px-4 md:px-6 relative">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex flex-col items-center justify-center space-y-6 text-center mb-16"
            >
              {/* <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-2">
                简单 • 快速 • 安全
              </div> */}
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70">
                {t("howItWorksTitle")}
              </h2>
              <p className="max-w-[800px] text-muted-foreground md:text-lg">
                {t("howItWorksSubtitle")}
              </p>
            </motion.div>

            {/* Upgraded Layout for Steps */}
            <div className="grid lg:grid-cols-3 gap-8 lg:gap-12 relative">
              {/* Connection lines */}
              <div className="hidden lg:block absolute top-[30%] left-[calc(16.67%-10px)] right-[calc(16.67%-10px)] h-0.5 bg-gradient-to-r from-primary/40 to-primary/40 z-0"></div>
              <div className="hidden lg:block absolute top-[30%] left-[calc(50%-10px)] right-[calc(16.67%-10px)] h-0.5 bg-gradient-to-r from-primary/40 to-primary/40 z-0"></div>
              
              {/* Step 1: Download and Install */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="relative z-10 bg-card border border-border/40 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden"
              >
                <div className="absolute top-0 right-0 w-24 h-24 -mr-8 -mt-8 bg-gradient-to-br from-primary/30 via-primary/10 to-transparent rounded-full blur-2xl opacity-50"></div>
                
                <div className="px-6 py-8 flex flex-col items-center text-center">
                  <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-primary to-primary/60 text-primary-foreground text-2xl font-bold shadow-lg mb-6">
                    01
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{t("step1Title")}</h3>
                  
                  {/* Stylized illustration */}
                  <div className="mb-6 relative mx-auto w-full max-w-[220px] h-[120px] bg-muted/50 rounded-lg flex items-center justify-center">
                    <svg className="w-16 h-16 text-primary" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M56 24H40V8H56V24ZM40 32H56V48H40V32ZM24 8H8V24H24V8ZM8 32H24V48H8V32Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M40 8L24 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M40 48L24 48" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M24 24L40 24" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M24 32L40 32" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                    <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 px-3 py-1 bg-background rounded-full border border-border text-xs font-medium">
                      Windows 10+ / macOS 14.5+
                    </div>
                  </div>
                  
                  <p className="text-muted-foreground mb-6">{t("step1Description")}</p>
                  
                  {/* Download Buttons */}
                  <div className="flex flex-col w-full gap-3">
           
                    <Button onClick={() => setActiveDialog("windows")} className="w-full bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center">
                      <svg viewBox="0 0 24 24" className="h-5 w-5 mr-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 3.449L9.75 2.1V11.551H0V3.449ZM10.949 1.949L24 0V11.4H10.949V1.949ZM0 12.6H9.75V22.051L0 20.699V12.6ZM10.949 12.6H24V24L11.1 22.199" />
                      </svg>
                      Windows 下载
                    </Button>
                   
                    <div className="flex gap-2">
                      <Button onClick={() => setActiveDialog("mac-arm")}  className="w-full bg-gray-800 hover:bg-gray-900 text-white flex items-center justify-center">
                        <svg viewBox="0 0 24 24" className="h-5 w-5 mr-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path d="M11.6734 8.25004C10.7950 8.25004 9.54245 7.65079 8.81372 6.8329C8.15587 6.08751 7.67797 5.01126 7.67797 3.9329C7.67797 3.74129 7.68981 3.5496 7.70224 3.37504C8.81118 3.40901 10.1795 4.04585 10.9621 4.89168C11.5712 5.54337 12.1426 6.59165 12.1426 7.6754C12.1426 7.86712 12.119 8.04901 12.1059 8.12915C12.0573 8.14315 11.8771 8.25004 11.6734 8.25004ZM16.0043 20.2191C16.8372 19.2522 17.0407 18.7888 17.5281 17.7718C18.0912 16.6024 18.4859 15.0777 18.4859 13.611C18.4859 11.9988 17.9213 10.5117 16.872 9.49248C16.0409 8.68332 14.842 8.16129 13.6897 8.16129C12.3942 8.16129 11.3882 8.71657 10.6935 9.15496C10.2219 9.45135 9.81868 9.60126 9.40052 9.60126C8.94463 9.60126 8.50706 9.43346 8.0272 9.14415C7.38305 8.76126 6.67347 8.24996 5.87293 8.25004C4.55359 8.25015 3.26953 8.91265 2.40942 10.0581C1.17176 11.6973 1.03111 14.5976 1.92847 17.0016C2.59587 18.8305 3.54501 20.5524 4.69082 20.5524C5.21563 20.5524 5.54355 20.3679 5.89805 20.1678C6.29251 19.9461 6.72697 19.7026 7.41989 19.7026C8.11708 19.7026 8.55298 19.9468 8.94881 20.169C9.30467 20.3697 9.63331 20.5549 10.1599 20.5549C11.3433 20.5549 12.2695 18.8551 13.0243 16.9257C13.3888 15.9682 13.6728 14.9303 13.8701 14.0504C14.6723 13.7616 15.4057 13.3045 15.9432 12.703C16.524 12.0541 16.9522 11.2404 17.1867 10.3607C16.3723 9.94037 15.7343 9.25368 15.3235 8.47404C14.9039 7.67818 14.7111 6.7616 14.8098 5.82554C13.7893 6.15257 12.9277 6.85054 12.3399 7.69496C11.7521 8.53932 11.4339 9.54257 11.3734 10.4926C11.9659 10.8212 12.3778 11.3338 12.6775 11.9261C13.1393 12.8254 13.335 13.9258 13.335 14.9998C13.335 16.1583 13.1166 17.4064 12.8111 18.5026C16.0693 18.7655 16.0043 20.2191 16.0043 20.2191Z" />
                      </svg>
                      ARM版
                      </Button>
                      <Button onClick={() => setActiveDialog("mac-intel")} className="w-full bg-gray-600 hover:bg-gray-700 text-white flex items-center justify-center">
                        <svg viewBox="0 0 24 24" className="h-5 w-5 mr-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path d="M11.6734 8.25004C10.7950 8.25004 9.54245 7.65079 8.81372 6.8329C8.15587 6.08751 7.67797 5.01126 7.67797 3.9329C7.67797 3.74129 7.68981 3.5496 7.70224 3.37504C8.81118 3.40901 10.1795 4.04585 10.9621 4.89168C11.5712 5.54337 12.1426 6.59165 12.1426 7.6754C12.1426 7.86712 12.119 8.04901 12.1059 8.12915C12.0573 8.14315 11.8771 8.25004 11.6734 8.25004ZM16.0043 20.2191C16.8372 19.2522 17.0407 18.7888 17.5281 17.7718C18.0912 16.6024 18.4859 15.0777 18.4859 13.611C18.4859 11.9988 17.9213 10.5117 16.872 9.49248C16.0409 8.68332 14.842 8.16129 13.6897 8.16129C12.3942 8.16129 11.3882 8.71657 10.6935 9.15496C10.2219 9.45135 9.81868 9.60126 9.40052 9.60126C8.94463 9.60126 8.50706 9.43346 8.0272 9.14415C7.38305 8.76126 6.67347 8.24996 5.87293 8.25004C4.55359 8.25015 3.26953 8.91265 2.40942 10.0581C1.17176 11.6973 1.03111 14.5976 1.92847 17.0016C2.59587 18.8305 3.54501 20.5524 4.69082 20.5524C5.21563 20.5524 5.54355 20.3679 5.89805 20.1678C6.29251 19.9461 6.72697 19.7026 7.41989 19.7026C8.11708 19.7026 8.55298 19.9468 8.94881 20.169C9.30467 20.3697 9.63331 20.5549 10.1599 20.5549C11.3433 20.5549 12.2695 18.8551 13.0243 16.9257C13.3888 15.9682 13.6728 14.9303 13.8701 14.0504C14.6723 13.7616 15.4057 13.3045 15.9432 12.703C16.524 12.0541 16.9522 11.2404 17.1867 10.3607C16.3723 9.94037 15.7343 9.25368 15.3235 8.47404C14.9039 7.67818 14.7111 6.7616 14.8098 5.82554C13.7893 6.15257 12.9277 6.85054 12.3399 7.69496C11.7521 8.53932 11.4339 9.54257 11.3734 10.4926C11.9659 10.8212 12.3778 11.3338 12.6775 11.9261C13.1393 12.8254 13.335 13.9258 13.335 14.9998C13.335 16.1583 13.1166 17.4064 12.8111 18.5026C16.0693 18.7655 16.0043 20.2191 16.0043 20.2191Z" />
                      </svg>
                      Intel版
                      </Button>
                    
                    </div>
                  </div>
                  <div className="mt-3 text-xs text-muted-foreground">
                    <span className="inline-flex items-center">
                      <svg viewBox="0 0 20 20" fill="currentColor" className="w-3.5 h-3.5 mr-1">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clipRule="evenodd" />
                      </svg>
                      Mac提示"文件损坏"
                    </span>
                    <a href="https://sysin.org/blog/macos-if-crashes-when-opening/" className="text-primary hover:underline ml-1">
                      解决方法
                    </a>
                  </div>
                </div>
              </motion.div>

              {/* Step 2: Enter Key */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="relative z-10 bg-card border border-border/40 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden"
              >
                <div className="absolute top-0 right-0 w-24 h-24 -mr-8 -mt-8 bg-gradient-to-br from-secondary/30 via-secondary/10 to-transparent rounded-full blur-2xl opacity-50"></div>
                
                <div className="px-6 py-8 flex flex-col items-center text-center">
                  <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-primary to-primary/60 text-primary-foreground text-2xl font-bold shadow-lg mb-6">
                    02
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{t("enterKeyTitle")}</h3>
                  
                  {/* Key Input Image with enhanced styling */}
                  <div className="mb-6 w-full max-w-[280px] overflow-hidden rounded-xl border border-border/30 shadow-lg">
                    <div className="relative w-full aspect-[4/3]">
                      <Image
                        src="/key.png"
                        alt={t("keyInputAlt")}
                        fill
                        className="object-cover hover:scale-105 transition-all duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                    </div>
                    <div className="bg-muted/30 backdrop-blur-sm p-3 border-t border-border/30">
                      <p className="text-sm font-medium">输入激活密钥</p>
                      {/* <p className="text-xs text-muted-foreground mt-1">联系客服获取</p> */}
                    </div>
                  </div>
                  
                  <p className="text-muted-foreground mb-4">{t("enterKeyDescription")}</p>
                </div>
              </motion.div>

              {/* Step 3: Verify Operation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="relative z-10 bg-card border border-border/40 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden"
              >
                <div className="absolute top-0 right-0 w-24 h-24 -mr-8 -mt-8 bg-gradient-to-br from-primary/30 via-primary/10 to-transparent rounded-full blur-2xl opacity-50"></div>
                
                <div className="px-6 py-8 flex flex-col items-center text-center">
                  <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-primary to-primary/60 text-primary-foreground text-2xl font-bold shadow-lg mb-6">
                    03
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{t("verifyTitle")}</h3>
                  
                  {/* Verification Links */}
                  <div className="w-full max-w-[280px] space-y-4 mb-6">
                    <Link 
                      href="#howItWorks" 
                      className="flex items-center justify-between p-4 w-full rounded-xl bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 hover:border-primary/40 transition-all hover:shadow-md hover:from-primary/15 hover:to-primary/10 group"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3 group-hover:bg-primary/30 transition-colors">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                            <polyline points="14 2 14 8 20 8"/>
                            <path d="m10 13-2 2 2 2"/>
                            <path d="m14 17 2-2-2-2"/>
                          </svg>
                        </div>
                        <div className="text-left">
                          <p className="font-medium">熟悉操作</p>
                          <p className="text-xs text-muted-foreground group-hover:text-primary/70 transition-colors">下面的使用说明</p>
                        </div>
                      </div>
                      <div className="h-8 w-8 rounded-full flex items-center justify-center text-primary/70 bg-white/80 dark:bg-gray-800/80 shadow-sm group-hover:bg-primary/10 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="transition-transform group-hover:translate-x-0.5">
                          <path d="m9 18 6-6-6-6"/>
                        </svg>
                      </div>
                    </Link>
                    
                    <Link 
                      href="#faq" 
                      className="flex items-center justify-between p-4 w-full rounded-xl bg-gradient-to-r from-amber-500/10 to-amber-500/5 border border-amber-500/20 hover:border-amber-500/40 transition-all hover:shadow-md hover:from-amber-500/15 hover:to-amber-500/10 group"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-amber-500/20 flex items-center justify-center mr-3 group-hover:bg-amber-500/30 transition-colors">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-500">
                            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/>
                            <circle cx="12" cy="12" r="3"/>
                          </svg>
                        </div>
                        <div className="text-left">
                          <p className="font-medium">隐身性验证</p>
                          <p className="text-xs text-muted-foreground group-hover:text-amber-500/70 transition-colors">查看常见问题解答</p>
                        </div>
                      </div>
                      <div className="h-8 w-8 rounded-full flex items-center justify-center text-amber-500/70 bg-white/80 dark:bg-gray-800/80 shadow-sm group-hover:bg-amber-500/10 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="transition-transform group-hover:translate-x-0.5">
                          <path d="m9 18 6-6-6-6"/>
                        </svg>
                      </div>
                    </Link>
                    
                  </div>
                  <div className="mt-2 w-full max-w-xs mx-auto">
                  <div className="rounded-xl bg-gradient-to-r from-primary/5 to-amber-100/30 dark:to-amber-900/10 border border-border/40 px-4 py-3 flex flex-col gap-2 shadow-sm">
                    <div className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M9 12l2 2 4-4" />
                      </svg>
                      <span className="text-sm text-green-700 dark:text-green-300">Windows系统隐身性目前没有任何问题</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-amber-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 8v4" />
                        <path d="M12 16h.01" />
                      </svg>
                      <span className="text-sm text-amber-700 dark:text-amber-300">
                        Mac系统存在一些已知问题
                        <Link href="#faq" className="underline hover:text-primary ml-1">常见问题解答</Link>
                      </span>
                    </div>
                  </div>
                </div>

                </div>
              </motion.div>
            </div>
            
            {/* Additional Instructions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="mt-16"
            >
              <div className="text-center mb-12 relative">
                <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 w-40 h-40 bg-primary/5 rounded-full blur-3xl opacity-70 mix-blend-multiply"></div>
                <h2 className="text-3xl font-bold mb-3 relative bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70">{t("shortcutsTitle")}</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto relative">支持三种方式，根据实际情况灵活切换</p>
                <div className="mt-4 w-20 h-1 bg-gradient-to-r from-primary to-primary/20 rounded-full mx-auto"></div>
              </div>
              
              {/* Enhanced Shortcuts Cards - All Three in One Row */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-10">
                {/* Screenshot Shortcuts Card */}
                <div className="bg-card border border-border/60 rounded-xl shadow-sm transition-all hover:shadow-md hover:border-primary/20 overflow-hidden">
                  <div className="flex items-center p-5 border-b border-border/30">
                    <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-6"><path d="M15 8h.01"/><path d="M11 20H7a5 5 0 0 1-5-5V7a5 5 0 0 1 5-5h10a5 5 0 0 1 5 5v4"/><path d="m22 22-5-5"/><circle cx="17" cy="17" r="5"/></svg>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">鼠标点击操作</h3>
                      <span className="text-sm text-primary/80 font-medium">推荐使用方式</span>
                    </div>
                  </div>
                  
                  <div className="relative group">
                    <video 
                      src="/portal.mp4"
                      className="w-full h-[200px] object-cover"
                      autoPlay
                      controls
                      playsInline
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-background/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center pointer-events-none">
                      <div className="backdrop-blur-sm bg-background/30 rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-muted/30">
                    <p className="text-sm text-muted-foreground">
                      移动合适位置后使用, 点击操作穿透至底层页面，以避免误触发。
                    </p>
                  </div>
                </div>
                
                {/* Mouse Gesture Card */}
                <div className="bg-card border border-border/60 rounded-xl shadow-sm transition-all hover:shadow-md hover:border-primary/20 overflow-hidden">
                  <div className="flex items-center p-5 border-b border-border/30">
                    <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-6"><path d="M4 11V8a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v7"/><path d="M9 14h2"/><path d="M13 8V6a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1h-3"/><path d="M20 16a2 2 0 1 0-4 0v4h6v-1a3 3 0 0 0-2-3Z"/></svg>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">快捷键操作</h3>
                      <span className="text-sm text-amber-500 font-medium">备用操作</span>
                    </div>
                  </div>
                  
                  <div className="relative group">
                    <video 
                      src="/shortkeys.mp4"
                      className="w-full h-[200px] object-cover"
                      controls
                      autoPlay
                      playsInline
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-background/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center pointer-events-none">
                      <div className="backdrop-blur-sm bg-background/30 rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-muted/30">
                    <p className="text-sm text-muted-foreground">
                      截图操作类似微信截图一样，先定位左上角，再定位右下角。
                    </p>
                  </div>
                </div>
                
                {/* Text/Clipboard Shortcuts Card */}
                <div className="bg-card border border-border/60 rounded-xl shadow-sm transition-all hover:shadow-md hover:border-primary/20 overflow-hidden">
                  <div className="flex items-center p-5 border-b border-border/30">
                    <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-6"><path d="M9 3h6v11h-6z"/><path d="m9 14 3 7 3-7"/><path d="M6 8h12"/><path d="M6 16h12"/></svg>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">{t("textShortcuts")}</h3>
                      <span className="text-sm text-red-500 font-medium">共享屏幕不适用</span>
                    </div>
                  </div>
                  
                  <div className="relative group">
                    <video 
                      src="/paste.mp4"
                      className="w-full h-[200px] object-cover"
                      autoPlay
                      controls
                      playsInline
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-background/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center pointer-events-none">
                      <div className="backdrop-blur-sm bg-background/30 rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-muted/30">
                    <p className="text-sm text-muted-foreground">
                      复制文本后使用 Alt/Option + Z 快速获取答案。
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Control Shortcuts Card - Full Width */}
              <div className="relative overflow-hidden bg-gradient-to-br from-card to-card/80 border border-border/40 rounded-xl shadow-md transition-all hover:shadow-lg hover:border-primary/10 group">
                <div className="absolute -right-16 -top-16 w-48 h-48 bg-primary/5 rounded-full blur-3xl opacity-70 transition-all duration-700 group-hover:opacity-100"></div>
                <h3 className="text-xl font-bold p-6 border-b border-border/40 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary mr-3 size-7">
                    <rect x="3" y="11" width="18" height="10" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                  功能控制快捷键
                </h3>
                
                {/* Important Notice Box */}
                <div className="mx-6 mt-4 p-4 bg-amber-50 dark:bg-amber-950/30 border-l-4 border-amber-500 rounded-r-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-0.5">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-500">
                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                        <line x1="12" y1="9" x2="12" y2="13"></line>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-base font-bold text-amber-800 dark:text-amber-300">重要使用注意事项</h4>
                      <ul className="mt-2 space-y-2 text-sm text-amber-700 dark:text-amber-300/90 list-disc pl-5">
                        <li><span className="font-medium">网络要求：</span>软件必须在有网的情况下使用，<span className="underline decoration-amber-500 decoration-2">且不建议使用科学上网等梯子工具</span>，会造成网络不稳定</li>
                        <li><span className="font-medium">系统要求：</span>推荐在 Windows 10+ 系统或 macOS 14.5+ 版本下使用</li>
                        <li><span className="font-medium">截图要求：</span>不支持鼠标滚动截图，题目很长的情况下，可以截两张图一起上传</li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-6">
                   <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/50 rounded-xl flex flex-col items-center text-center hover:bg-muted/60 hover:shadow-md transition-all duration-300 border border-transparent hover:border-border/20 group/item">
                    <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 transform group-hover/item:scale-110 transition-transform duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-7">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                      </svg>
                    </div>
                    <div className="font-semibold text-lg mb-2">{t("quitShortcut") || "关闭程序"}</div>
                    <kbd className="px-3 py-1.5 bg-background shadow-lg border border-border/60 rounded-lg text-sm font-medium w-full hover:border-primary/20 transition-all">Ctrl/Command + Q</kbd>
                    <p className="text-muted-foreground mt-2 text-sm">一键关闭程序</p>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/50 rounded-xl flex flex-col items-center text-center hover:bg-muted/60 hover:shadow-md transition-all duration-300 border border-transparent hover:border-border/20 group/item">
                    <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 transform group-hover/item:scale-110 transition-transform duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-7">
                        <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                        <path d="M3 3v5h5"/>
                        <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 5.39-1.61"/>
                        <path d="M16 16h5v5"/>
                      </svg>
                    </div>
                    <div className="font-semibold text-lg mb-2">{t("resetShortcut")}</div>
                    <kbd className="px-3 py-1.5 bg-background shadow-lg border border-border/60 rounded-lg text-sm font-medium w-full hover:border-primary/20 transition-all">Ctrl/Command + R</kbd>
                    <p className="text-muted-foreground mt-2 text-sm">重置当前状态，准备下一题</p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/50 rounded-xl flex flex-col items-center text-center hover:bg-muted/60 hover:shadow-md transition-all duration-300 border border-transparent hover:border-border/20 group/item">
                    <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 transform group-hover/item:scale-110 transition-transform duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-7">
                        <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/>
                        <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/>
                        <path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/>
                        <line x1="2" x2="22" y1="2" y2="22"/>
                      </svg>
                    </div>
                    <div className="font-semibold text-lg mb-2">{t("hideShortcut")}</div>
                    <kbd className="px-3 py-1.5 bg-background shadow-lg border border-border/60 rounded-lg text-sm font-medium w-full hover:border-primary/20 transition-all">Ctrl/Command + B</kbd>
                    <p className="text-muted-foreground mt-2 text-sm">一键隐藏界面</p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/50 rounded-xl flex flex-col items-center text-center hover:bg-muted/60 hover:shadow-md transition-all duration-300 border border-transparent hover:border-border/20 group/item">
                    <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 transform group-hover/item:scale-110 transition-transform duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-7">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="m9 16 3-3 3 3"/>
                        <path d="m9 8 3 3 3-3"/>
                      </svg>
                    </div>
                    <div className="font-semibold text-lg mb-2">{t("moveShortcut")}</div>
                    <kbd className="px-3 py-1.5 bg-background shadow-lg border border-border/60 rounded-lg text-sm font-medium w-full hover:border-primary/20 transition-all">Ctrl/Command + ↑↓←→</kbd>
                    <p className="text-muted-foreground mt-2 text-sm">移动结果窗口到合适位置</p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/50 rounded-xl flex flex-col items-center text-center hover:bg-muted/60 hover:shadow-md transition-all duration-300 border border-transparent hover:border-border/20 group/item">
                    <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 transform group-hover/item:scale-110 transition-transform duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-7">
                        <polyline points="16 18 22 12 16 6"/>
                        <polyline points="8 6 2 12 8 18"/>
                      </svg>
                    </div>
                    <div className="font-semibold text-lg mb-2">{t("copyCodeShortcut")}</div>
                    <kbd className="px-3 py-1.5 bg-background shadow-lg border border-border/60 rounded-lg text-sm font-medium w-full hover:border-primary/20 transition-all">Alt/Option + C</kbd>
                    <p className="text-muted-foreground mt-2 text-sm">一键复制AI返回的代码</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="w-full py-20">
          <div className="container px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="flex flex-col items-center justify-center space-y-4 text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold tracking-tight">{t("testimonialsTitle")}</h2>
              <p className="max-w-[800px] text-muted-foreground md:text-lg">{t("testimonialsSubtitle")}</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="relative mx-auto max-w-[90vw] md:max-w-[95vw] xl:max-w-[1400px]"
            >
              <TestimonialsCarousel />
            </motion.div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="w-full py-20 bg-muted/30 relative overflow-hidden">
          <div className="absolute inset-0 -z-10 h-full w-full bg-white dark:bg-black bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#1f1f1f_1px,transparent_1px),linear-gradient(to_bottom,#1f1f1f_1px,transparent_1px)] bg-[size:4rem_4rem] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_50%,#000_40%,transparent_100%)]"></div>

          <div className="container px-4 relative">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="flex flex-col items-center justify-center space-y-4 text-center mb-12"
            >
              {/* <Badge className="rounded-full px-4 py-1.5 text-sm font-medium" variant="secondary">
                {t("pricing")}
              </Badge> */}
              <h2 className="text-3xl md:text-4xl font-bold tracking-tight">{t("pricingTitle")}</h2>
              <p className="max-w-[800px] text-muted-foreground md:text-lg">{t("pricingSubtitle")}</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-16 mx-auto"
            >
              <div className="bg-card border border-border/40 rounded-xl shadow-md overflow-hidden">
                <div className="p-6 border-b border-border/30 bg-gradient-to-r from-muted/10 to-background">
                  <h3 className="text-xl font-bold flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary mr-3">
                      <path d="M12 2c1.5 0 3 .5 3 2-2 0-6 0-6 0 0-1.5 1.5-2 3-2zm0 6v10M9 8h6"></path>
                      <path d="M11 13c-.3 0-.5-.3-.5-.5 0-.3.2-.5.5-.5h2c.3 0 .5.2.5.5 0 .2-.2.5-.5.5h-2Z"></path>
                    </svg>
                    AI模型对比与选择指南
                  </h3>
                  <p className="text-muted-foreground mt-2">不同AI模型特点及使用场景，根据您的需求选择最合适的模型</p>
                </div>
                
                <div className="overflow-x-auto w-full">
                  <table className="w-full border-collapse min-w-[800px]">
                    <thead>
                      <tr className="bg-gradient-to-r from-primary/5 to-primary/10 border-b border-border/40">
                        <th className="px-6 py-5 text-left font-medium text-primary whitespace-nowrap">模型名称</th>
                        <th className="px-6 py-5 text-left font-medium text-primary whitespace-nowrap">特点</th>
                        <th className="px-6 py-5 text-left font-medium text-primary whitespace-nowrap">适用场景</th>
                        <th className="px-6 py-5 text-left font-medium text-primary whitespace-nowrap">模型响应耗时</th>
                        <th className="px-6 py-5 text-left font-medium text-primary whitespace-nowrap">消耗积分</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border/30">
                      <tr className="hover:bg-muted/30 transition-all duration-200 group border-b border-border/20">
                        <td className="px-6 py-5">
                          <div className="font-medium text-base group-hover:text-primary transition-colors">Doubao-1.5-vision-pro</div>
                          <div className="text-xs text-muted-foreground mt-1.5 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><circle cx="12" cy="12" r="1"/><path d="M5 12a7 7 0 1 0 14 0 7 7 0 1 0-14 0Z"/><path d="M12 19a7 7 0 1 0 0-14"/></svg>
                            视觉增强版
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-sm px-3 py-1 bg-primary/5 rounded-md inline-block">强大的视觉分析能力，支持图像理解</span>
                        </td>
                        <td className="px-6 py-5">
                          <ul className="text-sm space-y-2">
                            <li className="flex items-center">
                              <Badge className="mr-2 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 hover:bg-green-200">基础</Badge>
                              选择题
                            </li>
                            <li className="flex items-center gap-1.5">
                              <span className="w-1.5 h-1.5 rounded-full bg-primary/60"></span>
                              行测题 中等偏下算法题
                            </li>
                          </ul>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-green-600 dark:text-green-400 font-medium flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0Z"/><path d="M12 7v5l2.5 2.5"/></svg>
                            15-25秒
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <span className="font-medium text-amber-600 dark:text-amber-400 text-lg flex items-center">
                            4
                          </span>
                        </td>
                      </tr>
                      
                      <tr className="hover:bg-muted/30 transition-all duration-200 group border-b border-border/20">
                        <td className="px-6 py-5">
                          <div className="font-medium text-base group-hover:text-primary transition-colors">Doubao-1.5-thinking-pro</div>
                          <div className="text-xs text-muted-foreground mt-1.5 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 2a7 7 0 0 1 7 7v1.5a4.5 4.5 0 0 1 9 0v.75a2.25 2.25 0 0 1-4.5 0v-1.5a.75.75 0 0 0-1.5 0V12a7 7 0 0 1-14 0v-1.5a.75.75 0 0 0-1.5 0v1.5a2.25 2.25 0 0 1-4.5 0v-.75a4.5 4.5 0 0 1 9 0V9a7 7 0 0 1 7-7Z"/></svg>
                            思考增强型
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-sm px-3 py-1 bg-primary/5 rounded-md inline-block">更强的逻辑思考能力，擅长推理与解题</span>
                        </td>
                        <td className="px-6 py-5">
                          <ul className="text-sm space-y-2">
                            <li className="flex items-center">
                              <Badge className="mr-2 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 hover:bg-green-200">基础</Badge>
                              算法题解析
                            </li>
                            <li className="flex items-center gap-1.5">
                              <span className="w-1.5 h-1.5 rounded-full bg-primary/60"></span>
                              逻辑推理题
                            </li>
                          </ul>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-yellow-600 dark:text-yellow-400 font-medium flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0Z"/><path d="M12 7v5l2.5 2.5"/></svg>
                            2-3分钟
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <span className="font-medium text-amber-600 dark:text-amber-400 text-lg flex items-center">
                            4
                          </span>
                        </td>
                      </tr>
                      
                      <tr className="hover:bg-muted/30 transition-all duration-200 group border-b border-border/20">
                        <td className="px-6 py-5">
                          <div className="font-medium text-base group-hover:text-primary transition-colors">DeepSeek V3</div>
                          <div className="text-xs text-muted-foreground mt-1.5 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>
                            250324版本
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-sm px-3 py-1 bg-primary/5 rounded-md inline-block">响应速度快，代码生成准确度高</span>
                        </td>
                        <td className="px-6 py-5">
                          <ul className="text-sm space-y-2">
                            <li className="flex items-center">
                              <Badge className="mr-2 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 hover:bg-green-200">基础</Badge>
                              选择题
                            </li>
                            <li className="flex items-center gap-1.5">
                              <span className="w-1.5 h-1.5 rounded-full bg-primary/60"></span>
                              常见算法题
                            </li>
                          </ul>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-green-600 dark:text-green-400 font-medium flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0Z"/><path d="M12 7v5l2.5 2.5"/></svg>
                            15-30秒
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <span className="font-medium text-amber-600 dark:text-amber-400 text-lg flex items-center">
                            5
                          </span>
                        </td>
                      </tr>
                      
                      <tr className="hover:bg-muted/30 transition-all duration-200 group border-b border-border/20">
                        <td className="px-6 py-5">
                          <div className="font-medium text-base group-hover:text-primary transition-colors">DeepSeek R1</div>
                          <div className="text-xs text-muted-foreground mt-1.5 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 20h9"/><path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"/></svg>
                            250528版本
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-sm px-3 py-1 bg-primary/5 rounded-md inline-block">深度思考，精确度高，擅长复杂推理</span>
                        </td>
                        <td className="px-6 py-5">
                          <ul className="text-sm space-y-2">
                            <li className="flex items-center gap-1.5">
                              <Badge className="mr-2 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 hover:bg-green-200">基础</Badge>
                              复杂代码问题
                            </li>
                            <li className="flex items-center gap-1.5">
                              <span className="w-1.5 h-1.5 rounded-full bg-primary/60"></span>
                              技术底层原理
                            </li>
                          </ul>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-red-600 dark:text-red-400 font-medium flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0Z"/><path d="M12 7v5l2.5 2.5"/></svg>
                            3-4分钟
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <span className="font-medium text-amber-600 dark:text-amber-400 text-lg flex items-center">
                            5
                          </span>
                        </td>
                      </tr>
                      
           
                      <tr className="bg-gradient-to-r from-primary/5 to-primary/10 hover:from-primary/10 hover:to-primary/15 transition-all duration-200 group border-b border-border/20">
                        <td className="px-6 py-5">
                          <div className="font-medium text-base group-hover:text-primary transition-colors">Gemini-2.5-Flash-Preview</div>
                          <div className="text-xs text-muted-foreground mt-1.5 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M20 20v-5h-5"/><path d="M20 10V5h-5"/><path d="M4 20v-5h5"/><path d="M4 10V5h5"/><path d="M12 22v-6"/><path d="M12 8V2"/><path d="M2 12h6"/><path d="M16 12h6"/><circle cx="12" cy="12" r="1"/></svg>
                            Google最新thinking模型
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-sm px-3 py-1 bg-primary/10 rounded-md inline-block">综合表现优异，代码生成准确</span>
                        </td>
                        <td className="px-6 py-5">
                          <ul className="text-sm space-y-2">
                            <li className="flex items-center">
                              <Badge className="mr-2 bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300 hover:bg-purple-200">高级</Badge>
                              复杂算法题
                            </li>
                            <li className="flex items-center gap-1.5">
                              <span className="w-1.5 h-1.5 rounded-full bg-primary/80"></span>
                              通用问题
                            </li>
                          </ul>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-yellow-600 dark:text-yellow-400 font-medium flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0Z"/><path d="M12 7v5l2.5 2.5"/></svg>
                            2-3分钟
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <span className="font-medium text-amber-600 dark:text-amber-400 text-lg flex items-center">
                            6
                          </span>
                        </td>
                      </tr>
                      
                      <tr className="bg-gradient-to-r from-primary/5 to-primary/10 hover:from-primary/10 hover:to-primary/15 transition-all duration-200 group border-b border-border/20">
                        <td className="px-6 py-5">
                          <div className="font-medium text-base group-hover:text-primary transition-colors">Claude-4-Sonnet</div>
                          <div className="text-xs text-muted-foreground mt-1.5 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M16 10H6m12 .5s1.5-1 1.5-3-1-3-3-3h-12s1 1.5 1 3-1 3-1 3h6m8 2s2 1.5 2 3-1 3-3 3h-13c3 0 3-3 3-3s.5-3-2.5-3m10.5 0h-7"/></svg>
                            Anthropic最新模型
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-sm px-3 py-1 bg-primary/10 rounded-md inline-block">理解能力极强，精确度最高，擅长解决复杂问题</span>
                        </td>
                        <td className="px-6 py-5">
                          <ul className="text-sm space-y-2">
                            <li className="flex items-center">
                              <Badge className="mr-2 bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300 hover:bg-purple-200">高级</Badge>
                              复杂算法题
                            </li>
                            <li className="flex items-center gap-1.5">
                              <span className="w-1.5 h-1.5 rounded-full bg-primary/80"></span>
                              开放性问答, 系统设计题
                            </li>
                          </ul>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-green-600 dark:text-green-400 font-medium flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0Z"/><path d="M12 7v5l2.5 2.5"/></svg>
                            10-20秒
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <span className="font-medium text-amber-600 dark:text-amber-400 text-lg flex items-center">
                            8
                          </span>
                        </td>
                      </tr>

                      <tr className="bg-gradient-to-r from-primary/5 to-primary/10 hover:from-primary/10 hover:to-primary/15 transition-all duration-200 group border-b border-border/20">
                        <td className="px-6 py-5">
                          <div className="font-medium text-base group-hover:text-primary transition-colors">Grok-4</div>
                          <div className="text-xs text-muted-foreground mt-1.5 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                            xAI最新模型
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-sm px-3 py-1 bg-primary/10 rounded-md inline-block">实时信息处理，创新思维强，适合复杂推理</span>
                        </td>
                        <td className="px-6 py-5">
                          <ul className="text-sm space-y-2">
                            <li className="flex items-center">
                              <Badge className="mr-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200">高级</Badge>
                              创新性问题
                            </li>
                            <li className="flex items-center gap-1.5">
                              <span className="w-1.5 h-1.5 rounded-full bg-primary/80"></span>
                              复杂算法题, 逻辑推理题
                            </li>
                          </ul>
                        </td>
                        <td className="px-6 py-5">
                          <span className="text-yellow-600 dark:text-yellow-400 font-medium flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0Z"/><path d="M12 7v5l2.5 2.5"/></svg>
                            2-3分钟
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <span className="font-medium text-amber-600 dark:text-amber-400 text-lg flex items-center">
                            8
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  {/* <div className="mt-6 p-4 rounded-xl bg-gradient-to-r from-primary/5 to-amber-100/20 border border-border/30 flex items-start gap-3">
                    <svg className="w-6 h-6 text-primary shrink-0 mt-0.5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10" />
                      <path d="M12 8v4" />
                      <path d="M12 16h.01" />
                    </svg>
                    <div>
                      <div className="font-semibold text-base mb-1">模型选择建议</div>
                      <ul className="text-sm text-muted-foreground list-disc pl-5 space-y-1">
                        <li>
                          <span className="text-primary font-medium">日常行测/选择题：</span>
                          推荐 <span className="font-semibold text-green-700 dark:text-green-300">Doubao-1.5-vision-pro</span>，速度快、积分消耗低。
                        </li>
                        <li>
                          <span className="text-primary font-medium">复杂推理/主观题：</span>
                          推荐 <span className="font-semibold text-blue-700 dark:text-blue-300">Doubao-1.5-thinking-pro</span> 或 <span className="font-semibold text-purple-700 dark:text-purple-300">DeepSeek R1</span>，逻辑能力更强。
                        </li>
                        <li>
                          <span className="text-primary font-medium">高精度/难题：</span>
                          推荐 <span className="font-semibold text-orange-700 dark:text-orange-300">Claude-4-Sonnet</span>，但响应时间和积分消耗较高。
                        </li>
                      </ul>
                    </div>
                  </div> */}
                </div>
              </div>
            </motion.div>

            <div className="mx-auto">
              <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-5 lg:gap-4 xl:gap-6">
                {[
                  {
                    name: "新客专项",
                    price: "￥0",
                    originalPrice: "￥10",
                    description: "新用户专享",
                    features: [
                      "10积分",
                      "所有模型权限",
                      "技术支持",
                      "试用尝鲜"
                    ],
                    cta: "立即购买",
                    icon: "Star"
                  },
                  {
                    name: "入门套餐",
                    price: "￥50",
                    originalPrice: "￥50",
                    description: "适合个人短期使用",
                    features: [
                      "50积分",
                      "初级模型使用权限",
                      "基础客户支持",
                      "有效期90天"
                    ],
                    cta: "立即购买",
                    icon: "Zap"
                  },
                  {
                    name: "基础套餐",
                    price: "￥100",
                    originalPrice: "￥125",
                    description: "适合个人笔试准备",
                    features: [
                      "125积分",
                      "所有模型使用权限",
                      "语音识别功能",
                      "交流群资格",
                      "永久有效"
                    ],
                    cta: "立即购买",
                    popular: true,
                    icon: "Users"
                  },
                  {
                    name: "专业套餐",
                    price: "￥200",
                    originalPrice: "￥300",
                    description: "适合长期使用",
                    features: [
                      "300积分",
                      "所有模型使用权限",
                      "语音识别功能",
                      "交流群资格",
                      "永久有效"
                    ],
                    cta: "立即购买",
                    icon: "Building"
                  },
                  {
                    name: "代理套餐",
                    price: "￥500",
                    originalPrice: "￥1000",
                    description: "适合代理商长期使用",
                    features: [
                      "1000积分",
                      "所有模型使用权限",
                      "语音识别功能",
                      "交流群资格",
                      "共赢合作",
                    ],
                    cta: "立即购买",
                    icon: "Shield"
                  }
                ].map((plan, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: i * 0.1 }}
                  >
                    <Card
                      className={`relative overflow-hidden h-full ${
                        plan.popular ? "border-primary shadow-lg" : "border-border/40 shadow-md"
                      } bg-gradient-to-b from-background to-muted/10 backdrop-blur hover:shadow-lg transition-all duration-300 ${
                        plan.popular ? "hover:-translate-y-1" : "hover:-translate-y-0.5"
                      }`}
                    >
                      {plan.popular && (
                        <div className="absolute top-0 right-0 bg-primary text-primary-foreground px-3 py-1 text-xs font-medium rounded-bl-lg">
                          推荐套餐
                        </div>
                      )}
                      <CardContent className="p-6 flex flex-col h-full">
                        <div className="flex items-center mb-4">
                          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                            {plan.icon === "Zap" && <Zap className="size-5" />}
                            {plan.icon === "Users" && <Users className="size-5" />}
                            {plan.icon === "Shield" && <Shield className="size-5" />}
                            {plan.icon === "Star" && <Star className="size-5" />}
                            {plan.icon === "Building" && <Layers className="size-5" />}
                          </div>
                          <h3 className="text-xl font-bold">{plan.name}</h3>
                        </div>
                        
                        <div className="flex items-baseline mt-2">
                          <span className="text-3xl font-bold">{plan.price}</span>
                          {plan.originalPrice && (
                            <span className="text-sm text-muted-foreground ml-2 line-through">{plan.originalPrice}</span>
                          )}
                        </div>
                        <p className="text-muted-foreground text-sm mt-2 mb-4">{plan.description}</p>
                        <div className="h-px w-full bg-border/50 my-2"></div>
                        <ul className="space-y-3 my-4 flex-grow">
                          {plan.features.map((feature, j) => (
                            <li key={j} className="flex items-center text-sm">
                              <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3 flex-shrink-0">
                                <Check className="size-3" />
                              </div>
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                        <Button
                          className={`w-full mt-auto rounded-full ${
                            plan.popular ? "bg-primary hover:bg-primary/90" : ""
                          } transition-all`}
                          variant={plan.popular ? "default" : "outline"}
                        >
                          {plan.cta}
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
              
              <div className="mt-12 flex flex-col items-center">
                <div className="bg-muted/50 border border-border/40 rounded-xl p-4 max-w-2xl text-center">
                  <p className="text-sm text-muted-foreground">
                    所有套餐都支持隐身模式、AI答题、截图识别等全部功能。积分可按需使用，不同模型耗费积分不同。
                    <br />如需大量积分或长期使用，建议选择更高级的套餐或代理套餐。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq" className="w-full py-20 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 -z-10 opacity-30">
            <div className="absolute inset-0 bg-white dark:bg-black bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#1f1f1f_1px,transparent_1px),linear-gradient(to_bottom,#1f1f1f_1px,transparent_1px)] bg-[size:4rem_4rem] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_50%,#000_40%,transparent_100%)]"></div>
          </div>
          
          {/* Decorative elements */}
          <div className="absolute top-40 left-[10%] w-64 h-64 bg-primary/5 rounded-full blur-3xl opacity-60 animate-pulse"></div>
          <div className="absolute bottom-40 right-[10%] w-80 h-80 bg-secondary/5 rounded-full blur-3xl opacity-60 animate-pulse" style={{ animationDuration: '15s' }}></div>
          
          <div className="container px-4 md:px-6 relative">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="flex flex-col items-center justify-center space-y-6 text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70">{t("faqTitle")}</h2>
              <p className="max-w-[800px] text-muted-foreground md:text-lg">{t("faqSubtitle")}</p>
              <div className="w-20 h-1 bg-gradient-to-r from-primary to-primary/20 rounded-full mt-2"></div>
            </motion.div>

            <div className="mx-auto max-w-4xl relative">
              <div className="absolute -top-10 -left-10 w-40 h-40 bg-primary/5 rounded-full blur-3xl opacity-30"></div>
              <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-secondary/5 rounded-full blur-3xl opacity-30"></div>
              
              <Accordion type="single" collapsible className="w-full space-y-4">
                {[
                    {
                    icon: "Star",
                    question: t("faqQuestion5"),
                    answer: t("faqAnswer5"),
                    image: "/niuke.jpg" // Add image path for question 5
                  },
                  {
                    icon: "AlertTriangle",
                    question: t("faqQuestion6"),
                    answer: t("faqAnswer6"),
                    image: "/mac.png" // Add image path for question 6
                  },
                  {
                    icon: "HelpCircle",
                    question: t("faqQuestion1"),
                    answer: t("faqAnswer1"),
                  },
                  {
                    icon: "MessageSquare",
                    question: t("faqQuestion9"),
                    answer: t("faqAnswer9"),
                  },
                  {
                    icon: "ShieldCheck",
                    question: t("faqQuestion2"),
                    answer: t("faqAnswer2"),
                  },
                  {
                    icon: "Zap",
                    question: t("faqQuestion3"),
                    answer: t("faqAnswer3"),
                  },
                  {
                    icon: "Briefcase",
                    question: t("faqQuestion4"),
                    answer: t("faqAnswer4"),
                  },
                  {
                    icon: "MessageSquare",
                    question: t("faqQuestion8"),
                    answer: t("faqAnswer8"),
                  },
                ].map((faq, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: i * 0.05 }}
                    className="group"
                  >
                    <AccordionItem 
                      value={`faq-${i}`} 
                      className="border border-border/40 rounded-xl shadow-sm bg-card/80 backdrop-blur-sm overflow-hidden hover:shadow-md hover:border-primary/20 transition-all duration-300"
                    >
                      <AccordionTrigger className="px-6 py-4 flex items-center gap-4 group-data-[state=open]:bg-muted/40 hover:bg-muted/20 transition-colors">
                        <div className="flex-shrink-0 w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                          {faq.icon === "HelpCircle" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><path d="M12 17h.01"/></svg>}
                          {faq.icon === "ShieldCheck" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/><path d="m9 12 2 2 4-4"/></svg>}
                          {faq.icon === "Zap" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M13 2 3 14h9l-1 8 10-12h-9l1-8z"/></svg>}
                          {faq.icon === "Briefcase" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg>}
                          {faq.icon === "Clock" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>}
                          {faq.icon === "CreditCard" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="14" x="2" y="5" rx="2"/><line x1="2" x2="22" y1="10" y2="10"/></svg>}
                          {faq.icon === "Users" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>}
                          {faq.icon === "MessageSquare" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>}
                          {faq.icon === "AlertTriangle" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>}
                          {faq.icon === "Eye" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg>}
                          {faq.icon === "Star" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>}
                          {faq.icon === "Lightbulb" && <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"></path><path d="M9 18h6"></path><path d="M10 22h4"></path></svg>}
                        </div>
                        <span className="font-medium text-left flex-grow">{faq.question}</span>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 py-6 pl-20 bg-gradient-to-b from-muted/30 to-transparent">
                        <div className="space-y-3 text-muted-foreground leading-relaxed max-w-none animate-accordion-down">
                          {faq.answer.split('\n').map((paragraph, idx) => (
                            paragraph ? (
                              <p key={idx} className="text-sm md:text-base transition-colors duration-200 hover:text-foreground">
                                {paragraph}
                              </p>
                            ) : <div key={idx} className="h-2" /> // Empty line becomes spacing
                          ))}
                          {faq.image && (
                            <div className="mt-4">
                              <Image
                                src={faq.image}
                                alt=""
                                width={500}
                                height={300}
                                className="w-full h-auto rounded-lg shadow-md"
                                priority
                              />
                            </div>
                          )}
                          <div className="pt-2">
                            <div className="w-16 h-0.5 bg-gradient-to-r from-primary/20 to-transparent rounded-full"></div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </motion.div>
                ))}
              </Accordion>
              
              <div className="mt-10 flex flex-col items-center text-center">
                <p className="text-muted-foreground max-w-[600px]">还有其他问题? 请随时联系管理员</p>
                <Button variant="outline" className="mt-4 rounded-full group">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 group-hover:text-primary transition-colors"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" /></svg>
                  联系管理员
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Download Link Dialog */}
      <Dialog open={activeDialog !== null} onOpenChange={() => setActiveDialog(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {activeDialog === "windows" && "Windows 下载链接"}
              {activeDialog === "mac-arm" && "macOS (ARM) 下载链接"}
              {activeDialog === "mac-intel" && "macOS (Intel) 下载链接"}
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 rounded-lg border border-border/40 bg-muted/30 space-y-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">{t("downloadLink")}:</p>
              <div className="flex items-center justify-between p-3 rounded bg-background border border-border">
                <span className="text-sm truncate mr-2">
                  {activeDialog === "windows" && "https://wwwb.lanzouu.com/i929C2yl3y4j"}
                  {activeDialog === "mac-arm" && "https://wwwb.lanzouu.com/ieXf62yqbclc"}
                  {activeDialog === "mac-intel" && "https://wwwb.lanzouu.com/iQ4xS2yqbdsf"}
                </span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => {
                     const url = activeDialog  === "windows"
                        ? "https://wwwb.lanzouu.com/i929C2yl3y4j"
                        : activeDialog  === "mac-arm"
                        ? "https://wwwb.lanzouu.com/ieXf62yqbclc"
                        : "https://wwwb.lanzouu.com/iQ4xS2yqbdsf"
                      
                    navigator.clipboard.writeText(url);
                  }}
                  className="h-8 w-8"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">{t("password")}:</p>
              <div className="flex items-center justify-between p-3 rounded bg-background border border-border">
                <span className="text-sm">
                  {activeDialog === "windows" && "cop1"}
                  {activeDialog === "mac-arm" && "a2z2"}
                  {activeDialog === "mac-intel" && "9ey4"}
                </span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => {
                    const password = activeDialog  === "windows"
                      ? "cop1"
                      : activeDialog  === "mac-arm"
                      ? "a2z2"
                      : "9ey4";
                    navigator.clipboard.writeText(password);
                  }}
                  className="h-8 w-8"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => window.open(activeDialog === 'windows' ? 'https://wwwb.lanzouu.com/i929C2yl3y4j' : activeDialog === "mac-arm" ? 'https://wwwb.lanzouu.com/ieXf62yqbclc' : 'https://wwwb.lanzouu.com/iQ4xS2yqbdsf', "_blank")} className="w-full">
              <ExternalLink className="h-4 w-4 mr-2" />
              打开下载链接
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <footer className="w-full border-t bg-background/95 backdrop-blur-sm">
        <div className="container flex flex-col gap-8 px-4 py-10 md:px-6 lg:py-16">
          <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2 font-bold">
                <div className="size-8 rounded-lg bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center text-primary-foreground">
                  C
                </div>
                <span>Offer Helper</span>
              </div>
              <p className="text-sm text-muted-foreground">{t("footerDescription")}</p>
              <div className="flex gap-4">
                <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="size-5"
                  >
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                  </svg>
                  <span className="sr-only">Facebook</span>
                </Link>
                <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="size-5"
                  >
                    <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                  </svg>
                  <span className="sr-only">Twitter</span>
                </Link>
                <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="size-5"
                  >
                    <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                    <rect width="4" height="12" x="2" y="9"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                  <span className="sr-only">LinkedIn</span>
                </Link>
              </div>
            </div>
            <div className="space-y-4">
              <h4 className="text-sm font-bold">{t("footerProduct")}</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#features" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerFeatures")}
                  </Link>
                </li>
                <li>
                  <Link href="#pricing" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerPricing")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerIntegrations")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerAPI")}
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h4 className="text-sm font-bold">{t("footerResources")}</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerDocumentation")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerGuides")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerBlog")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerSupport")}
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h4 className="text-sm font-bold">{t("footerCompany")}</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerAbout")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerCareers")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerPrivacyPolicy")}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    {t("footerTermsOfService")}
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="flex flex-col gap-4 sm:flex-row justify-between items-center border-t border-border/40 pt-8">
            <p className="text-xs text-muted-foreground">
              &copy; {new Date().getFullYear()} Offer Helper. {t("footerRights")}
            </p>
            <div className="flex gap-4">
              <Link href="#" className="text-xs text-muted-foreground hover:text-foreground transition-colors">
                {t("footerPrivacyPolicy")}
              </Link>
              <Link href="#" className="text-xs text-muted-foreground hover:text-foreground transition-colors">
                {t("footerTermsOfService")}
              </Link>
              <Link href="#" className="text-xs text-muted-foreground hover:text-foreground transition-colors">
                {t("footerCookiePolicy")}
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
