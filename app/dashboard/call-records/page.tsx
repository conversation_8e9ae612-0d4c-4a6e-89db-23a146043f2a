"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import {
  Search,
  Download,
  RefreshCw,
  Calendar,
  Clock,
  BarChart3,
  Activity,
  CheckCircle,
  XCircle
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { UsageRecordService, UsageRecordDto } from "@/lib/api"
import DashboardLayout from "@/components/dashboard/dashboard-layout"

// 简化的调用记录类型
interface CallRecord {
  id: number
  apiKeyId: number
  apiKeyValue: string
  usedAt: string // 响应时间
  endpoint: string // 模型名称
  duration: number // 响应耗时（毫秒）
}

export default function CallRecordsPage() {
  const [records, setRecords] = useState<CallRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const router = useRouter()

  useEffect(() => {
    // 检查登录状态
    const token = localStorage.getItem("token")
    if (!token) {
      router.push("/login")
      return
    }

    // 加载调用记录
    loadCallRecords()
  }, [router, currentPage, statusFilter, dateRange])

  const loadCallRecords = async () => {
    try {
      const response = await UsageRecordService.getUsageRecords({
        page: currentPage - 1, // Java后端从0开始
        size: pageSize,
        sortBy: 'usedAt',
        sortDir: 'desc'
      })

      if (response.code === 0 && response.data) {
        // 直接使用后端返回的数据
        const transformedRecords: CallRecord[] = response.data.records.map(record => ({
          id: record.id,
          apiKeyId: record.apiKeyId,
          apiKeyValue: record.apiKeyValue,
          usedAt: record.usedAt,
          endpoint: record.endpoint,
          duration: record.duration || 0
        }))
        setRecords(transformedRecords)
      } else {
        toast.error(response.message || "加载调用记录失败")
      }
    } catch (error) {
      toast.error("网络错误: " + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const exportRecords = async () => {
    try {
      // 使用当前已加载的记录进行导出
      const csvHeaders = [
        "时间",
        "API Key",
        "端点",
        "持续时间(ms)"
      ]

      const csvRows = [csvHeaders.join(",")]

      filteredRecords.forEach(record => {
        const row = [
          new Date(record.usedAt).toLocaleString(),
          record.apiKeyValue,
          record.endpoint,
          record.duration.toString()
        ]
        csvRows.push(row.join(","))
      })

      const csvContent = csvRows.join("\n")
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `call-records-${format(new Date(), "yyyy-MM-dd")}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      toast.success("导出成功")
    } catch (error) {
      toast.error("导出失败")
    }
  }



  const filteredRecords = records.filter(record =>
    record.endpoint.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.apiKeyValue.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 统计数据
  const stats = {
    total: records.length,
    success: records.length, // 假设所有记录都是成功的
    error: 0, // 简化统计
    avgResponseTime: records.length > 0
      ? Math.round(records.reduce((sum, r) => sum + (r.duration || 0), 0) / records.length)
      : 0
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">调用记录</h1>
            <p className="text-muted-foreground">
              查看和分析您的API调用历史
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={loadCallRecords}>
              <RefreshCw className="w-4 h-4 mr-2" />
              刷新
            </Button>
            <Button onClick={exportRecords}>
              <Download className="w-4 h-4 mr-2" />
              导出
            </Button>
          </div>
        </div>
        {/* 筛选和搜索 */}
        <Card>
          <CardHeader>
            <CardTitle>筛选条件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索端点或API Key..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="success">成功 (2xx)</SelectItem>
                  <SelectItem value="client-error">客户端错误 (4xx)</SelectItem>
                  <SelectItem value="server-error">服务器错误 (5xx)</SelectItem>
                </SelectContent>
              </Select>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[280px] justify-start text-left font-normal",
                      !dateRange.from && "text-muted-foreground"
                    )}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} -{" "}
                          {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>选择日期范围</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={setDateRange}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </CardContent>
        </Card>

        {/* 调用记录列表 */}
        <Card>
          <CardHeader>
            <CardTitle>调用记录</CardTitle>
            <CardDescription>
              显示 {filteredRecords.length} 条记录
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredRecords.length === 0 ? (
              <div className="text-center py-12">
                <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">暂无调用记录</h3>
                <p className="mt-2 text-muted-foreground">
                  开始使用API后，调用记录将显示在这里
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>响应时间</TableHead>
                    <TableHead>模型名称</TableHead>
                    <TableHead>响应耗时</TableHead>
                    <TableHead>API Key</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          {format(new Date(record.usedAt), "MM/dd HH:mm:ss")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {record.endpoint}
                        </code>
                      </TableCell>
                      <TableCell>
                        <span className={cn(
                          "font-medium",
                          (record.duration || 0) > 1000 ? "text-red-600" :
                          (record.duration || 0) > 500 ? "text-yellow-600" : "text-green-600"
                        )}>
                          {record.duration || 0}ms
                        </span>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-2 py-1 rounded">
                          {record.apiKeyValue.slice(0, 8)}...{record.apiKeyValue.slice(-4)}
                        </code>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
