"use client"

import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  BookOpen, 
  Download, 
  Key, 
  Keyboard, 
  RotateCcw, 
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Monitor,
  Smartphone,
  Globe,
  MessageSquare
} from "lucide-react"

export default function UsageGuidePage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center">
              <BookOpen className="w-8 h-8 mr-3" />
              使用说明
            </h1>
            <p className="text-muted-foreground">
              详细的使用指南和常见问题解答
            </p>
          </div>
        </div>

        {/* 快速开始 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
              快速开始
            </CardTitle>
            <CardDescription>
              按照以下步骤快速上手使用
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              <div className="flex items-start space-x-3">
                <Badge variant="outline" className="mt-1">1</Badge>
                <div>
                  <h4 className="font-medium">下载安装</h4>
                  <p className="text-sm text-muted-foreground">
                    选择对应版本的安装包，下载安装后，微信联系 Java_Plus 获取试用 Key，填入Key
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Badge variant="outline" className="mt-1">2</Badge>
                <div>
                  <h4 className="font-medium">注册 Key（联系咨询）</h4>
                  <p className="text-sm text-muted-foreground">
                    通过联系方式获取使用 Key
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Badge variant="outline" className="mt-1">3</Badge>
                <div>
                  <h4 className="font-medium">验证功能</h4>
                  <p className="text-sm text-muted-foreground">
                    验证共享屏幕隐藏性和快捷键功能
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 快捷键说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Keyboard className="w-5 h-5 mr-2 text-blue-600" />
              快捷键操作
            </CardTitle>
            <CardDescription>
              掌握这些快捷键，提升使用效率
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 截图功能 */}
            <div>
              <h4 className="font-medium mb-3 flex items-center">
                <Monitor className="w-4 h-4 mr-2" />
                截图功能
              </h4>
              <div className="space-y-2 ml-6">
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">当前光标位置作为图片左上角</span>
                  <Badge variant="secondary">Alt/Option + 1</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">当前光标位置作为图片右下角</span>
                  <Badge variant="secondary">Alt/Option + 2</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">DeepSeek chat 模型</span>
                  <Badge variant="secondary">Alt/Option + Shift + 1</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">DeepSeek r1 模型</span>
                  <Badge variant="secondary">Alt/Option + Shift + 2</Badge>
                </div>
              </div>
            </div>

            <Separator />

            {/* 复制文本解答 */}
            <div>
              <h4 className="font-medium mb-3 flex items-center">
                <MessageSquare className="w-4 h-4 mr-2" />
                复制文本解答
              </h4>
              <div className="space-y-2 ml-6">
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">复制文本</span>
                  <Badge variant="secondary">Ctrl/Command + C</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">DeepSeek chat 模型</span>
                  <Badge variant="secondary">Alt/Option + Z</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">DeepSeek r1 模型</span>
                  <Badge variant="secondary">Alt/Option + X</Badge>
                </div>
              </div>
            </div>

            <Separator />

            {/* 其他操作 */}
            <div>
              <h4 className="font-medium mb-3 flex items-center">
                <RotateCcw className="w-4 h-4 mr-2" />
                其他操作
              </h4>
              <div className="space-y-2 ml-6">
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">重置</span>
                  <Badge variant="secondary">Ctrl/Command + R</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded">
                  <span className="text-sm">隐藏窗口</span>
                  <Badge variant="secondary">Ctrl/Command + H</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 新手教程 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="w-5 h-5 mr-2 text-purple-600" />
              新手教程
            </CardTitle>
            <CardDescription>
              详细的操作步骤指南
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-medium">1. 下载安装</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  选择对应版本的安装包，下载安装后，微信联系 Java_Plus 获取试用 Key，填入Key
                </p>
              </div>
              
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium">2. 验证共享屏幕是否具备隐藏性</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  打开笔试或者面试平台，点击共享屏幕，一般会有个小窗口，即可验证是否能看到，如果能看到，只能选择共享某个程序或者窗口。
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  会议软件可以使用录制的方式或者两个账号登录会议的方式验证。
                </p>
              </div>
              
              <div className="border-l-4 border-yellow-500 pl-4">
                <h4 className="font-medium">3. 验证快捷键是否正常</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  验证快捷键功能是否正常。
                </p>
              </div>
              
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-medium">4. 验证图片位置是否正常（1.0.3以下版本）</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  使用截图快捷键后，工具上面会显示图片，将图片拖拽到浏览器后Enter打开，如果图片与实际位置不符，设置一下当前电脑的屏幕缩放为100%
                </p>
              </div>
              
              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-medium text-red-600">5. 注意事项（非常重要）</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  答案未返回之前，不要继续提问，否则会造成key次数浪费，耐心等待，chat模型一般需要在 30s-60s 返回结果，r1模型一般在 3min-5min 返回结果
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  当前题做完后，一定要使用 Ctrl/Command + R 重置后，再进行下一道题
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 常见问题 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
              常见问题
            </CardTitle>
            <CardDescription>
              遇到问题时的解决方案
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">1. 软件支持哪些操作系统？</h4>
                <p className="text-sm text-muted-foreground">
                  目前支持 Windows 10+ 和 macOS arm 系统。注意：部分windows10系统在共享屏幕的情况下，可能存在对方视角出现黑块，此时需要升级系统或者更换电脑。
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">2. 如何获取使用Key？</h4>
                <p className="text-sm text-muted-foreground">
                  您可以通过"联系咨询"部分的联系方式获取使用 Key。
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">3. Key剩余次数是否可以退？</h4>
                <p className="text-sm text-muted-foreground">
                  不可以。可以转让或者出售给其他人。
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">4. Key是否支持多端使用？</h4>
                <p className="text-sm text-muted-foreground">
                  可以，Key可以在多台设备同时使用。
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">5. Key一次如何计算？</h4>
                <p className="text-sm text-muted-foreground">
                  使用 (Alt/Option + Shift + 1 或者 Alt/Option + Shift + 2) 识别截图、使用（Alt/Option + Z 或者 Alt/Option + X）识别剪切板后成功获取结果算一次操作。
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">6. 如何关闭程序？</h4>
                <p className="text-sm text-muted-foreground">
                  Windows通过任务管理器，Mac通过活动监视器关闭。
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">7. 截图后使用报错如何解决？</h4>
                <p className="text-sm text-muted-foreground">
                  （1）当使用 Alt/Option + 1 后直接报错，是因为截图权限未开。<br/>
                  （2）当使用 Alt/Option + 3 后直接报错，当前key剩余积分不足或者图片内容空白，使用Ctrl/Command + R重置后，重新截图，重新获取答案，答案未返回之前积分不减少。
                </p>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">8. 支持的平台有哪些？</h4>
                <p className="text-sm text-muted-foreground">
                  (1) 常见的笔试平台包括不限于：牛客网、赛马网、LeetCode、CodeSignal、HackerRank、Codility等。<br/>
                  (2) 常见的面试平台包括不限于：腾讯会议、钉钉会议、飞书会议、Zoom、Teams、Google Meet等。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 编程语言支持 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="w-5 h-5 mr-2 text-indigo-600" />
              编程语言支持
            </CardTitle>
            <CardDescription>
              支持几乎所有主流编程语言
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {["Java", "Python", "JavaScript", "C/C++", "Go", "Rust", "PHP", "TypeScript", "C#", "Ruby", "Swift", "Kotlin"].map((lang) => (
                <Badge key={lang} variant="outline" className="text-sm">
                  {lang}
                </Badge>
              ))}
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              我们的产品集成最新的 DeepSeek 大模型，提供专业级的代码生成、分析和优化能力，远超传统编程助手。
            </p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
