"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  BarChart3,
  Key,
  Activity,
  Users,
  TrendingUp,
  Shield,
  Zap,
  Calendar,
  Award,
  Sparkles,
  ArrowUpRight,
  Target,
  Gauge,
  Star,
  Wallet
} from "lucide-react"
import { DashboardService, DashboardStats, DashboardUserInfo, ApiKeyService } from "@/lib/api"
import { toast } from "sonner"

export default function DashboardPage() {
  const [user, setUser] = useState<DashboardUserInfo | null>(null)
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // 检查登录状态
    const token = localStorage.getItem("token")

    if (!token) {
      router.push("/login")
      return
    }

    // 加载仪表板数据
    loadDashboardData()
  }, [router])

  const loadDashboardData = async () => {
    try {
      const response = await DashboardService.getDashboardStats()

      if (response.code === 0 && response.data) {
        setUser(response.data.userInfo)
        setStats(response.data.stats)
      } else {
        toast.error(response.message || "加载仪表板数据失败")
      }
    } catch (error) {
      console.error("加载仪表板数据失败:", error)
      toast.error("网络错误，请稍后重试")
    } finally {
      setLoading(false)
    }
  }

  // 测试401错误处理的函数
  const test401Error = async () => {
    try {
      // 临时保存当前token
      const currentToken = localStorage.getItem('token')

      // 设置一个无效的token来触发401错误
      localStorage.setItem('token', 'invalid-token-for-testing')

      // 尝试调用一个需要认证的外部API
      await ApiKeyService.getApiKeys()

      // 如果没有抛出错误，恢复原token
      if (currentToken) {
        localStorage.setItem('token', currentToken)
      }
    } catch (error) {
      // 这里会捕获401错误，但401处理逻辑已经在ApiClient中执行了
      console.log('401错误已被处理:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user || !stats) {
    return null
  }

  // 统计数据配置
  const statsConfig = [
    {
      title: "剩余积分",
      value: stats.remainingCalls.toString(),
      change: "+12%",
      icon: <Wallet className="h-5 w-5" />,
      color: "text-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      borderColor: "border-blue-200 dark:border-blue-800/30",
      gradient: "from-blue-500 to-blue-600",
      darkGradient: "dark:from-blue-600 dark:to-blue-700"
    },
    {
      title: "总积分",
      value: stats.totalCalls.toString(),
      change: "",
      icon: <Target className="h-5 w-5" />,
      color: "text-green-600",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      borderColor: "border-green-200 dark:border-green-800/30",
      gradient: "from-green-500 to-green-600",
      darkGradient: "dark:from-green-600 dark:to-green-700"
    },
    {
      title: "API Key 数量",
      value: stats.apiKeyCount.toString(),
      change: `+${stats.apiKeyCount > 0 ? '1' : '0'}`,
      icon: <Key className="h-5 w-5" />,
      color: "text-purple-600",
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
      borderColor: "border-purple-200 dark:border-purple-800/30",
      gradient: "from-purple-500 to-purple-600",
      darkGradient: "dark:from-purple-600 dark:to-purple-700"
    }
  ]



  return (
    <DashboardLayout>
      <div className="min-h-full bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900/20">
        <div className="flex flex-col space-y-4 p-4">
        {/* 欢迎区域 */}
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20 p-4 border border-blue-100 dark:border-blue-800/30 flex-shrink-0">
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-200/30 to-purple-200/30 dark:from-blue-400/20 dark:to-purple-400/20 rounded-full -translate-y-10 translate-x-10"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-indigo-200/30 to-blue-200/30 dark:from-indigo-400/20 dark:to-blue-400/20 rounded-full translate-y-8 -translate-x-8"></div>

          <div className="relative flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <h1 className="text-2xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                  欢迎回来，{user.name}!
                </h1>
              </div>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                这是您的 API 管理工作台概览
              </p>
              <div className="flex items-center space-x-3 mt-2">
                <Badge variant="outline" className="bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700 px-2 py-0.5 text-xs">
                  <Shield className="w-3 h-3 mr-1" />
                  在线
                </Badge>
                <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700 px-2 py-0.5 text-xs">
                  <Calendar className="w-3 h-3 mr-1" />
                  {new Date().toLocaleDateString()}
                </Badge>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
                <Award className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid gap-3 md:grid-cols-3 flex-shrink-0">
          {statsConfig.map((stat, index) => (
            <Card key={index} className={`relative overflow-hidden border-0 shadow-md hover:shadow-lg transition-all duration-300 ${stat.bgColor} ${stat.borderColor} border group`}>
              <div className={`absolute top-0 right-0 w-14 h-14 bg-gradient-to-br ${stat.gradient} ${stat.darkGradient} opacity-10 dark:opacity-20 rounded-full -translate-y-7 translate-x-7 group-hover:opacity-20 dark:group-hover:opacity-30 transition-opacity duration-300`}></div>

              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5">
                <CardTitle className="text-xs font-medium text-gray-600 dark:text-gray-300">
                  {stat.title}
                </CardTitle>
                <div className={`p-1.5 rounded-lg bg-gradient-to-br ${stat.gradient} ${stat.darkGradient} shadow-sm group-hover:shadow-md transition-shadow duration-300`}>
                  <div className="text-white">
                    {stat.icon}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="relative pt-0 pb-3">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">{stat.value}</div>
                {stat.change && (
                  <div className="flex items-center space-x-1">
                    <ArrowUpRight className="w-3 h-3 text-green-600 dark:text-green-400" />
                    <span className="text-xs font-medium text-green-600 dark:text-green-400">
                      {stat.change}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">较上月</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 主要内容区域 - 重新设计的三栏布局 */}
        <div className="grid gap-4 lg:grid-cols-12 flex-1 min-h-0">
          {/* 快速操作 - 占据更多空间 */}
          <div className="lg:col-span-5 flex flex-col">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 dark:from-gray-800 dark:via-blue-900/20 dark:to-indigo-900/30 h-fit hover:shadow-xl transition-all duration-300 group">
              <CardHeader className="pb-3 flex-shrink-0 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-400/10 to-purple-400/10 dark:from-blue-400/20 dark:to-purple-400/20 rounded-full -translate-y-12 translate-x-12 group-hover:scale-110 transition-transform duration-500"></div>
                <CardTitle className="flex items-center text-xl font-bold relative z-10">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 rounded-xl mr-3 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                    <Zap className="w-5 h-5 text-white" />
                  </div>
                  <span className="bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 bg-clip-text text-transparent">
                    快速操作
                  </span>
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-300 text-sm ml-12 relative z-10">
                  高效管理您的API服务，一键直达核心功能
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2 pb-6 px-6">
                <div className="grid gap-3 grid-cols-1">
                  <Button
                    className="group/btn justify-start h-14 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.01] hover:-translate-y-0.5"
                    onClick={() => router.push("/dashboard/api-keys")}
                  >
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-white/20 rounded-lg mr-3 group-hover/btn:bg-white/30 transition-colors duration-300">
                        <Key className="w-4 h-4" />
                      </div>
                      <div className="text-left flex-1">
                        <div className="font-medium text-sm">API Key 管理</div>
                        <div className="text-xs opacity-90">创建和管理访问密钥</div>
                      </div>
                      <ArrowUpRight className="w-4 h-4 opacity-70 group-hover/btn:opacity-100 group-hover/btn:translate-x-0.5 group-hover/btn:-translate-y-0.5 transition-all duration-300" />
                    </div>
                  </Button>

                  <Button
                    className="group/btn justify-start h-14 bg-gradient-to-r from-emerald-500 via-green-600 to-teal-600 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.01] hover:-translate-y-0.5"
                    onClick={() => router.push("/dashboard/call-records")}
                  >
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-white/20 rounded-lg mr-3 group-hover/btn:bg-white/30 transition-colors duration-300">
                        <BarChart3 className="w-4 h-4" />
                      </div>
                      <div className="text-left flex-1">
                        <div className="font-medium text-sm">调用记录</div>
                        <div className="text-xs opacity-90">查看详细使用统计</div>
                      </div>
                      <ArrowUpRight className="w-4 h-4 opacity-70 group-hover/btn:opacity-100 group-hover/btn:translate-x-0.5 group-hover/btn:-translate-y-0.5 transition-all duration-300" />
                    </div>
                  </Button>

                  <Button
                    className="group/btn justify-start h-14 bg-gradient-to-r from-purple-500 via-violet-600 to-indigo-600 hover:from-purple-600 hover:via-indigo-600 hover:to-blue-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.01] hover:-translate-y-0.5"
                    onClick={() => router.push("/dashboard/usage-guide")}
                  >
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-white/20 rounded-lg mr-3 group-hover/btn:bg-white/30 transition-colors duration-300">
                        <Activity className="w-4 h-4" />
                      </div>
                      <div className="text-left flex-1">
                        <div className="font-medium text-sm">使用指南</div>
                        <div className="text-xs opacity-90">学习API使用方法</div>
                      </div>
                      <ArrowUpRight className="w-4 h-4 opacity-70 group-hover/btn:opacity-100 group-hover/btn:translate-x-0.5 group-hover/btn:-translate-y-0.5 transition-all duration-300" />
                    </div>
                  </Button>

                  <Button
                    className="group/btn justify-start h-14 bg-gradient-to-r from-orange-500 via-red-500 to-pink-600 hover:from-orange-600 hover:via-pink-600 hover:to-rose-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.01] hover:-translate-y-0.5"
                    onClick={() => router.push("/dashboard/recharge")}
                  >
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-white/20 rounded-lg mr-3 group-hover/btn:bg-white/30 transition-colors duration-300">
                        <TrendingUp className="w-4 h-4" />
                      </div>
                      <div className="text-left flex-1">
                        <div className="font-medium text-sm">积分充值</div>
                        <div className="text-xs opacity-90">购买更多API积分</div>
                      </div>
                      <ArrowUpRight className="w-4 h-4 opacity-70 group-hover/btn:opacity-100 group-hover/btn:translate-x-0.5 group-hover/btn:-translate-y-0.5 transition-all duration-300" />
                    </div>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 账户信息 - 重新设计 */}
          <div className="lg:col-span-4 flex flex-col">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-white via-indigo-50/40 to-purple-50/60 dark:from-gray-800 dark:via-indigo-900/20 dark:to-purple-900/30 h-fit hover:shadow-xl transition-all duration-300 group">
              <CardHeader className="pb-3 flex-shrink-0 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-400/10 to-purple-400/10 dark:from-indigo-400/20 dark:to-purple-400/20 rounded-full -translate-y-10 translate-x-10 group-hover:scale-110 transition-transform duration-500"></div>
                <CardTitle className="flex items-center text-lg font-bold relative z-10">
                  <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 rounded-xl mr-3 shadow-lg">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <span className="bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent">
                    账户信息
                  </span>
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-300 text-sm ml-12 relative z-10">
                  您的个人账户详细信息
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2 pb-6 px-6 space-y-3">
                <div className="space-y-3">
                  <div className="group/item flex items-center justify-between p-4 bg-gradient-to-r from-white/80 to-indigo-50/80 dark:from-gray-700/50 dark:to-indigo-900/30 rounded-xl backdrop-blur-sm border border-indigo-100/50 dark:border-indigo-800/30 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-400 dark:to-indigo-400 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">邮箱地址</span>
                    </div>
                    <span className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate ml-2 max-w-[140px]" title={user.email}>
                      {user.email}
                    </span>
                  </div>

                  <div className="group/item flex items-center justify-between p-4 bg-gradient-to-r from-white/80 to-green-50/80 rounded-xl backdrop-blur-sm border border-green-100/50 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                      <span className="text-sm font-medium text-gray-700">总调用次数</span>
                    </div>
                    <span className="text-sm font-bold text-green-600">{stats.totalCallCount.toLocaleString()}</span>
                  </div>

                  <div className="group/item flex items-center justify-between p-4 bg-gradient-to-r from-white/80 to-orange-50/80 rounded-xl backdrop-blur-sm border border-orange-100/50 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                      <span className="text-sm font-medium text-gray-700">已使用积分</span>
                    </div>
                    <span className="text-sm font-bold text-orange-600">{stats.usedCalls.toLocaleString()}</span>
                  </div>

                  {user.lastLogin && (
                    <div className="group/item flex items-center justify-between p-4 bg-gradient-to-r from-white/80 to-purple-50/80 rounded-xl backdrop-blur-sm border border-purple-100/50 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-sm font-medium text-gray-700">上次登录</span>
                      </div>
                      <span className="text-sm font-semibold text-gray-900">
                        {new Date(user.lastLogin).toLocaleDateString('zh-CN')}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 使用概览 - 重新设计 */}
          <div className="lg:col-span-3 flex flex-col">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-white via-emerald-50/40 to-teal-50/60 h-fit hover:shadow-xl transition-all duration-300 group">
              <CardHeader className="pb-3 flex-shrink-0 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-400/10 to-teal-400/10 rounded-full -translate-y-10 translate-x-10 group-hover:scale-110 transition-transform duration-500"></div>
                <CardTitle className="flex items-center text-lg font-bold relative z-10">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl mr-3 shadow-lg">
                    <Gauge className="w-5 h-5 text-white" />
                  </div>
                  <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    使用概览
                  </span>
                </CardTitle>
                <CardDescription className="text-gray-600 text-sm ml-12 relative z-10">
                  实时监控您的API使用情况
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2 pb-6 px-6 space-y-4">
                {/* 使用率进度条 */}
                <div className="p-5 bg-gradient-to-r from-white/90 to-emerald-50/90 rounded-2xl backdrop-blur-sm border border-emerald-100/50 shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-sm font-semibold text-gray-700 flex items-center">
                      <div className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full mr-2"></div>
                      积分使用率
                    </span>
                    <span className="text-lg font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                      {Math.round((stats.usedCalls / stats.totalCalls) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden shadow-inner">
                    <div
                      className="bg-gradient-to-r from-emerald-500 via-green-500 to-teal-600 h-3 rounded-full transition-all duration-1000 ease-out shadow-sm relative overflow-hidden"
                      style={{ width: `${Math.round((stats.usedCalls / stats.totalCalls) * 100)}%` }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>已使用: {stats.usedCalls.toLocaleString()}</span>
                    <span>总计: {stats.totalCalls.toLocaleString()}</span>
                  </div>
                </div>

                {/* 统计卡片 - 紧凑设计 */}
                <div className="grid grid-cols-2 gap-2.5">
                  <div className="group/stat p-3 bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-[1.01] relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-full -translate-y-6 translate-x-6 group-hover/stat:scale-110 transition-transform duration-500"></div>
                    <div className="flex items-center justify-between mb-1.5 relative z-10">
                      <div className="p-1.5 bg-white/20 rounded-lg">
                        <Key className="w-4 h-4" />
                      </div>
                      <Star className="w-3 h-3 opacity-70 group-hover/stat:opacity-100 transition-opacity duration-300" />
                    </div>
                    <div className="text-xl font-bold mb-0.5 relative z-10">{stats.apiKeyCount}</div>
                    <div className="text-xs opacity-90 relative z-10">API Keys</div>
                  </div>

                  <div className="group/stat p-3 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-[1.01] relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-full -translate-y-6 translate-x-6 group-hover/stat:scale-110 transition-transform duration-500"></div>
                    <div className="flex items-center justify-between mb-1.5 relative z-10">
                      <div className="p-1.5 bg-white/20 rounded-lg">
                        <Activity className="w-4 h-4" />
                      </div>
                      <TrendingUp className="w-3 h-3 opacity-70 group-hover/stat:opacity-100 transition-opacity duration-300" />
                    </div>
                    <div className="text-xl font-bold mb-0.5 relative z-10">{stats.totalCallCount.toLocaleString()}</div>
                    <div className="text-xs opacity-90 relative z-10">总调用</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      </div>
    </DashboardLayout>
  )
}
