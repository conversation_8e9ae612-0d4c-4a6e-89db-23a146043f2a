"use client"

import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { 
  CreditCard, 
  Star, 
  Zap, 
  Users, 
  Building, 
  Crown,
  Check,
  AlertCircle,
  Sparkles
} from "lucide-react"
import { toast } from "sonner"

export default function RechargePage() {
  const plans = [
    {
      name: "新客专项",
      price: "￥0",
      originalPrice: "￥10",
      description: "新用户专享",
      credits: 10,
      features: [
        "10积分",
        "所有模型权限",
        "技术支持",
        "试用尝鲜"
      ],
      cta: "立即获取",
      icon: Star,
      popular: false,
      color: "text-yellow-600"
    },
    {
      name: "入门套餐",
      price: "￥50",
      originalPrice: "￥50",
      description: "适合个人短期使用",
      credits: 50,
      features: [
        "50积分",
        "初级模型使用权限",
        "基础客户支持",
        "有效期90天"
      ],
      cta: "立即购买",
      icon: Zap,
      popular: false,
      color: "text-blue-600"
    },
    {
      name: "基础套餐",
      price: "￥100",
      originalPrice: "￥125",
      description: "适合个人笔试准备",
      credits: 125,
      features: [
        "125积分",
        "所有模型使用权限",
        "交流群资格",
        "永久有效"
      ],
      cta: "立即购买",
      icon: Users,
      popular: true,
      color: "text-green-600"
    },
    {
      name: "专业套餐",
      price: "￥200",
      originalPrice: "￥300",
      description: "适合长期使用",
      credits: 300,
      features: [
        "300积分",
        "所有模型使用权限",
        "交流群资格",
        "永久有效"
      ],
      cta: "立即购买",
      icon: Building,
      popular: false,
      color: "text-purple-600"
    },
    {
      name: "代理套餐",
      price: "￥500",
      originalPrice: "￥1000",
      description: "适合团队或代理",
      credits: 1000,
      features: [
        "1000积分",
        "所有模型使用权限",
        "代理权限",
        "永久有效"
      ],
      cta: "联系客服",
      icon: Crown,
      popular: false,
      color: "text-orange-600"
    }
  ]

  const handlePurchase = (plan: typeof plans[0]) => {
    if (plan.name === "新客专项") {
      toast.success("新客专项积分已添加到您的账户！")
    } else if (plan.name === "代理套餐") {
      toast.info("请联系客服获取代理套餐")
    } else {
      toast.info(`正在跳转到支付页面购买${plan.name}...`)
      // 这里可以集成实际的支付系统
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center">
              <CreditCard className="w-8 h-8 mr-3" />
              积分充值
            </h1>
            <p className="text-muted-foreground">
              选择适合您的积分套餐，享受AI编程辅助服务
            </p>
          </div>
        </div>

        {/* 重要提示 */}
        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-orange-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-orange-800 dark:text-orange-200">
                  温馨提示
                </h4>
                <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                  确保本地测试通过后，并且完全满足需求再进行充值，充值后积分不会退款。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 套餐卡片 */}
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
          {plans.map((plan, index) => {
            const IconComponent = plan.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card
                  className={`relative overflow-hidden h-full ${
                    plan.popular 
                      ? "border-primary shadow-lg ring-2 ring-primary/20" 
                      : "border-border/40 shadow-md"
                  } bg-gradient-to-b from-background to-muted/10 backdrop-blur hover:shadow-lg transition-all duration-300 ${
                    plan.popular ? "hover:-translate-y-1" : "hover:-translate-y-0.5"
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 right-0 bg-primary text-primary-foreground px-3 py-1 text-xs font-medium rounded-bl-lg flex items-center">
                      <Sparkles className="w-3 h-3 mr-1" />
                      推荐套餐
                    </div>
                  )}
                  
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="flex items-center mb-4">
                      <div className={`p-2 rounded-lg bg-muted/50 ${plan.color}`}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <div className="ml-3">
                        <h3 className="font-bold text-lg">{plan.name}</h3>
                        <Badge variant="outline" className="text-xs">
                          {plan.credits}积分
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex items-baseline mb-2">
                      <span className="text-3xl font-bold">{plan.price}</span>
                      {plan.originalPrice && plan.originalPrice !== plan.price && (
                        <span className="text-sm text-muted-foreground ml-2 line-through">
                          {plan.originalPrice}
                        </span>
                      )}
                    </div>
                    <p className="text-muted-foreground text-sm mb-4">{plan.description}</p>
                    
                    <div className="h-px w-full bg-border/50 my-4"></div>
                    
                    <ul className="space-y-3 mb-6 flex-grow">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm">
                          <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3 flex-shrink-0">
                            <Check className="w-3 h-3" />
                          </div>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                    
                    <Button
                      onClick={() => handlePurchase(plan)}
                      className={`w-full mt-auto rounded-full ${
                        plan.popular ? "bg-primary hover:bg-primary/90" : ""
                      } transition-all`}
                      variant={plan.popular ? "default" : "outline"}
                    >
                      {plan.cta}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>
    </DashboardLayout>
  )
}
