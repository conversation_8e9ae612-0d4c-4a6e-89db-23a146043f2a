"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { ApiKeyService, ApiKeyDto } from "@/lib/api"
import {
  Plus,
  Key,
  Eye,
  Copy,
  EyeOff,
  Edit,
  Mic,
  Calendar,
  Shield,
  AlertTriangle,
  Coins
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tooltip"
import { toast } from "sonner"
import DashboardLayout from "@/components/dashboard/dashboard-layout"

// 扩展API返回的类型
interface ApiKey extends ApiKeyDto {
  name?: string
  description?: string
  lastUsed?: string
  status: "active" | "inactive"
  permissions?: string[]
  key: string // keyValue的别名
}

interface User {
  id: number
  email: string
  name: string
  remainingCalls?: number
  totalCalls?: number
}

export default function ApiKeysPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showVoiceDialog, setShowVoiceDialog] = useState(false)
  const [showVoiceEditDialog, setShowVoiceEditDialog] = useState(false)
  const [selectedKey, setSelectedKey] = useState<ApiKey | null>(null)
  const [visibleKeys, setVisibleKeys] = useState<Set<number>>(new Set())
  const [user, setUser] = useState<User | null>(null)
  const [newKey, setNewKey] = useState({
    name: "",
    description: "",
    permissions: ["read", "write"],
    remainingCalls: 0
  })
  const [editKey, setEditKey] = useState({
    keyName: "",
    remainingCalls: 0,
    active: true
  })
  const [voiceConfig, setVoiceConfig] = useState({
    voiceAppId: "",
    voiceAccessKeyId: ""
  })
  const [voiceEditConfig, setVoiceEditConfig] = useState({
    voiceEnabled: false,
    voiceAppId: "",
    voiceAccessKeyId: ""
  })
  const router = useRouter()

  useEffect(() => {
    // 检查登录状态
    const token = localStorage.getItem("token")
    const userData = localStorage.getItem("user")

    if (!token || !userData) {
      router.push("/login")
      return
    }

    try {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)
      // 初始化创建表单的积分字段
      setNewKey(prev => ({ ...prev, remainingCalls: parsedUser.remainingCalls || 0 }))
    } catch (error) {
      console.error("解析用户数据失败:", error)
      router.push("/login")
      return
    }

    // 加载API Keys
    loadApiKeys()
  }, [router])

  const loadApiKeys = async () => {
    try {
      const response = await ApiKeyService.getApiKeys()

      if (response.code === 0 && response.data) {
        // 转换数据格式以适配现有UI
        const transformedKeys: ApiKey[] = response.data.map(key => ({
          ...key,
          key: key.keyValue,
          name: key.keyName || `API Key ${key.id}`,
          description: `Created at ${new Date(key.createdAt).toLocaleDateString()}`,
          status: key.active ? "active" : "inactive" as const,
          permissions: ["read", "write"],
          lastUsed: undefined
        }))
        setApiKeys(transformedKeys)

        // 更新用户积分信息
        if (response.userInfo) {
          const currentUser = user || JSON.parse(localStorage.getItem("user") || "{}")
          const updatedUser = {
            ...currentUser,
            remainingCalls: response.userInfo.remainingCalls,
            totalCalls: response.userInfo.totalCalls
          }
          setUser(updatedUser)
          // 同时更新localStorage中的用户信息
          localStorage.setItem("user", JSON.stringify(updatedUser))
        }
      } else {
        toast.error(response.message || "加载API Keys失败")
      }
    } catch (error) {
      toast.error("网络错误: " + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const validateCreateCredits = (credits: number): string | null => {
    if (!user) return "用户数据错误"

    if (credits < 0) {
      return "积分不能为负数"
    }

    if (credits > (user.remainingCalls || 0)) {
      return `积分不能超过用户剩余积分，当前剩余: ${user.remainingCalls || 0}`
    }

    return null
  }

  const createApiKey = async () => {
    try {
      // 校验用户是否有权限创建
      if (!canCreateApiKey(user)) {
        toast.error("剩余积分不足，请先充值")
        return
      }

      // 校验积分
      const creditError = validateCreateCredits(newKey.remainingCalls)
      if (creditError) {
        toast.error(creditError)
        return
      }

      // 校验名称
      if (!newKey.name.trim()) {
        toast.error("请输入API Key名称")
        return
      }

      const response = await ApiKeyService.createApiKey(newKey.name.trim(), newKey.remainingCalls)

      if (response.code === 0 && response.data) {
        // 转换新创建的API Key格式
        const newApiKey: ApiKey = {
          ...response.data,
          key: response.data.keyValue,
          name: newKey.name || response.data.keyName || `API Key ${response.data.id}`,
          description: newKey.description || `Created at ${new Date(response.data.createdAt).toLocaleDateString()}`,
          status: response.data.active ? "active" : "inactive" as const,
          permissions: newKey.permissions,
          lastUsed: undefined
        }

        setApiKeys([...apiKeys, newApiKey])

        // 更新用户积分
        let updatedUser = user
        if (user) {
          updatedUser = {
            ...user,
            remainingCalls: (user.remainingCalls || 0) - newKey.remainingCalls
          }
          setUser(updatedUser)
          localStorage.setItem("user", JSON.stringify(updatedUser))
        }

        setShowCreateDialog(false)
        setNewKey({ name: "", description: "", permissions: ["read", "write"], remainingCalls: updatedUser?.remainingCalls || 0 })
        toast.success(response.message || "API Key创建成功")
      } else {
        toast.error(response.message || "创建失败")
      }
    } catch (error) {
      toast.error("网络错误: " + (error as Error).message)
    }
  }


  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success("已复制到剪贴板")
  }


  const toggleKeyVisibility = (keyId: number) => {
    const newVisibleKeys = new Set(visibleKeys)
    if (newVisibleKeys.has(keyId)) {
      newVisibleKeys.delete(keyId)
    } else {
      newVisibleKeys.add(keyId)
    }
    setVisibleKeys(newVisibleKeys)
  }

  const validateCredits = (newCredits: number): string | null => {
    if (!selectedKey || !user) return "数据错误"

    // 检查积分不能为负数
    if (newCredits < 0) {
      return "积分不能为负数"
    }

    // 计算积分变化
    const creditsChange = newCredits - selectedKey.remainingCalls

    // 如果是增加积分，检查用户剩余积分是否足够
    if (creditsChange > 0 && (user.remainingCalls || 0) < creditsChange) {
      return `用户剩余积分不足，最多可增加 ${user.remainingCalls || 0} 积分`
    }

    return null
  }

  const saveApiKeyChanges = async () => {
    if (!selectedKey) return

    try {
      // 校验积分
      const creditError = validateCredits(editKey.remainingCalls)
      if (creditError) {
        toast.error(creditError)
        return
      }

      // 计算积分变化
      const creditsChange = editKey.remainingCalls - selectedKey.remainingCalls

      const response = await ApiKeyService.updateApiKeyAdvanced(selectedKey.id.toString(), {
        keyName: editKey.keyName.trim() || undefined,
        remainingCalls: editKey.remainingCalls,
        active: editKey.active
      })

      if (response.code === 0 && response.data) {
        // 更新本地数据
        const updatedKeys = apiKeys.map(key =>
          key.id === selectedKey.id
            ? { ...key, ...response.data!, key: response.data!.keyValue, name: response.data!.keyName }
            : key
        )
        setApiKeys(updatedKeys)

        // 更新用户积分
        if (creditsChange !== 0 && user) {
          const updatedUser = {
            ...user,
            remainingCalls: (user.remainingCalls || 0) - creditsChange
          }
          setUser(updatedUser)
          localStorage.setItem("user", JSON.stringify(updatedUser))
        }

        setShowEditDialog(false)
        setSelectedKey(null)
        toast.success("API Key更新成功")
      } else {
        toast.error(response.message || "更新失败")
      }
    } catch (error) {
      toast.error("网络错误: " + (error as Error).message)
    }
  }

  const canEnableVoice = (user: User | null): boolean => {
    return (user?.totalCalls || 0) > 125
  }

  const canCreateApiKey = (user: User | null): boolean => {
    return (user?.remainingCalls || 0) > 0
  }

  const enableVoice = async () => {
    if (!selectedKey) return

    try {
      // 校验权限
      if (!canEnableVoice(user)) {
        toast.error("累计充值需大于100才能开通语音功能")
        return
      }

      // 校验必填字段
      if (!voiceConfig.voiceAppId.trim()) {
        toast.error("请输入语音AppId")
        return
      }
      if (!voiceConfig.voiceAccessKeyId.trim()) {
        toast.error("请输入语音AccessKeyId")
        return
      }

      const response = await ApiKeyService.enableVoice(selectedKey.id.toString(), {
        voiceAppId: voiceConfig.voiceAppId.trim(),
        voiceAccessKeyId: voiceConfig.voiceAccessKeyId.trim()
      })

      if (response.code === 0 && response.data) {
        // 更新本地数据
        const updatedKeys = apiKeys.map(key =>
          key.id === selectedKey.id
            ? { ...key, ...response.data!, key: response.data!.keyValue, name: response.data!.keyName }
            : key
        )
        setApiKeys(updatedKeys)

        setShowVoiceDialog(false)
        setSelectedKey(null)
        setVoiceConfig({ voiceAppId: "", voiceAccessKeyId: "" })
        toast.success("语音功能开通成功")
      } else {
        toast.error(response.message || "开通失败")
      }
    } catch (error) {
      toast.error("网络错误: " + (error as Error).message)
    }
  }



  const saveVoiceEditChanges = async () => {
    if (!selectedKey) return

    try {
      // 如果启用语音功能，校验必填字段
      if (voiceEditConfig.voiceEnabled) {
        if (!voiceEditConfig.voiceAppId.trim()) {
          toast.error("请输入语音AppId")
          return
        }
        if (!voiceEditConfig.voiceAccessKeyId.trim()) {
          toast.error("请输入语音AccessKeyId")
          return
        }
      }

      const response = await ApiKeyService.updateApiKeyAdvanced(selectedKey.id.toString(), {
        voiceEnabled: voiceEditConfig.voiceEnabled,
        voiceAppId: voiceEditConfig.voiceEnabled ? voiceEditConfig.voiceAppId.trim() : undefined,
        voiceAccessKeyId: voiceEditConfig.voiceEnabled ? voiceEditConfig.voiceAccessKeyId.trim() : undefined
      })

      if (response.code === 0 && response.data) {
        // 更新本地数据
        const updatedKeys = apiKeys.map(key =>
          key.id === selectedKey.id
            ? { ...key, ...response.data!, key: response.data!.keyValue, name: response.data!.keyName }
            : key
        )
        setApiKeys(updatedKeys)

        setShowVoiceEditDialog(false)
        setSelectedKey(null)
        setVoiceEditConfig({ voiceEnabled: false, voiceAppId: "", voiceAccessKeyId: "" })
        toast.success("语音配置更新成功")
      } else {
        toast.error(response.message || "更新失败")
      }
    } catch (error) {
      toast.error("网络错误: " + (error as Error).message)
    }
  }

  const formatKey = (key: string, isVisible: boolean) => {
    if (isVisible) {
      return key
    }
    return `sk-${"*".repeat(20)}${key.slice(-4)}`
  }

  const formatExpiryDate = (expiresAt?: string) => {
    if (!expiresAt) {
      return "永不过期"
    }

    const expiryDate = new Date(expiresAt)
    const now = new Date()
    const isExpired = expiryDate < now
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

    return {
      date: expiryDate.toLocaleDateString(),
      fullDate: expiryDate.toLocaleString(),
      isExpired,
      daysUntilExpiry,
      isExpiringSoon: daysUntilExpiry <= 7 && daysUntilExpiry > 0
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 pb-6">
        {/* 页面标题 */}
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20 p-6 border border-blue-100 dark:border-blue-800/30">
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-200/30 to-purple-200/30 dark:from-blue-400/20 dark:to-purple-400/20 rounded-full -translate-y-10 translate-x-10"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-indigo-200/30 to-blue-200/30 dark:from-indigo-400/20 dark:to-blue-400/20 rounded-full translate-y-8 -translate-x-8"></div>

          <div className="relative flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <Key className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                <h1 className="text-2xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                  API Keys 管理
                </h1>
              </div>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                管理您的API访问密钥，控制积分分配
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {/* 用户积分显示 */}
              <div className="flex items-center space-x-2 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm px-4 py-2 rounded-lg border border-white/20 dark:border-gray-700/30 shadow-sm">
                <Coins className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <div className="text-sm">
                  <span className="font-medium text-gray-700 dark:text-gray-300">剩余积分: </span>
                  <span className="text-blue-600 dark:text-blue-400 font-bold">{user?.remainingCalls || 0}</span>
                  <span className="text-gray-500 dark:text-gray-400 ml-1">/ {user?.totalCalls || 0}</span>
                </div>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => canCreateApiKey(user) && setShowCreateDialog(true)}
                      disabled={!canCreateApiKey(user)}
                      className={`${
                        canCreateApiKey(user)
                          ? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-700 dark:hover:to-blue-800 text-white"
                          : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      } border-0 shadow-sm hover:shadow-md transition-all duration-300`}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      创建API Key
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{canCreateApiKey(user) ? "创建新的API Key" : "剩余积分不足，请先充值"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid gap-3 md:grid-cols-3">
          <Card className="relative overflow-hidden border-0 shadow-md hover:shadow-lg transition-all duration-300 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800/30 border group">
            <div className="absolute top-0 right-0 w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 opacity-10 dark:opacity-20 rounded-full -translate-y-7 translate-x-7 group-hover:opacity-20 dark:group-hover:opacity-30 transition-opacity duration-300"></div>

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5">
              <CardTitle className="text-xs font-medium text-gray-600 dark:text-gray-300">总计</CardTitle>
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 shadow-sm group-hover:shadow-md transition-shadow duration-300">
                <Key className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="relative pt-0 pb-3">
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">{apiKeys.length}</div>
              <p className="text-xs text-gray-500 dark:text-gray-400">API Keys</p>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-md hover:shadow-lg transition-all duration-300 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800/30 border group">
            <div className="absolute top-0 right-0 w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 opacity-10 dark:opacity-20 rounded-full -translate-y-7 translate-x-7 group-hover:opacity-20 dark:group-hover:opacity-30 transition-opacity duration-300"></div>

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5">
              <CardTitle className="text-xs font-medium text-gray-600 dark:text-gray-300">活跃</CardTitle>
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 shadow-sm group-hover:shadow-md transition-shadow duration-300">
                <Shield className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="relative pt-0 pb-3">
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                {apiKeys.filter(key => key.status === "active").length}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">正在使用中</p>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-md hover:shadow-lg transition-all duration-300 bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800/30 border group">
            <div className="absolute top-0 right-0 w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-600 opacity-10 dark:opacity-20 rounded-full -translate-y-7 translate-x-7 group-hover:opacity-20 dark:group-hover:opacity-30 transition-opacity duration-300"></div>

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5">
              <CardTitle className="text-xs font-medium text-gray-600 dark:text-gray-300">非活跃</CardTitle>
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 dark:from-orange-600 dark:to-orange-700 shadow-sm group-hover:shadow-md transition-shadow duration-300">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="relative pt-0 pb-3">
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                {apiKeys.filter(key => key.status === "inactive").length}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">未使用或已停用</p>
            </CardContent>
          </Card>
        </div>

        {/* API Keys 列表 */}
        <Card className="border-0 shadow-md bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900/50">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg dark:text-gray-100">
              <div className="p-1.5 bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 rounded-lg mr-2 shadow-sm">
                <Key className="w-4 h-4 text-white" />
              </div>
              API Keys 列表
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-300">
              您创建的所有API访问密钥，可以进行编辑和管理
            </CardDescription>
          </CardHeader>
          <CardContent>
            {apiKeys.length === 0 ? (
              <div className="text-center py-16">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-2xl flex items-center justify-center shadow-lg mx-auto mb-6">
                  <Key className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">暂无API Keys</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
                  {canCreateApiKey(user)
                    ? "创建您的第一个API Key来开始使用我们的服务，享受强大的API功能"
                    : "您当前的积分余额为0，请先充值获取积分后再创建API Key"
                  }
                </p>
                {!canCreateApiKey(user) && (
                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mb-6 max-w-md mx-auto">
                    <div className="flex items-center justify-center space-x-2 text-orange-700 dark:text-orange-300">
                      <AlertTriangle className="w-5 h-5" />
                      <span className="font-medium">积分不足</span>
                    </div>
                    <p className="text-sm text-orange-600 dark:text-orange-400 mt-2">
                      当前剩余积分：{user?.remainingCalls || 0}
                    </p>
                  </div>
                )}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        disabled={!canCreateApiKey(user)}
                        className={`${
                          canCreateApiKey(user)
                            ? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-700 dark:hover:to-blue-800 text-white"
                            : "bg-gray-300 text-gray-500 cursor-not-allowed"
                        } border-0 shadow-md hover:shadow-lg transition-all duration-300`}
                        onClick={() => canCreateApiKey(user) && setShowCreateDialog(true)}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        创建第一个API Key
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{canCreateApiKey(user) ? "创建您的第一个API Key" : "剩余积分不足，请先充值"}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>API Key</TableHead>
                    <TableHead>剩余积分</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>语音功能</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>过期时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {apiKeys.map((apiKey) => (
                    <TableRow key={apiKey.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full"></div>
                          <div className="font-medium text-gray-900 dark:text-gray-100">{apiKey.name}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <code className="text-sm bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 px-3 py-1.5 rounded-lg font-mono border dark:border-gray-600 dark:text-gray-200">
                            {formatKey(apiKey.key, visibleKeys.has(apiKey.id))}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 hover:bg-blue-50 dark:hover:bg-blue-900/30 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                            onClick={() => toggleKeyVisibility(apiKey.id)}
                          >
                            {visibleKeys.has(apiKey.id) ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 hover:bg-green-50 dark:hover:bg-green-900/30 hover:text-green-600 dark:hover:text-green-400 transition-colors duration-200"
                            onClick={() => copyToClipboard(apiKey.key)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 bg-blue-50 px-3 py-1.5 rounded-lg w-fit">
                          <Coins className="w-4 h-4 text-blue-600" />
                          <span className="font-medium text-gray-900">{apiKey.remainingCalls}</span>
                          <span className="text-gray-500">/ {apiKey.totalCalls}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={apiKey.status === "active"
                            ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0 shadow-sm"
                            : "bg-gradient-to-r from-gray-400 to-gray-500 text-white border-0 shadow-sm"
                          }
                        >
                          {apiKey.status === "active" ? "启用" : "停用"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={apiKey.voiceEnabled
                            ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-sm"
                            : "bg-gradient-to-r from-gray-400 to-gray-500 text-white border-0 shadow-sm"
                          }
                        >
                          {apiKey.voiceEnabled ? "启用" : "停用"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center bg-gray-50 px-3 py-1.5 rounded-lg w-fit">
                          <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-700">
                            {new Date(apiKey.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const expiryInfo = formatExpiryDate(apiKey.expiresAt)
                          if (typeof expiryInfo === 'string') {
                            return (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center bg-blue-50 dark:bg-blue-900/20 px-3 py-1.5 rounded-lg w-fit cursor-help">
                                      <Calendar className="mr-2 h-4 w-4 text-blue-500 dark:text-blue-400" />
                                      <span className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                                        {expiryInfo}
                                      </span>
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>此API Key永不过期</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )
                          } else {
                            const bgColor = expiryInfo.isExpired
                              ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                              : expiryInfo.isExpiringSoon
                              ? 'bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800'
                              : 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'

                            const textColor = expiryInfo.isExpired
                              ? 'text-red-700 dark:text-red-300'
                              : expiryInfo.isExpiringSoon
                              ? 'text-orange-700 dark:text-orange-300'
                              : 'text-green-700 dark:text-green-300'

                            const iconColor = expiryInfo.isExpired
                              ? 'text-red-500 dark:text-red-400'
                              : expiryInfo.isExpiringSoon
                              ? 'text-orange-500 dark:text-orange-400'
                              : 'text-green-500 dark:text-green-400'

                            return (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className={`flex items-center px-3 py-1.5 rounded-lg w-fit cursor-help ${bgColor}`}>
                                      <Calendar className={`mr-2 h-4 w-4 ${iconColor}`} />
                                      <span className={`text-sm font-medium ${textColor}`}>
                                        {expiryInfo.date}
                                      </span>
                                      {expiryInfo.isExpired && (
                                        <Badge className="ml-2 bg-red-500 text-white text-xs">
                                          已过期
                                        </Badge>
                                      )}
                                      {expiryInfo.isExpiringSoon && (
                                        <Badge className="ml-2 bg-orange-500 text-white text-xs">
                                          即将过期
                                        </Badge>
                                      )}
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <div className="text-sm">
                                      <p className="font-medium">过期时间: {expiryInfo.fullDate}</p>
                                      {expiryInfo.isExpired ? (
                                        <p className="text-red-400 mt-1">此API Key已过期，无法使用</p>
                                      ) : expiryInfo.isExpiringSoon ? (
                                        <p className="text-orange-400 mt-1">还有 {expiryInfo.daysUntilExpiry} 天过期</p>
                                      ) : (
                                        <p className="text-green-400 mt-1">还有 {expiryInfo.daysUntilExpiry} 天过期</p>
                                      )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )
                          }
                        })()}
                      </TableCell>

                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          {!apiKey.voiceEnabled && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    disabled={!canEnableVoice(user)}
                                    className={`${
                                      canEnableVoice(user)
                                        ? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
                                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                                    } border-0 shadow-sm hover:shadow-md transition-all duration-300`}
                                    onClick={() => {
                                      if (canEnableVoice(user)) {
                                        setSelectedKey(apiKey)
                                        setVoiceConfig({ voiceAppId: "", voiceAccessKeyId: "" })
                                        setShowVoiceDialog(true)
                                      }
                                    }}
                                  >
                                    <Mic className="mr-2 h-4 w-4" />
                                    语音开通
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{canEnableVoice(user) ? "开通语音功能" : "累计充值大于100可开通语音功能"}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                          {apiKey.voiceEnabled && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white border-0 shadow-sm hover:shadow-md transition-all duration-300"
                              onClick={() => {
                                setSelectedKey(apiKey)
                                setVoiceEditConfig({
                                  voiceEnabled: apiKey.voiceEnabled,
                                  voiceAppId: apiKey.voiceAppId || "",
                                  voiceAccessKeyId: apiKey.voiceAccessKeyId || ""
                                })
                                setShowVoiceEditDialog(true)
                              }}
                            >
                              <Mic className="mr-2 h-4 w-4" />
                              语音编辑
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white border-0 shadow-sm hover:shadow-md transition-all duration-300"
                            onClick={() => {
                                setSelectedKey(apiKey)
                                // 回填当前key的信息
                                setEditKey({
                                  keyName: apiKey.keyName || apiKey.name || "",
                                  remainingCalls: apiKey.remainingCalls,
                                  active: apiKey.active
                                })
                                setShowEditDialog(true)
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              编辑
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 创建API Key对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-0 shadow-xl">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center text-xl dark:text-gray-100">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-lg mr-3 shadow-md">
                <Plus className="w-5 h-5 text-white" />
              </div>
              创建新的API Key
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-300">
              为您的应用程序创建一个新的API访问密钥，开始使用我们的服务
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">名称 *</Label>
              <Input
                id="name"
                placeholder="例如：生产环境API"
                value={newKey.name}
                onChange={(e) => setNewKey({ ...newKey, name: e.target.value })}
                className="border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="credits" className="text-sm font-medium text-gray-700 dark:text-gray-300">积分分配 *</Label>
              <Input
                id="credits"
                type="number"
                placeholder="输入要分配的积分"
                value={newKey.remainingCalls}
                onChange={(e) => setNewKey({ ...newKey, remainingCalls: parseInt(e.target.value) || 0 })}
                className={`${validateCreateCredits(newKey.remainingCalls) ? "border-red-500 focus:border-red-500 focus:ring-red-500" : "border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500"} dark:bg-gray-700 dark:text-gray-100`}
              />
              <div className="space-y-1">
                <div className="flex items-center justify-between text-xs bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-lg">
                  <span className="text-gray-600 dark:text-gray-300">用户剩余积分:</span>
                  <span className="font-medium text-blue-600 dark:text-blue-400">{user?.remainingCalls || 0}</span>
                </div>
                {validateCreateCredits(newKey.remainingCalls) && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-2">
                    <p className="text-xs text-red-600 dark:text-red-400 font-medium">
                      {validateCreateCredits(newKey.remainingCalls)}
                    </p>
                  </div>
                )}
                {!canCreateApiKey(user) && (
                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-orange-500 dark:bg-orange-400 rounded-full mt-0.5 flex-shrink-0"></div>
                      <div className="text-sm text-orange-700 dark:text-orange-300">
                        <p className="font-medium mb-1">积分不足提醒：</p>
                        <p className="text-xs leading-relaxed">
                          您当前的剩余积分为0，无法创建API Key。请先充值获取积分后再创建。
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 dark:text-gray-300"
            >
              取消
            </Button>
            <Button
              onClick={createApiKey}
              disabled={!canCreateApiKey(user)}
              className={`${
                canCreateApiKey(user)
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-700 dark:hover:to-blue-800 text-white"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              } border-0 shadow-sm hover:shadow-md transition-all duration-300`}
            >
              <Plus className="w-4 h-4 mr-2" />
              创建
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 修改API Key对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-md bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-0 shadow-xl">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center text-xl dark:text-gray-100">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 rounded-lg mr-3 shadow-md">
                <Edit className="w-5 h-5 text-white" />
              </div>
              编辑API Key
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-300">
              修改API Key的配置信息和积分分配
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name" className="text-sm font-medium text-gray-700 dark:text-gray-300">Key名称</Label>
              <Input
                id="edit-name"
                placeholder="输入Key名称"
                value={editKey.keyName}
                onChange={(e) => setEditKey({ ...editKey, keyName: e.target.value })}
                className="border-gray-200 focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-credits" className="text-sm font-medium text-gray-700">剩余积分</Label>
              <Input
                id="edit-credits"
                type="number"
                placeholder="输入剩余积分"
                value={editKey.remainingCalls}
                onChange={(e) => setEditKey({ ...editKey, remainingCalls: parseInt(e.target.value) || 0 })}
                className={`${validateCredits(editKey.remainingCalls) ? "border-red-500 focus:border-red-500 focus:ring-red-500" : "border-gray-200 focus:border-indigo-500 focus:ring-indigo-500"}`}
              />
              <div className="space-y-1">
                <div className="flex items-center justify-between text-xs bg-blue-50 px-3 py-2 rounded-lg">
                  <span className="text-gray-600">原剩余积分:</span>
                  <span className="font-medium text-blue-600">{selectedKey?.remainingCalls}</span>
                </div>
                <div className="flex items-center justify-between text-xs bg-green-50 px-3 py-2 rounded-lg">
                  <span className="text-gray-600">用户剩余积分:</span>
                  <span className="font-medium text-green-600">{user?.remainingCalls || 0}</span>
                </div>
                {validateCredits(editKey.remainingCalls) && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-2">
                    <p className="text-xs text-red-600 font-medium">
                      {validateCredits(editKey.remainingCalls)}
                    </p>
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="edit-active" className="text-sm font-medium text-gray-700 dark:text-gray-300">启用状态</Label>
                <Switch
                  id="edit-active"
                  checked={editKey.active}
                  onCheckedChange={(checked) => setEditKey({ ...editKey, active: checked })}
                />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {editKey.active ? "API Key当前已启用，可以正常使用" : "API Key当前已停用，无法使用"}
              </p>
            </div>

          </div>
          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              onClick={() => setShowEditDialog(false)}
              className="border-gray-300 hover:bg-gray-50"
            >
              取消
            </Button>
            <Button
              onClick={saveApiKeyChanges}
              className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white border-0 shadow-sm hover:shadow-md transition-all duration-300"
            >
              <Edit className="w-4 h-4 mr-2" />
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 语音开通对话框 */}
      <Dialog open={showVoiceDialog} onOpenChange={setShowVoiceDialog}>
        <DialogContent className="max-w-md bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-0 shadow-xl">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center text-xl dark:text-gray-100">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 rounded-lg mr-3 shadow-md">
                <Mic className="w-5 h-5 text-white" />
              </div>
              语音功能开通
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-300">
              开通语音功能需要配置语音服务的相关参数
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="voice-app-id" className="text-sm font-medium text-gray-700 dark:text-gray-300">语音AppId *</Label>
              <Input
                id="voice-app-id"
                placeholder="请输入语音AppId"
                value={voiceConfig.voiceAppId}
                onChange={(e) => setVoiceConfig({ ...voiceConfig, voiceAppId: e.target.value })}
                className="border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="voice-access-key" className="text-sm font-medium text-gray-700 dark:text-gray-300">语音AccessKeyId *</Label>
              <Input
                id="voice-access-key"
                placeholder="请输入语音AccessKeyId"
                value={voiceConfig.voiceAccessKeyId}
                onChange={(e) => setVoiceConfig({ ...voiceConfig, voiceAccessKeyId: e.target.value })}
                className="border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="w-4 h-4 bg-blue-500 dark:bg-blue-400 rounded-full mt-0.5 flex-shrink-0"></div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  <p className="font-medium mb-1">开通说明：</p>
                  <p className="text-xs leading-relaxed">
                    语音功能需要配置第三方语音服务的AppId和AccessKeyId。
                    <a
                      href="https://example.com/voice-setup"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 underline ml-1"
                    >
                      查看详细配置说明 →
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              onClick={() => setShowVoiceDialog(false)}
              className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 dark:text-gray-300"
            >
              取消
            </Button>
            <Button
              onClick={enableVoice}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-700 dark:hover:to-blue-800 text-white border-0 shadow-sm hover:shadow-md transition-all duration-300"
            >
              <Mic className="w-4 h-4 mr-2" />
              开通语音功能
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 语音编辑对话框 */}
      <Dialog open={showVoiceEditDialog} onOpenChange={setShowVoiceEditDialog}>
        <DialogContent className="max-w-md bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-0 shadow-xl">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center text-xl dark:text-gray-100">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-indigo-600 dark:from-purple-600 dark:to-indigo-700 rounded-lg mr-3 shadow-md">
                <Edit className="w-5 h-5 text-white" />
              </div>
              语音功能编辑
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-300">
              编辑语音功能的配置信息和启停用状态
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="voice-edit-enabled" className="text-sm font-medium text-gray-700 dark:text-gray-300">语音功能</Label>
                <Switch
                  id="voice-edit-enabled"
                  checked={voiceEditConfig.voiceEnabled}
                  onCheckedChange={(checked) => setVoiceEditConfig({ ...voiceEditConfig, voiceEnabled: checked })}
                />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {voiceEditConfig.voiceEnabled ? "语音功能已启用" : "语音功能已停用"}
              </p>
            </div>
            {voiceEditConfig.voiceEnabled && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="voice-edit-app-id" className="text-sm font-medium text-gray-700 dark:text-gray-300">语音AppId *</Label>
                  <Input
                    id="voice-edit-app-id"
                    placeholder="请输入语音AppId"
                    value={voiceEditConfig.voiceAppId}
                    onChange={(e) => setVoiceEditConfig({ ...voiceEditConfig, voiceAppId: e.target.value })}
                    className="border-gray-200 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-gray-100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="voice-edit-access-key" className="text-sm font-medium text-gray-700 dark:text-gray-300">语音AccessKeyId *</Label>
                  <Input
                    id="voice-edit-access-key"
                    placeholder="请输入语音AccessKeyId"
                    value={voiceEditConfig.voiceAccessKeyId}
                    onChange={(e) => setVoiceEditConfig({ ...voiceEditConfig, voiceAccessKeyId: e.target.value })}
                    className="border-gray-200 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-gray-100"
                  />
                </div>
                          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="w-4 h-4 bg-blue-500 dark:bg-blue-400 rounded-full mt-0.5 flex-shrink-0"></div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  <p className="font-medium mb-1">开通说明：</p>
                  <p className="text-xs leading-relaxed">
                    语音功能需要配置第三方语音服务的AppId和AccessKeyId。
                    <a
                      href="https://example.com/voice-setup"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 underline ml-1"
                    >
                      查看详细配置说明 →
                    </a>
                  </p>
                </div>
              </div>
            </div>
              </>
            )}
          </div>
          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              onClick={() => setShowVoiceEditDialog(false)}
              className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 dark:text-gray-300"
            >
              取消
            </Button>
            <Button
              onClick={saveVoiceEditChanges}
              className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 dark:from-purple-600 dark:to-purple-700 dark:hover:from-purple-700 dark:hover:to-purple-800 text-white border-0 shadow-sm hover:shadow-md transition-all duration-300"
            >
              <Edit className="w-4 h-4 mr-2" />
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </DashboardLayout>
  )
}
