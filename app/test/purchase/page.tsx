"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import PurchaseModal from "@/components/purchase-modal"

export default function PurchaseTestPage() {
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false)

  const testPlan = {
    name: "测试套餐",
    price: "￥10",
    credits: 100,
    features: [
      "100积分",
      "所有模型权限",
      "技术支持",
      "测试功能"
    ]
  }

  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>购买功能测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold">{testPlan.name}</h3>
              <p className="text-2xl font-bold text-primary">{testPlan.price}</p>
              <p className="text-sm text-muted-foreground">{testPlan.credits}积分</p>
            </div>
            
            <Button 
              onClick={() => setIsPurchaseModalOpen(true)}
              className="w-full"
            >
              测试购买
            </Button>
          </div>
        </CardContent>
      </Card>

      <PurchaseModal
        isOpen={isPurchaseModalOpen}
        onClose={() => setIsPurchaseModalOpen(false)}
        plan={testPlan}
      />
    </div>
  )
}
