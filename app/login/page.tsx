"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Mail, ArrowRight, CheckCircle, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { AuthService } from "@/lib/api"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [step, setStep] = useState<"email" | "verification">("email")
  const [isLoading, setIsLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const router = useRouter()
  const { login } = useAuth()

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  // 发送验证码
  const sendVerificationCode = async () => {
    if (!email) {
      setError("请输入邮箱地址")
      return
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setError("请输入有效的邮箱地址")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // 调用Java后端API
      const response = await AuthService.sendCode(email)

      if (response.code === 0) {
        setStep("verification")
        setCountdown(60)
        setSuccess(response.message || "验证码已发送到您的邮箱")
      } else {
        setError(response.message || "发送验证码失败")
      }
    } catch (error) {
      setError("网络错误，请稍后重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 验证登录
  const verifyLogin = async () => {
    if (!verificationCode) {
      setError("请输入验证码")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // 调用Java后端API
      const response = await AuthService.verifyLogin(email, verificationCode)

      if (response.code === 0 && response.token && response.user) {
        // 使用认证上下文保存登录状态
        login(response.token, response.user)

        setSuccess("登录成功，正在跳转...")
        setTimeout(() => {
          router.push("/dashboard")
        }, 1500)
      } else {
        setError(response.message || "验证码错误")
      }
    } catch (error) {
      setError("网络错误，请稍后重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 重新发送验证码
  const resendCode = () => {
    setCountdown(60)
    sendVerificationCode()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center p-4">
      <div className="absolute inset-0 -z-10 h-full w-full bg-white dark:bg-black bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#1f1f1f_1px,transparent_1px),linear-gradient(to_bottom,#1f1f1f_1px,transparent_1px)] bg-[size:4rem_4rem] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div>
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-lg"
      >
        <Card className="border-border/40 bg-card/80 backdrop-blur-sm shadow-xl">
          <CardHeader className="text-center space-y-6 py-8">
            <div className="mx-auto size-16 rounded-lg bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center text-primary-foreground text-2xl font-bold">
              C
            </div>
            <div>
              <CardTitle className="text-3xl font-bold">欢迎登录</CardTitle>
              <CardDescription className="text-muted-foreground text-base mt-3">
                {step === "email" ? "请输入您的邮箱地址" : "请输入验证码"}
              </CardDescription>

            </div>
          </CardHeader>

          <CardContent className="space-y-8 px-8 pb-8">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}

            {step === "email" ? (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div className="space-y-3">
                  <Label htmlFor="email" className="text-base font-medium">邮箱地址</Label>
                  <div className="relative">
                    <Mail className="absolute left-4 top-4 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="请输入您的邮箱"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-12 h-12 text-base"
                      onKeyDown={(e) => e.key === "Enter" && sendVerificationCode()}
                    />
                  </div>
                </div>

                <Button
                  onClick={sendVerificationCode}
                  disabled={isLoading}
                  className="w-full h-12 text-base"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      发送中...
                    </>
                  ) : (
                    <>
                      发送验证码
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div className="space-y-3">
                  <Label htmlFor="code" className="text-base font-medium">验证码</Label>
                  <Input
                    id="code"
                    type="text"
                    placeholder="请输入6位验证码"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    maxLength={6}
                    className="h-12 text-base text-center tracking-widest"
                    onKeyDown={(e) => e.key === "Enter" && verifyLogin()}
                  />
                  <div className="text-sm text-muted-foreground">
                    验证码已发送至：{email}
                  </div>
                </div>

                <Button
                  onClick={verifyLogin}
                  disabled={isLoading}
                  className="w-full h-12 text-base"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      验证中...
                    </>
                  ) : (
                    <>
                      登录
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>

                <div className="flex items-center justify-between text-sm">
                  <Button
                    variant="ghost"
                    onClick={() => setStep("email")}
                    className="p-0 h-auto text-muted-foreground hover:text-foreground"
                  >
                    更换邮箱
                  </Button>
                  
                  {countdown > 0 ? (
                    <span className="text-muted-foreground">
                      {countdown}秒后可重发
                    </span>
                  ) : (
                    <Button
                      variant="ghost"
                      onClick={resendCode}
                      className="p-0 h-auto text-primary hover:text-primary/80"
                    >
                      重新发送
                    </Button>
                  )}
                </div>
              </motion.div>
            )}
          </CardContent>
        </Card>

        <div className="mt-6 text-center text-sm text-muted-foreground">
          登录即表示您同意我们的
          <a href="#" className="text-primary hover:underline ml-1">服务条款</a>
          和
          <a href="#" className="text-primary hover:underline ml-1">隐私政策</a>
        </div>
      </motion.div>
    </div>
  )
}
