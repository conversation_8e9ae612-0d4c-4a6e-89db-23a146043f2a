// API配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://43.128.84.4/api'

// 401错误处理函数类型
type UnauthorizedHandler = () => void

// API端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    SEND_CODE: `${API_BASE_URL}/auth/send-code`,
    VERIFY_LOGIN: `${API_BASE_URL}/auth/verify-login`,
    LOGIN: `${API_BASE_URL}/auth/login`,
    REGISTER: `${API_BASE_URL}/auth/register`,
    USER_INFO: `${API_BASE_URL}/auth/userInfo`,
    UPDATE_PASSWORD: `${API_BASE_URL}/auth/updatePwd`,
    LOGOUT: `${API_BASE_URL}/auth/logout`,
  },
  // API Key管理
  API_KEYS: {
    LIST: `${API_BASE_URL}/keys`,
    CREATE: `${API_BASE_URL}/keys`,
    UPDATE: (id: string) => `${API_BASE_URL}/keys/${id}`,
    UPDATE_ADVANCED: (id: string) => `${API_BASE_URL}/keys/${id}/advanced`,
    DELETE: (id: string) => `${API_BASE_URL}/keys/${id}`,
    ADMIN_LIST: `${API_BASE_URL}/keys/admin/all`,
    ENABLE_VOICE: (id: string) => `${API_BASE_URL}/keys/${id}/voice/enable`,
  },
  // 调用记录
  USAGE_RECORDS: {
    LIST: `${API_BASE_URL}/usage`,
    UPDATE_NOTES: (id: string) => `${API_BASE_URL}/usage/${id}/notes`,
    ADMIN_LIST: `${API_BASE_URL}/usage/admin/all`,
  },
  // 仪表板相关
  DASHBOARD: {
    STATS: `${API_BASE_URL}/dashboard/stats`,
  },
  // 订单相关
  ORDERS: {
    CREATE: `${API_BASE_URL}/orders/create`,
    STATUS: (orderNumber: string) => `${API_BASE_URL}/orders/${orderNumber}/status`,
    MY_ORDERS: `${API_BASE_URL}/orders/my`,
    CONFIRM: (token: string) => `${API_BASE_URL}/orders/confirm/${token}`,
  }
}

// HTTP请求工具函数
export class ApiClient {
  // 401错误处理回调函数
  private static unauthorizedHandler: UnauthorizedHandler | null = null

  // 设置401错误处理回调
  static setUnauthorizedHandler(handler: UnauthorizedHandler) {
    this.unauthorizedHandler = handler
  }

  private static getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }

  static async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    })

    // 检查401未授权错误
    if (response.status === 401) {
      // 调用401错误处理回调
      if (this.unauthorizedHandler) {
        this.unauthorizedHandler()
      }

      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || '登录已失效，请重新登录')
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  static async get<T>(url: string): Promise<T> {
    return this.request<T>(url, { method: 'GET' })
  }

  static async post<T>(url: string, data?: any): Promise<T> {
    return this.request<T>(url, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  static async put<T>(url: string, data?: any): Promise<T> {
    return this.request<T>(url, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  static async delete<T>(url: string): Promise<T> {
    return this.request<T>(url, { method: 'DELETE' })
  }
}

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
}

// 认证相关类型
export interface LoginResponse {
  token: string
  user: {
    id: number
    email: string
    name: string
    roles: string[]
    enabled: boolean
    createdAt: string
    lastLogin?: string
  }
}

// 验证登录响应类型（匹配后端实际返回的结构）
export interface VerifyLoginResponse extends ApiResponse {
  token: string
  user: {
    id: number
    email: string
    name: string
    roles: string[]
    enabled: boolean
    createdAt: string
    lastLogin?: string
    remainingCalls?: number
    totalCalls?: number
  }
}

// API Key相关类型
export interface ApiKeyDto {
  id: number
  keyValue: string
  keyName: string
  remainingCalls: number
  totalCalls: number
  active: boolean
  createdAt: string
  expiresAt?: string
  voiceEnabled: boolean
  voiceAppId?: string
  voiceAccessKeyId?: string
  user?: {
    id: number
    email: string
    fullName: string
  }
}

// getUserApiKeys 接口的返回类型
export interface GetApiKeysResponse extends ApiResponse {
  data: ApiKeyDto[]
  userInfo: {
    remainingCalls: number
    totalCalls: number
  }
}

// 调用记录相关类型
export interface UsageRecordDto {
  id: number
  apiKeyId: number
  apiKeyValue: string
  usedAt: string
  endpoint: string
  duration: number
}

export interface PaginatedResponse<T> {
  records: T[]
  totalElements: number
  totalPages: number
  currentPage: number
  pageSize: number
  hasNext: boolean
  hasPrevious: boolean
}

// getUserUsageRecords 接口的返回类型
export interface GetUsageRecordsResponse extends ApiResponse {
  data: PaginatedResponse<UsageRecordDto> & {
    totalCallCount: number
  }
}

// 仪表板数据相关类型
export interface DashboardStats {
  remainingCalls: number
  totalCalls: number
  apiKeyCount: number
  totalCallCount: number
  usedCalls: number
}

export interface DashboardUserInfo {
  id: number
  email: string
  name: string
  remainingCalls: number
  totalCalls: number
  lastLogin?: string
}

export interface GetDashboardStatsResponse extends ApiResponse {
  data: {
    userInfo: DashboardUserInfo
    stats: DashboardStats
  }
}

// API服务类
export class AuthService {
  static async sendCode(email: string): Promise<ApiResponse> {
    return ApiClient.post(API_ENDPOINTS.AUTH.SEND_CODE, { email })
  }

  static async verifyLogin(email: string, code: string): Promise<VerifyLoginResponse> {
    return ApiClient.post(API_ENDPOINTS.AUTH.VERIFY_LOGIN, { email, code })
  }

  static async getUserInfo(): Promise<ApiResponse<LoginResponse['user']>> {
    return ApiClient.get(API_ENDPOINTS.AUTH.USER_INFO)
  }

  static async updatePassword(oldPassword: string, newPassword: string, confirmPassword: string): Promise<ApiResponse> {
    return ApiClient.put(API_ENDPOINTS.AUTH.UPDATE_PASSWORD, {
      old: oldPassword,
      new: newPassword,
      confirm: confirmPassword
    })
  }

  static async logout(): Promise<ApiResponse> {
    return ApiClient.post(API_ENDPOINTS.AUTH.LOGOUT)
  }
}

export class ApiKeyService {
  static async getApiKeys(): Promise<GetApiKeysResponse> {
    return ApiClient.get(API_ENDPOINTS.API_KEYS.LIST)
  }

  static async createApiKey(keyName?: string, remainingCalls?: number): Promise<ApiResponse<ApiKeyDto>> {
    const data: any = {}
    if (keyName) data.keyName = keyName
    if (remainingCalls !== undefined) data.remainingCalls = remainingCalls
    return ApiClient.post(API_ENDPOINTS.API_KEYS.CREATE, Object.keys(data).length > 0 ? data : undefined)
  }

  static async updateApiKey(id: string, data: { remainingCalls?: number, active?: boolean }): Promise<ApiResponse<ApiKeyDto>> {
    return ApiClient.put(API_ENDPOINTS.API_KEYS.UPDATE(id), data)
  }

  static async updateApiKeyAdvanced(id: string, data: {
    keyName?: string
    creditsChange?: number
    remainingCalls?: number
    active?: boolean
    voiceEnabled?: boolean
    voiceAppId?: string
    voiceAccessKeyId?: string
  }): Promise<ApiResponse<ApiKeyDto>> {
    return ApiClient.put(API_ENDPOINTS.API_KEYS.UPDATE_ADVANCED(id), data)
  }

  static async deleteApiKey(id: string): Promise<ApiResponse> {
    return ApiClient.delete(API_ENDPOINTS.API_KEYS.DELETE(id))
  }

  static async enableVoice(id: string, data: {
    voiceAppId: string
    voiceAccessKeyId: string
  }): Promise<ApiResponse<ApiKeyDto>> {
    return ApiClient.put(API_ENDPOINTS.API_KEYS.ENABLE_VOICE(id), data)
  }

  static async toggleActive(id: string, active: boolean): Promise<ApiResponse<ApiKeyDto>> {
    return ApiClient.put(API_ENDPOINTS.API_KEYS.UPDATE_ADVANCED(id), { active })
  }
}

export class UsageRecordService {
  static async getUsageRecords(params?: {
    apiKeyId?: number
    page?: number
    size?: number
    sortBy?: string
    sortDir?: string
  }): Promise<ApiResponse<PaginatedResponse<UsageRecordDto>>> {
    const searchParams = new URLSearchParams()
    if (params?.apiKeyId) searchParams.append('apiKeyId', params.apiKeyId.toString())
    if (params?.page !== undefined) searchParams.append('page', params.page.toString())
    if (params?.size) searchParams.append('size', params.size.toString())
    if (params?.sortBy) searchParams.append('sortBy', params.sortBy)
    if (params?.sortDir) searchParams.append('sortDir', params.sortDir)

    const url = `${API_ENDPOINTS.USAGE_RECORDS.LIST}?${searchParams.toString()}`
    return ApiClient.get(url)
  }

  static async updateNotes(id: string, notes: string): Promise<ApiResponse<UsageRecordDto>> {
    return ApiClient.put(API_ENDPOINTS.USAGE_RECORDS.UPDATE_NOTES(id), { notes })
  }
}

export class DashboardService {
  static async getDashboardStats(): Promise<GetDashboardStatsResponse> {
    return ApiClient.get(API_ENDPOINTS.DASHBOARD.STATS)
  }
}

// 订单相关类型定义
export interface CreateOrderRequest {
  planName: string
  amount: number
  credits: number
  paymentMethod: 'WECHAT' | 'ALIPAY'
  description?: string
}

export interface OrderDto {
  id: number
  orderNumber: string
  planName: string
  amount: number
  credits: number
  paymentMethod: string
  status: 'PENDING' | 'PAID' | 'CANCELLED' | 'EXPIRED'
  createdAt: string
  paidAt?: string
  expiredAt: string
  qrCodeData?: string
  paymentUrl?: string
  description?: string
}

export class OrderService {
  static async createOrder(request: CreateOrderRequest): Promise<ApiResponse<OrderDto>> {
    return ApiClient.post(API_ENDPOINTS.ORDERS.CREATE, request)
  }

  static async getOrderStatus(orderNumber: string): Promise<ApiResponse<{
    orderNumber: string
    status: string
    amount: number
    credits: number
    createdAt: string
    paidAt?: string
    expiredAt: string
  }>> {
    return ApiClient.get(API_ENDPOINTS.ORDERS.STATUS(orderNumber))
  }

  static async getMyOrders(): Promise<ApiResponse<OrderDto[]>> {
    return ApiClient.get(API_ENDPOINTS.ORDERS.MY_ORDERS)
  }
}
