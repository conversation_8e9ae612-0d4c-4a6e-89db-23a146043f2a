#!/bin/bash

# MySQL密码重置工具
# 当MySQL安装后不知道root密码时使用此脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
    else
        OS="Unknown"
    fi
    log_info "检测到操作系统: $OS"
}

# 查找MySQL临时密码
find_temp_password() {
    log_info "查找MySQL临时密码..."
    
    local log_files=(
        "/var/log/mysqld.log"
        "/var/log/mysql/mysqld.log"
        "/var/log/mysql/error.log"
        "/var/log/mysql.log"
    )
    
    for log_file in "${log_files[@]}"; do
        if [[ -f "$log_file" ]]; then
            local temp_pass=$(grep 'temporary password' "$log_file" 2>/dev/null | tail -1 | awk '{print $NF}')
            if [[ -n "$temp_pass" ]]; then
                log_success "在 $log_file 中找到临时密码"
                echo "临时密码: $temp_pass"
                echo
                log_info "请使用以下命令登录并修改密码:"
                echo "mysql -u root -p"
                echo "然后执行: ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
                return 0
            fi
        fi
    done
    
    log_warning "未找到临时密码，将使用其他方法重置"
    return 1
}

# 获取用户新密码
get_new_password() {
    while true; do
        read -s -p "请输入新的MySQL root密码 (至少8位字符): " NEW_PASSWORD
        echo
        if [[ ${#NEW_PASSWORD} -ge 8 ]]; then
            read -s -p "请再次确认密码: " NEW_PASSWORD_CONFIRM
            echo
            if [[ "$NEW_PASSWORD" == "$NEW_PASSWORD_CONFIRM" ]]; then
                break
            else
                log_error "两次密码输入不一致，请重新输入"
            fi
        else
            log_error "密码长度至少需要8位字符"
        fi
    done
}

# 检测MySQL服务名称
detect_mysql_service() {
    if systemctl list-unit-files | grep -q "^mysqld.service"; then
        MYSQL_SERVICE="mysqld"
    elif systemctl list-unit-files | grep -q "^mysql.service"; then
        MYSQL_SERVICE="mysql"
    else
        log_error "未找到MySQL服务"
        exit 1
    fi
    log_info "MySQL服务名称: $MYSQL_SERVICE"
}

# 方法1: 尝试使用临时密码
try_temp_password() {
    log_info "方法1: 尝试使用临时密码重置..."
    
    if find_temp_password; then
        read -p "是否尝试使用找到的临时密码自动重置? (y/N): " auto_reset
        if [[ $auto_reset =~ ^[Yy]$ ]]; then
            local temp_pass=$(grep 'temporary password' /var/log/mysqld.log /var/log/mysql/mysqld.log /var/log/mysql/error.log 2>/dev/null | tail -1 | awk '{print $NF}')
            if [[ -n "$temp_pass" ]]; then
                if mysql -u root -p"$temp_pass" --connect-expired-password -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$NEW_PASSWORD';" 2>/dev/null; then
                    log_success "密码重置成功！"
                    return 0
                else
                    log_warning "临时密码可能已失效"
                fi
            fi
        fi
    fi
    return 1
}

# 方法2: 尝试无密码连接
try_no_password() {
    log_info "方法2: 尝试无密码连接..."
    
    if mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$NEW_PASSWORD';" 2>/dev/null; then
        log_success "密码设置成功！"
        return 0
    fi
    return 1
}

# 方法3: 使用系统认证
try_system_auth() {
    log_info "方法3: 尝试系统认证..."
    
    if sudo mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$NEW_PASSWORD';" 2>/dev/null; then
        log_success "密码设置成功！"
        return 0
    fi
    return 1
}

# 方法4: 安全模式重置
reset_with_safe_mode() {
    log_info "方法4: 使用安全模式重置密码..."
    
    # 停止MySQL服务
    log_info "停止MySQL服务..."
    systemctl stop $MYSQL_SERVICE
    
    # 创建临时初始化文件
    cat > /tmp/mysql-reset.sql << EOF
ALTER USER 'root'@'localhost' IDENTIFIED BY '$NEW_PASSWORD';
FLUSH PRIVILEGES;
EOF
    
    log_info "使用安全模式启动MySQL..."
    
    # 使用init-file方式
    if mysqld --init-file=/tmp/mysql-reset.sql --user=mysql --console &; then
        MYSQL_PID=$!
        
        # 等待初始化完成
        sleep 10
        
        # 停止临时进程
        kill $MYSQL_PID 2>/dev/null || true
        
        # 清理临时文件
        rm -f /tmp/mysql-reset.sql
        
        # 正常启动MySQL
        log_info "正常启动MySQL服务..."
        systemctl start $MYSQL_SERVICE
        
        # 测试新密码
        if mysql -u root -p"$NEW_PASSWORD" -e "SELECT 1;" 2>/dev/null; then
            log_success "密码重置成功！"
            return 0
        fi
    fi
    
    # 如果init-file方式失败，尝试skip-grant-tables方式
    log_warning "init-file方式失败，尝试skip-grant-tables方式..."
    
    # 创建临时配置文件
    cat > /tmp/mysql-skip-grants.cnf << 'EOF'
[mysqld]
skip-grant-tables
skip-networking
socket=/tmp/mysql_temp.sock
EOF
    
    # 使用临时配置启动MySQL
    mysqld --defaults-file=/tmp/mysql-skip-grants.cnf --user=mysql &
    MYSQL_PID=$!
    
    # 等待MySQL启动
    sleep 5
    
    # 重置密码
    if mysql --socket=/tmp/mysql_temp.sock -u root -e "FLUSH PRIVILEGES; ALTER USER 'root'@'localhost' IDENTIFIED BY '$NEW_PASSWORD';" 2>/dev/null; then
        log_success "安全模式密码重置成功"
        
        # 停止临时MySQL进程
        kill $MYSQL_PID 2>/dev/null || true
        sleep 2
        
        # 清理临时文件
        rm -f /tmp/mysql-skip-grants.cnf /tmp/mysql_temp.sock
        
        # 正常启动MySQL
        systemctl start $MYSQL_SERVICE
        
        # 验证密码
        if mysql -u root -p"$NEW_PASSWORD" -e "SELECT 1;" 2>/dev/null; then
            log_success "密码重置并验证成功！"
            return 0
        fi
    fi
    
    # 清理
    kill $MYSQL_PID 2>/dev/null || true
    rm -f /tmp/mysql-skip-grants.cnf /tmp/mysql_temp.sock /tmp/mysql-reset.sql
    systemctl start $MYSQL_SERVICE
    
    return 1
}

# 验证密码设置
verify_password() {
    log_info "验证新密码..."
    
    if mysql -u root -p"$NEW_PASSWORD" -e "SELECT USER(), @@hostname;" 2>/dev/null; then
        log_success "密码验证成功！"
        echo
        log_info "您现在可以使用以下命令连接MySQL:"
        echo "mysql -u root -p"
        echo "密码: $NEW_PASSWORD"
        return 0
    else
        log_error "密码验证失败"
        return 1
    fi
}

# 显示使用说明
show_manual_instructions() {
    echo
    log_warning "============= 手动重置说明 ============="
    echo "如果脚本自动重置失败，请手动执行以下步骤："
    echo
    echo "1. 停止MySQL服务:"
    echo "   sudo systemctl stop $MYSQL_SERVICE"
    echo
    echo "2. 安全模式启动MySQL:"
    echo "   sudo mysqld --skip-grant-tables --skip-networking &"
    echo
    echo "3. 连接MySQL:"
    echo "   mysql -u root"
    echo
    echo "4. 重置密码:"
    echo "   FLUSH PRIVILEGES;"
    echo "   ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';"
    echo "   EXIT;"
    echo
    echo "5. 重启MySQL服务:"
    echo "   sudo pkill mysqld"
    echo "   sudo systemctl start $MYSQL_SERVICE"
    echo
    echo "6. 测试新密码:"
    echo "   mysql -u root -p"
    echo "======================================="
}

# 主函数
main() {
    echo "MySQL密码重置工具"
    echo "=================="
    
    # 检查权限
    check_root
    
    # 检测系统
    detect_os
    
    # 检测MySQL服务
    detect_mysql_service
    
    # 获取新密码
    get_new_password
    
    echo
    log_info "开始尝试重置MySQL root密码..."
    
    # 尝试不同的重置方法
    if try_temp_password; then
        verify_password
        exit 0
    fi
    
    if try_no_password; then
        verify_password
        exit 0
    fi
    
    if try_system_auth; then
        verify_password
        exit 0
    fi
    
    if reset_with_safe_mode; then
        verify_password
        exit 0
    fi
    
    # 所有方法都失败
    log_error "所有自动重置方法都失败了"
    show_manual_instructions
    
    exit 1
}

# 执行主函数
main "$@"
