<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offer-Helper</title>
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', sans-serif;
        }

        body {
            background-color: #f5f7fa;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px;
        }

        /* 导航栏样式 */
        .nav {
            position: fixed;
            top: 0;
            width: 100%;
            background: linear-gradient(135deg, #2c3e50, #3498db); /* 渐变背景 */
            /* padding: 12px 0; */
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .nav .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-logo {
            display: flex;
            align-items: center;
        }
        
        .nav-logo img {
            height: 80px;
            margin-right: 15px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 5px 15px;
            border-radius: 30px;
            border: 2px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
        }

        .nav a:hover {
            background: rgba(255,255,255,0.2);
            border-color: white;
            box-shadow: 0 4px 15px rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .contact-btn {
            background: rgba(52, 152, 219, 0.3) !important;
            border: 2px solid rgba(52, 152, 219, 0.6) !important;
        }
        
        .contact-btn:hover {
            background: rgba(52, 152, 219, 0.5) !important;
        }

        /* 移动端导航菜单按钮 */
        .menu-toggle {
            display: none;
            flex-direction: column;
            justify-content: space-between;
            width: 30px;
            height: 21px;
            cursor: pointer;
        }

        .menu-toggle span {
            display: block;
            height: 3px;
            width: 100%;
            background-color: white;
            border-radius: 3px;
        }
        
        /* 移动端响应式样式 */
        @media (max-width: 768px) {
            .nav-logo img {
                height: 60px;
            }
            
            .menu-toggle {
                display: flex;
            }
            
            .nav-links {
                position: fixed;
                top: 80px;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #2c3e50, #3498db);
                flex-direction: column;
                align-items: center;
                padding: 20px 0;
                gap: 20px;
                transform: translateY(-150%);
                transition: transform 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }
            
            .nav-links.active {
                transform: translateY(0);
            }
        }

        /* 视频展示区 */
        .video-section {
            margin-top: 70px;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        video {
            width: 100%;
            max-width: 800px;
            margin: 20px auto;
            display: block;
        }

        /* 下载区 */
        .download-section {
            margin: 40px 0;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .download-buttons {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: 0.3s;
        }

        .windows-btn {
            background: #00a4ef;
            color: white;
        }

        .mac-btn {
            background: #999;
            color: white;
        }

        /* 移动端下载按钮响应式 */
        @media (max-width: 576px) {
            .download-buttons {
                flex-direction: column;
                gap: 15px;
            }
            
            .btn {
                width: 100%;
            }
        }

        /* 收费说明区 */
        .pricing-section {
            margin: 40px 0;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* 特性区域 */
        .features-section {
            margin: 80px 0;
            background: white;
            padding: 30px 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-top: 20px;
        }
        
        /* 移动端特性卡片响应式 */
        @media (max-width: 576px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }

        .feature-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            border-left: 4px solid #00a4ef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 15px;
            color: #00a4ef;
        }

        .feature-list {
            margin: 20px 0;
            padding-left: 20px;
        }

        .price-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .price-table td, .price-table th {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .price-table th {
            background-color: #f8f9fa;
        }
        
        /* 联系方式部分 */
        .contact-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .contact-icon {
            font-size: 24px;
            color: #00a4ef;
            width: 50px;
            height: 50px;
            background: #f0f7ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-left: 4px solid #00a4ef;
            padding-left: 10px;
        }

        h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }

        .highlight {
            color: #00a4ef;
            font-weight: bold;
        }

        .tag {
            display: inline-block;
            background: #e1f5fe;
            color: #0288d1;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 5px;
        }

        /* 添加锚点目标样式，为固定导航栏留出空间 */
        section[id] {
            scroll-margin-top: 100px; /* 调整这个值以匹配导航栏高度 */
        }
        
        /* 移动端锚点调整 */
        @media (max-width: 768px) {
            section[id] {
                scroll-margin-top: 80px;
            }
        }

        /* 可选：平滑滚动效果 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 移动端表格响应式 */
        @media (max-width: 576px) {
            .price-table {
                font-size: 14px;
            }
            
            .price-table td, .price-table th {
                padding: 8px;
            }
        }


        .faq-section {
            margin: 40px 0;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .faq-item {
            margin-bottom: 20px;
        }

        .faq-item h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .faq-item p {
            color: #555;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <nav class="nav">
        <div class="container">
            <div class="nav-logo">
                <img src="logo.png" alt="TechPortal Logo">
            </div>
            <div class="menu-toggle" id="menuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="nav-links" id="navLinks">
                <a href="#features">核心特性</a>
                <a href="#video">功能演示</a>
                <a href="#download">下载安装</a>
                <a href="#price">收费说明</a>
                <a href="#faq">常见问题</a>
                <a href="#contact" class="contact-btn">联系咨询</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- 特性展示 -->
        <section id="features" class="features-section">
            <h2>核心特性</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>99% 隐身能力</h3>
                    <p>针对<span class="highlight">腾讯会议</span>、<span class="highlight">钉钉会议</span>等主流会议软件的屏幕共享进行优化，实现近乎完美的隐身体验。</p>
                    <p style="margin-top:10px">
                        <span class="tag">腾讯会议</span>
                        <span class="tag">钉钉会议</span>
                        <span class="tag">飞书会议</span>
                        <span class="tag">牛客</span>
                        <span class="tag">赛马</span>
                        <span class="tag">Zoom</span>
                        <span class="tag">Teams</span>
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⌨️</div>
                    <h3>全程快捷键操作</h3>
                    <p>通过精心设计的快捷键系统，实现全程键盘操作，<span class="highlight">完全屏蔽屏幕窗口切换的风险</span>，保证面试和考试过程安全无忧。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3>DeepSeek AI 引擎</h3>
                    <p>集成最新的 DeepSeek 大模型，提供专业级的代码生成、分析和优化能力，远超传统编程助手。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📸</div>
                    <h3>截图/剪切板智能分析</h3>
                    <p>只需截取问题图片或复制文本，AI 立即分析并给出高质量解答，支持多语言代码识别与生成。</p>
                </div>
            </div>

            <div style="margin-top:30px">
                <h3>全面的编程语言支持</h3>
                <p style="margin-bottom:15px">我们的产品支持几乎所有主流编程语言，包括但不限于：</p>
                <div style="display:flex;flex-wrap:wrap;gap:10px;margin-top:15px">
                    <span class="tag">Java</span>
                    <span class="tag">Python</span>
                    <span class="tag">JavaScript</span>
                    <span class="tag">C/C++</span>
                    <span class="tag">Go</span>
                    <span class="tag">Rust</span>
                    <span class="tag">PHP</span>
                </div>
            </div>
        </section>

        <!-- 视频展示 -->
        <section id="video" class="video-section">
            <h2>产品功能演示</h2>
            
            <div style="margin-bottom: 40px;">
                <h3 style="margin-bottom: 15px;">隐身效果演示</h3>
                <video controls poster="placeholder-stealth.jpg" autoplay muted loop>
                    <source src="stealth-demo.mp4" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
                <p style="margin-top: 10px;">观看隐身功能演示，了解在各种会议软件中的隐身效果</p>
            </div>
            
            <div>
                <h3 style="margin-bottom: 15px;">AI 正确率演示</h3>
                <video controls poster="placeholder-accuracy.jpg" autoplay muted loop>
                    <source src="accuracy-demo.mp4" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
                <p style="margin-top: 10px;">观看 DeepSeek AI 引擎的代码生成和问题解答正确率演示</p>
            </div>
        </section>

        <!-- 下载区 -->
        <section id="download" class="download-section">
            <h2>立即下载</h2>
            <p style="margin-bottom:15px">最新版本已针对隐身性能和 DeepSeek 集成进行全面优化</p>
            <div class="download-buttons">
                <p>下载链接：<a href="https://wwwb.lanzn.com/b00zxo2ffg" target="_blank">https://wwwb.lanzn.com/b00zxo2ffg</a></p>
                <p>密码：5qp8</p>
            </div>
            <p style="margin-top:15px;color:#666">
                支持系统：Windows10+ / macOS
            </p>
        </section>

        <!-- 收费说明 -->
        <section id="price" class="pricing-section">
            <h2>使用说明（可看视频）</h2>
            <ol class="feature-list">
                <li>下载安装</li>
                <li>注册 Key（联系咨询）</li>
                <li>快捷键
                    <ul style="margin-top: 10px; margin-bottom: 15px;">
                        <li><strong>截图：</strong>
                            <ul>
                                <li>Alt/Option + 1 &nbsp; 当前光标位置作为图片左上角</li>
                                <li>Alt/Option + 2 &nbsp; 当前光标位置作为图片右下角</li>
                            </ul>
                            <ul>
                                <li>Alt/Option + Shift + 1 &nbsp; DeepSeek chat 模型</li>
                                <li>Alt/Option + Shift + 2 &nbsp; DeepSeek r1 模型</li>
                            </ul>
                        </li>
                        <br>
                        <li><strong>复制文本解答：</strong>
                            <ul>
                                <li>Ctrl/Command + C &nbsp; 复制文本</li>
                                <li>Alt/Option + Z &nbsp; DeepSeek chat 模型</li>
                                <li>Alt/Option + X &nbsp; DeepSeek r1 模型</li>
                            </ul>
                        </li>
                        <br>
                        <li><strong>重置：</strong>
                            <ul>
                                <li>Ctrl/Command + R</li>
                            </ul>
                        </li>
                        <br>
                        <li><strong>隐藏：</strong>
                            <ul>
                                <li>Ctrl/Command + B</li>
                            </ul>
                        </li>
                        <br>
                        <li><strong>移动：</strong>
                            <ul>
                                <li>Ctrl/Command + 上下左右</li>
                            </ul>
                        </li>
                        <br>
                        <li><strong>复制代码（1.0.3以上版本支持）：</strong>
                            <ul>
                                <li>Alt + C</li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ol>
            
            <h2 style="margin-top: 30px;">收费标准</h2>
            <table class="price-table">
                <tr>
                    <th>套餐类型</th>
                    <th>功能特性</th>
                    <th>价格</th>
                </tr>
                <tr>
                    <td>新用户</td>
                    <td>
                        <ul style="list-style:none;padding-left:0">
                            <li>✓ 基础隐身功能</li>
                            <li>✓ 主流编程语言支持</li>
                            <li>✓ 基础代码生成</li>
                        </ul>
                    </td>
                    <td>两次免费额度</td>
                </tr>
                <tr>
                    <td>基础套餐</td>
                    <td>
                        <ul style="list-style:none;padding-left:0">
                            <li>✓ 99%隐身能力</li>
                            <li>✓ DeepSeek AI 高级引擎</li>
                            <li>✓ 全语言支持与代码优化</li>
                            <li>✓ 截图智能分析</li>
                        </ul>
                    </td>
                    <td>¥50/10次</td>
                </tr>
                <tr>
                    <td>高级套餐</td>
                    <td>
                        <ul style="list-style:none;padding-left:0">
                            <li>✓ 基础套餐全部功能</li>
                            <li>✓ 更多使用次数</li>
                            <li>✓ 优先技术支持</li>
                        </ul>
                    </td>
                    <td>¥100/25次</td>
                </tr>
            </table>

            <div style="margin-top: 20px; color: red; font-weight: bold;">
                <p>温馨提示：确保本地测试通过后，并且完全满足需求再进行充值，充值后不会退款。</p>
                <p>注意不要连贯操作，答案未返回之前，不要继续提问，否则会造成key浪费。</p>
            </div>
        </section>
        

        <!-- 如何验证 -->
        <section id="validation" class="faq-section">
            <h2>新手教程</h2>
            <div class="faq-item">
                <h3>1. 下载安装</h3>
                <p>选择对应版本的安装包，下载安装后，微信联系 Java_Plus 获取试用 Key，填入Key</p>
            </div>
            <div class="faq-item">
                <h3>2. 验证共享屏幕是否具备隐藏性</h3>
                <p>打开笔试或者面试平台，点击共享屏幕，一般会有个小窗口，即可验证是否能看到，如果能看到，只能选择共享某个程序或者窗口。</p>
                <p>会议软件可以使用录制的方式或者两个账号登录会议的方式验证。</p>
            </div>
            <div class="faq-item">
                <h3>3. 验证快捷键是否正常</h3>
                <p>验证快捷键功能是否正常。</p>
            </div>
            <div class="faq-item">
                <h3>4. 验证图片位置是否正常（1.0.3以下版本）</h3>
                <p>使用截图快捷键后，工具上面会显示图片，将图片拖拽到浏览器后Enter打开，如果图片与实际位置不符，设置一下当前电脑的屏幕缩放为100%</p>
            </div>
            <div class="faq-item">
                <h3 style="color: red;">5. 注意事项（非常重要）</h3>
                <p>答案未返回之前，不要继续提问，否则会造成key次数浪费，耐心等待，chat模型一般需要在 30s-60s 返回结果，r1模型一般在 3min-5min 返回结果</p>
                <p>当前题做完后，一定要使用 Ctrl/Command + R 重置后，再进行下一道题</p>
            </div>
        </section>

        <!-- 常见问题 -->
        <section id="faq" class="faq-section">
            <h2>常见问题</h2>
            <div class="faq-item">
                <h3>1. 软件支持哪些操作系统？</h3>
                <p>目前支持 Windows 10+ 和 macOS arm 系统。注意：部分windows10系统在共享屏幕的情况下，可能存在对方视角出现黑块，此时需要升级系统或者更换电脑</p>
            </div>
            <div class="faq-item">
                <h3>2. 如何获取使用Key？</h3>
                <p>您可以通过“联系咨询”部分的联系方式获取使用 Key。</p>
            </div>
            <div class="faq-item">
                <h3>3. Key剩余次数是否可以退？</h3>
                <p>不可以。可以转让或者出售给其他人。</p>
            </div>
            <div class="faq-item">
                <h3>4. Key是否支持多端使用？</h3>
                <p>可以，Key可以在多台设备同时使用。</p>
            </div>
            <div class="faq-item">
                <h3>5. Key一次如何计算？</h3>
                <p>使用 (Alt/Option + Shift + 1 或者 Alt/Option + Shift + 2) 识别截图、使用（Alt/Option + Z 或者 Alt/Option + X）识别剪切板后成功获取结果算一次操作。</p>
            </div>
            <div class="faq-item">
                <h3>6. 如何关闭程序？</h3>
                <p>Windows通过任务管理器，Mac通过活动监视器关闭。</p>
            </div>
            <div class="faq-item">
                <h3>7. 为什么共享屏幕会被看到？</h3>
                <p>不同会议软件的共享屏幕的实现方式不同，且不同型号的电脑屏幕保护机制也不同，如果出现这种情况，可共享某个程序，而不是共享整个屏幕。</p>
            </div>
            <div class="faq-item">
                <h3>8. 如何截图获取答案？</h3>
                <p>参考上面的使用说明的截图部分，先使用 Alt/Option + 1，再使用 Alt/Option + 2完成截图，最后使用 Alt/Option + Shift + 1 解答。</p>
            </div>
            <div class="faq-item">
                <h3>9. DeepSeek的chat模型和r1模型有什么区别？</h3>
                <p>DeepSeek的chat模型能够解决大多数问题且响应速度快，r1模型会深度思考，正确率高于chat模型，但响应速度较慢。使用建议：可优先使用chat模型，若回答有偏差，再使用r1模型</p>
            </div>
            <div class="faq-item">
                <h3>10. 截图后使用报错如何解决？</h3>
                <p>（1）当使用 Alt/Option + 2 后直接报错，是因为截图权限未开，windows10参考：https://www.7jianban.com/jiaocheng/29494.html。</p>
                <p>（2）确保当前key剩余次数足够且图片内容尽可能只包含文字，使用Ctrl/Command + R重置后，重新截图，重新获取答案，答案未返回之前次数不减少。</p>
            </div>
            <div class="faq-item">
                <h3>11. Mac电脑安装提示"文件损坏"？</h3>
                <p>参考：https://sysin.org/blog/macos-if-crashes-when-opening/</p>
            </div>
        </section>
                
        <!-- 联系方式 -->
        <section id="contact" class="contact-section">
            <h2>联系咨询</h2>
            <p>如有任何问题或需求，欢迎通过以下方式联系：</p>
            
            <div class="contact-grid">
                <div class="contact-item">
                    <div class="contact-icon">💬</div>
                    <div>
                        <h3>微信</h3>
                        <p>Java_Plus</p>
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">📧</div>
                    <div>
                        <h3>电子邮件</h3>
                        <p><EMAIL></p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 在body结束标签前添加JavaScript -->
    <script>
        // 修复锚点定位问题
        document.querySelectorAll('.nav-links a').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    // 获取导航栏高度
                    const navHeight = document.querySelector('.nav').offsetHeight;
                    
                    // 计算目标位置，考虑导航栏高度
                    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - navHeight;
                    
                    // 滚动到目标位置
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    
                    // 在移动端点击导航链接后关闭菜单
                    if (window.innerWidth <= 768) {
                        document.getElementById('navLinks').classList.remove('active');
                    }
                }
            });
        });
        
        // 移动端菜单切换
        document.getElementById('menuToggle').addEventListener('click', function() {
            document.getElementById('navLinks').classList.toggle('active');
        });
        
        // 窗口大小变化时处理导航菜单
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('navLinks').classList.remove('active');
            }
        });
    </script>
</body>
</html>