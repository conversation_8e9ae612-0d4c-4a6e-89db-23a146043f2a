#!/bin/bash

# 测试MySQL临时密码显示功能
# 这个脚本用于测试临时密码检测功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================================"
echo "MySQL临时密码显示功能测试"
echo "========================================================"
echo

# 测试1: 检查是否存在MySQL日志文件
log_info "测试1: 检查MySQL日志文件..."
log_files=(
    "/var/log/mysqld.log"
    "/var/log/mysql/error.log"
    "/var/log/mysql.log"
)

found_logs=0
for log_file in "${log_files[@]}"; do
    if [[ -f "$log_file" ]]; then
        log_success "找到日志文件: $log_file"
        echo "  文件大小: $(du -h "$log_file" | cut -f1)"
        echo "  最后修改: $(stat -c %y "$log_file" 2>/dev/null || stat -f %Sm "$log_file" 2>/dev/null)"
        found_logs=$((found_logs + 1))
    fi
done

if [[ $found_logs -eq 0 ]]; then
    log_warning "未找到任何MySQL日志文件"
    echo "可能的原因："
    echo "1. MySQL未安装"
    echo "2. MySQL未启动过"
    echo "3. 日志文件位置不同"
fi

echo

# 测试2: 搜索临时密码
log_info "测试2: 搜索临时密码..."
temp_password_found=false

for log_file in "${log_files[@]}"; do
    if [[ -f "$log_file" ]]; then
        log_info "在 $log_file 中搜索临时密码..."
        
        # 搜索临时密码行
        temp_lines=$(grep 'temporary password' "$log_file" 2>/dev/null || true)
        
        if [[ -n "$temp_lines" ]]; then
            log_success "找到临时密码信息！"
            echo "完整的临时密码行："
            echo "$temp_lines" | while read line; do
                echo "  $line"
                # 提取密码
                password=$(echo "$line" | awk '{print $NF}')
                echo -e "  提取的密码: ${YELLOW}$password${NC}"
            done
            temp_password_found=true
        else
            log_info "在此文件中未找到临时密码"
        fi
        echo
    fi
done

if [[ "$temp_password_found" == false ]]; then
    log_warning "未在任何日志文件中找到临时密码"
fi

# 测试3: 检查MySQL服务状态
log_info "测试3: 检查MySQL服务状态..."
if command -v systemctl >/dev/null 2>&1; then
    if systemctl is-active mysqld >/dev/null 2>&1; then
        log_success "MySQL服务 (mysqld) 正在运行"
    elif systemctl is-active mysql >/dev/null 2>&1; then
        log_success "MySQL服务 (mysql) 正在运行"
    else
        log_warning "MySQL服务未运行"
    fi
else
    log_info "systemctl不可用，跳过服务状态检查"
fi

# 测试4: 检查MySQL客户端
log_info "测试4: 检查MySQL客户端..."
if command -v mysql >/dev/null 2>&1; then
    mysql_version=$(mysql --version 2>/dev/null || echo "无法获取版本")
    log_success "MySQL客户端已安装: $mysql_version"
else
    log_warning "MySQL客户端未安装"
fi

echo
echo "========================================================"
echo "测试完成"
echo "========================================================"
echo

# 提供使用建议
echo "使用建议："
echo "1. 如果找到了临时密码，可以使用以下命令测试："
echo "   ./install_mysql8.sh --show-temp-password"
echo
echo "2. 如果需要完整安装MySQL："
echo "   sudo ./install_mysql8.sh"
echo
echo "3. 如果需要手动重置密码："
echo "   sudo mysql_secure_installation"
