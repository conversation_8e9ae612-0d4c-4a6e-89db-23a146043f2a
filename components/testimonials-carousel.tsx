"use client"

import { useEffect, useState, useRef, useCallback } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { Star, ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

// 轮播组件
export default function TestimonialsCarousel() {
  // 图片总数
  const totalItems = 49
  // 轮播当前位置索引
  const [currentIndex, setCurrentIndex] = useState(0)
  // 轮播每页显示数量 (响应式)
  const [itemsPerPage, setItemsPerPage] = useState(5)  // 自动轮播间隔时间 (毫秒)
  const autoPlayInterval = 2000
  // 是否暂停自动轮播
  const [isPaused, setIsPaused] = useState(false)
  // 轮播容器引用
  const carouselRef = useRef<HTMLDivElement>(null)
  // 定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  // 触摸事件起始位置
  const [touchStart, setTouchStart] = useState(0)
  // 轮播方向（正向/反向）
  const [isReverse, setIsReverse] = useState(false)

  // 根据窗口大小调整每页显示数量
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setItemsPerPage(2) // 移动设备
      } else if (window.innerWidth < 1024) {
        setItemsPerPage(3) // 平板设备
      } else if (window.innerWidth < 1280) {
        setItemsPerPage(4) // 小屏幕电脑
      } else {
        setItemsPerPage(5) // 大屏幕电脑
      }
    }

    // 初始设置
    handleResize()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  // 移到下一页
  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => {
      // 计算最大开始索引，确保能完整显示一页
      const maxStartIndex = Math.max(0, totalItems - itemsPerPage);
      // 如果已经到最后，回到开始
      if (prevIndex >= maxStartIndex) {
        return 0;
      }
      // 否则移动一个项目，而不是一整页，使滚动更平滑
      return Math.min(prevIndex + 1, maxStartIndex);
    });
  }, [itemsPerPage, totalItems]);

  // 移到上一页
  const prevSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => {
      // 如果已经在开始，移动到最后
      if (prevIndex <= 0) {
        return Math.max(0, totalItems - itemsPerPage);
      }
      // 否则向前移动一个项目
      return prevIndex - 1;
    });
  }, [itemsPerPage, totalItems]);
  // 自动轮播
  useEffect(() => {
    if (!isPaused) {
      timerRef.current = setInterval(() => {
        // 在一个方向上滚动到头后，改变方向
        const maxStartIndex = Math.max(0, totalItems - itemsPerPage);
        
        if (isReverse) {
          if (currentIndex <= 0) {
            setIsReverse(false);
            nextSlide();
          } else {
            prevSlide();
          }
        } else {
          if (currentIndex >= maxStartIndex) {
            setIsReverse(true);
            prevSlide();
          } else {
            nextSlide();
          }
        }
      }, autoPlayInterval);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isPaused, nextSlide, prevSlide, currentIndex, autoPlayInterval, isReverse, itemsPerPage, totalItems]);

  // 鼠标悬停时暂停轮播
  const pauseAutoPlay = () => setIsPaused(true);
  const resumeAutoPlay = () => setIsPaused(false);

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
    pauseAutoPlay();
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const touchEnd = e.changedTouches[0].clientX;
    const diff = touchStart - touchEnd;
    
    // 如果滑动距离足够大，则切换到下一张/上一张
    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        nextSlide();
      } else {
        prevSlide();
      }
    }
    
    resumeAutoPlay();
  };

  // 计算可见项
  const visibleItems = []
  for (let i = 0; i < itemsPerPage; i++) {
    const itemIndex = (currentIndex + i) % totalItems
    visibleItems.push(itemIndex + 1) // 文件名从1开始
  }

  // 实际要显示的索引
  const indices = Array.from({ length: totalItems }, (_, i) => i + 1)

  // 计算指示点数量
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const currentPage = Math.floor(currentIndex / itemsPerPage)
  return (    <div 
      className="mx-auto relative carousel-container" 
      ref={carouselRef}
      onMouseEnter={pauseAutoPlay}
      onMouseLeave={resumeAutoPlay}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      <div className="relative overflow-hidden">
        <div 
          className="flex transition-transform duration-500 ease-out px-2"
          style={{ 
            transform: `translateX(-${(currentIndex / totalItems) * 100}%)`,
            width: `${(totalItems / itemsPerPage) * 100}%`
          }}
        >
          {indices.map((index) => (
            <div 
              key={index}
              className="px-2 carousel-slide"
              style={{ width: `${100 / totalItems}%` }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
                className="relative overflow-hidden rounded-lg shadow-md border border-border/30 aspect-[3/5] testimonial-card"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent z-10" />
                <Image
                  src={`/testimonials/testimonial-${index}.png`}
                  alt={`User testimonial ${index}`}
                  fill
                  className="object-cover testimonial-image"
                />
                <div className="absolute bottom-0 left-0 right-0 p-3 z-20 text-white">
                  <div className="flex items-center gap-1.5 mb-1">
                    {Array(5).fill(0).map((_, i) => (
                      <Star key={i} className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          ))}
        </div>
      </div>

      {/* 导航箭头 */}
      <Button
        variant="outline"
        size="icon"
        onClick={(e) => {
          e.stopPropagation()
          prevSlide()
        }}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-20 h-11 w-11 md:h-12 md:w-12 rounded-full bg-background/70 backdrop-blur-sm border border-border/40 text-foreground hover:bg-background/90 hover:text-primary shadow-lg"
      >
        <ChevronLeft className="h-6 w-6" />
        <span className="sr-only">Previous</span>
      </Button>

      <Button
        variant="outline"
        size="icon"
        onClick={(e) => {
          e.stopPropagation()
          nextSlide()
        }}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-20 h-11 w-11 md:h-12 md:w-12 rounded-full bg-background/70 backdrop-blur-sm border border-border/40 text-foreground hover:bg-background/90 hover:text-primary shadow-lg"
      >
        <ChevronRight className="h-6 w-6" />
        <span className="sr-only">Next</span>
      </Button>      {/* 页面指示器 */}
      <div className="flex justify-center mt-6 gap-2">
        {Array.from({ length: totalPages }).map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index * itemsPerPage)}
            className={`h-2 rounded-full transition-all duration-300 indicator-dot ${
              currentPage === index 
                ? "bg-primary w-6" 
                : "bg-muted-foreground/30 hover:bg-muted-foreground/50 w-2"
            }`}
            aria-label={`Go to page ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}
