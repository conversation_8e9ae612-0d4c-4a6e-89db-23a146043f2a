"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { X } from "lucide-react"

export function FloatingQRCodes() {
  const [isExpanded, setIsExpanded] = useState<boolean>(false)
  const [isVisible, setIsVisible] = useState<boolean>(true)

  if (!isVisible) return null
  
  return (
    <div className="fixed right-4 top-1/3 z-50 flex flex-col items-end">
      {/* Toggle Button */}
      {/* <Button
        variant="outline"
        size="icon"
        className="mb-2 size-10 rounded-full bg-background shadow-md border border-primary/20"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? (
          <X className="size-5 text-muted-foreground" />
        ) : (
          <svg 
            className="size-5 text-primary" 
            viewBox="0 0 24 24" 
            fill="currentColor"
          >
            <path d="M12 8.75a3.25 3.25 0 100 6.5 3.25 3.25 0 000-6.5z" />
            <path fillRule="evenodd" d="M6.77 7.75a1 1 0 01.73.34c.**********.84.41.34 0 .63-.17.84-.4a1 1 0 011.46 1.37C10.14 10.17 9.32 10.5 8.34 10.5c-.98 0-1.8-.33-2.3-1.03A1 1 0 017.5 8.1c.**********.85.4.33 0 .62-.17.83-.4a1 1 0 01.73-.35zM17.23 7.75a1 1 0 00-.73.34c-.21.24-.5.41-.84.41-.34 0-.63-.17-.84-.4a1 1 0 00-1.46 1.37c.5.7 1.32 1.03 2.3 1.03.98 0 1.8-.33 2.3-1.03a1 1 0 00-1.46-1.37c-.22.23-.51.4-.85.4-.33 0-.62-.17-.83-.4a1 1 0 00-.73-.35zM12 14a1 1 0 01.78.37l2 2.5a1 1 0 01-1.56 1.26l-1.22-1.53-1.22 1.53a1 1 0 01-1.56-1.26l2-2.5A1 1 0 0112 14z" clipRule="evenodd" />
            <path fillRule="evenodd" d="M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zM3 12a9 9 0 1118 0 9 9 0 01-18 0z" clipRule="evenodd"/>
          </svg>
        )}
      </Button> */}
      
      {/* Close Button */}
      {/* <Button 
        variant="ghost" 
        size="icon" 
        className="absolute -top-2 -left-2 size-5 rounded-full bg-muted hover:bg-muted/80 z-10"
        onClick={() => setIsVisible(false)}
      >
        <X className="size-3" />
      </Button> */}
      
      {/* QR Code Container */}
      <div 
        // className={`${
        //   isExpanded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-16 pointer-events-none'
        // } transition-all duration-300 flex flex-col gap-4 bg-card rounded-lg p-3 shadow-lg border border-border/40`}
      >
        {/* WeChat QR Code */}
        <div className="flex flex-col items-center">
          <div className="relative w-36 h-36 bg-white p-2 rounded-lg shadow-sm">
            {/* Placeholder for actual WeChat QR code image */}
            <div className="w-full h-full border-2 border-dashed border-primary/30 rounded flex items-center justify-center">
              <div className="bg-primary/10 p-2 rounded">
                {/* Placeholder for WeChat QR code SVG or image */}
                <Image
                    src={"/wechat.png"}
                    width={100}
                    height={100}
                    alt={'WeChat QR Code'}
                ></Image>
                
              </div>
            </div>
            {/* Logo in corner */}
            <div className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-sm border border-border/40">
              <svg 
                viewBox="0 0 24 24" 
                width="20" 
                height="20" 
                fill="#07C160"
              >
                <path d="M2.343 8.5c-.347 0-.68.057-1.008.162.034-.059.066-.144.098-.26.196-.474 1.764-1.09 3.537-1.09 1.655 0 3.344.516 4.65 1.568.042-.462.540-2.635 3.925-2.868a8.32 8.32 0 0 1 1.008-.002c1.592.097 2.288.47 2.651.751a19.84 19.84 0 0 0 1.518-.873c.677-.433 1.306-.861 1.945-1.211.658-.359 1.748-.5 1.833.26.035.317-.33.618-.55.877.293-.178.618-.33.968-.456.138-.05.282.188.222.402-.271.989-1.423 2.01-2.167 2.43-.234.133-.489.259-.758.374.348.18.564.47.642.874.223 1.153-.44 2.707-1.615 4.26-1.226 1.623-3.16 3.183-5.498 3.183-.078 0-.153-.005-.23-.01-.146-.008-.293-.012-.438-.012-.866 0-1.652.176-2.274.514-.285.155-.577.305-.885.429-1.315.528-3.504.93-5.044.012a4.93 4.93 0 0 1-.656-.46 3.2 3.2 0 0 1-.248-.255 4.008 4.008 0 0 1-.323-.423c-.519-.788-.832-1.85-.832-3.03 0-3.063 2.043-5.6 4.666-5.6zm8.096 1.866c-.806 0-1.46.703-1.46 1.57 0 .867.654 1.57 1.46 1.57.807 0 1.46-.703 1.46-1.57 0-.867-.653-1.57-1.46-1.57zm5.916 0c-.806 0-1.46.703-1.46 1.57 0 .867.654 1.57 1.46 1.57.807 0 1.46-.703 1.46-1.57 0-.867-.653-1.57-1.46-1.57z" />
              </svg>
            </div>
          </div>
          <span className="text-xs font-medium mt-2 text-center">微信联系</span>
          <span className="text-[10px] text-muted-foreground">Java_Plus</span>
        </div>
        
        {/* QQ QR Code */}
        <div className="flex flex-col items-center">
          <div className="relative w-36 h-36 bg-white p-2 rounded-lg shadow-sm">
            {/* Placeholder for actual QQ QR code image */}
            <div className="w-full h-full border-2 border-dashed border-blue-400/30 rounded flex items-center justify-center">
              <div className="bg-blue-400/10 p-2 rounded">
                <Image
                    src={"/qq.png"}
                    width={100}
                    height={100}
                    alt={'WeChat QR Code'}
                ></Image>
                
              </div>
            </div>
            {/* Logo in corner */}
            <div className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-sm border border-border/40">
              <svg 
                viewBox="0 0 24 24" 
                width="20" 
                height="20" 
                fill="#12B7F5"
              >
                <path d="M12.007 1c2.775 0 5.273 1.268 6.939 3.192a9.553 9.553 0 0 1 2.73 6.702c0 .536-.05 1.057-.124 1.578-.082.517-.206 1.02-.348 1.514l-.206.04c.016-.181.04-.371.04-.562 0-.396-.057-.783-.165-1.155-.124-.354-.297-.693-.545-.99l-.008-.02-.041-.057a.244.244 0 0 0-.033-.042c-.05-.05-.09-.066-.14-.066-.09 0-.157.066-.157.181 0 .033 0 .066.008.099l-.008-.016c-1.18 3.114-3.895 5.183-7.092 5.218h-.239c-3.288 0-6.111-2.176-7.232-5.202.008-.033.016-.066.016-.1 0-.114-.066-.18-.156-.18-.066 0-.132.05-.173.132-.24.297-.43.627-.545.99a3.396 3.396 0 0 0-.165 1.155c0 .19.016.38.033.561l-.223-.036a10.22 10.22 0 0 1-.347-1.514A10.987 10.987 0 0 1 3.98 10.9a9.173 9.173 0 0 1 1.198-4.593A8.697 8.697 0 0 1 12.007 1zm4.94 12.515c.396.619.958.99 1.578.99.198 0 .395-.025.585-.074l.025-.017c-.082.322-.19.635-.314.94-.223.446-.512.85-.908 1.205-.43.355-.933.644-1.463.817-.553.173-1.13.264-1.716.264-.586 0-1.164-.09-1.716-.264a4.771 4.771 0 0 1-1.463-.817c-.364-.33-.677-.76-.908-1.205-.124-.305-.231-.618-.305-.94l.024.017c.181.05.388.075.586.075.62 0 1.181-.372 1.577-.991a4.89 4.89 0 0 0 .487-.884c.074-.19.14-.371.198-.562 0 .083.006.157.017.24.09.264.19.52.33.759.132.223.288.43.471.602.363.338.85.544 1.396.544.545 0 1.022-.206 1.385-.544.19-.173.34-.379.47-.602.14-.239.248-.495.33-.76.02-.082.02-.165.02-.239.057.19.123.371.198.562.124.313.313.61.487.884z" />
              </svg>
            </div>
          </div>
          <span className="text-xs font-medium mt-2 text-center">QQ联系</span>
          <span className="text-[10px] text-muted-foreground">1670956606</span>
        </div>
      </div>
    </div>
  )
}
