"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Loader2, Clock, CheckCircle, QrCode } from "lucide-react"
import { toast } from "sonner"
import { OrderService, CreateOrderRequest, OrderDto } from "@/lib/api"

interface PurchaseModalProps {
  isOpen: boolean
  onClose: () => void
  plan: {
    name: string
    price: string
    credits: number
    features: string[]
  }
}

export default function PurchaseModal({ isOpen, onClose, plan }: PurchaseModalProps) {
  const [step, setStep] = useState<'confirm' | 'payment' | 'success'>('confirm')
  const [loading, setLoading] = useState(false)
  const [order, setOrder] = useState<OrderDto | null>(null)
  const [paymentMethod, setPaymentMethod] = useState<'WECHAT' | 'ALIPAY'>('WECHAT')
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null)

  // 清理轮询
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }
    }
  }, [pollingInterval])

  // 重置状态
  const resetModal = () => {
    setStep('confirm')
    setLoading(false)
    setOrder(null)
    if (pollingInterval) {
      clearInterval(pollingInterval)
      setPollingInterval(null)
    }
  }

  // 关闭弹窗
  const handleClose = () => {
    resetModal()
    onClose()
  }

  // 创建订单
  const handleCreateOrder = async () => {
    setLoading(true)
    try {
      const amount = parseFloat(plan.price.replace('￥', ''))
      const request: CreateOrderRequest = {
        planName: plan.name,
        amount: amount,
        credits: plan.credits,
        paymentMethod: paymentMethod,
        description: `购买${plan.name} - ${plan.credits}积分`
      }

      const response = await OrderService.createOrder(request)
      if (response.code === 0 && response.data) {
        setOrder(response.data)
        setStep('payment')
        startPolling(response.data.orderNumber)
        toast.success('订单创建成功，请扫码支付')
      } else {
        toast.error(response.message || '订单创建失败')
      }
    } catch (error) {
      console.error('创建订单失败:', error)
      toast.error('订单创建失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 开始轮询订单状态
  const startPolling = (orderNumber: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await OrderService.getOrderStatus(orderNumber)
        if (response.code === 0 && response.data) {
          if (response.data.status === 'PAID') {
            setStep('success')
            clearInterval(interval)
            setPollingInterval(null)
            toast.success('支付成功！积分已添加到您的账户')
          } else if (response.data.status === 'EXPIRED' || response.data.status === 'CANCELLED') {
            clearInterval(interval)
            setPollingInterval(null)
            toast.error('订单已过期或取消')
            handleClose()
          }
        }
      } catch (error) {
        console.error('查询订单状态失败:', error)
      }
    }, 3000) // 每3秒查询一次

    setPollingInterval(interval)
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {step === 'confirm' && '确认购买'}
            {step === 'payment' && '扫码支付'}
            {step === 'success' && '支付成功'}
          </DialogTitle>
        </DialogHeader>

        {step === 'confirm' && (
          <div className="space-y-6">
            {/* 套餐信息 */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">{plan.name}</h3>
                <Badge variant="secondary">{plan.credits}积分</Badge>
              </div>
              
              <div className="text-2xl font-bold text-primary">
                {plan.price}
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">套餐包含：</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <Separator />

            {/* 支付方式选择 */}
            <div className="space-y-3">
              <h4 className="font-medium">选择支付方式：</h4>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant={paymentMethod === 'WECHAT' ? 'default' : 'outline'}
                  onClick={() => setPaymentMethod('WECHAT')}
                  className="h-12"
                >
                  <QrCode className="w-4 h-4 mr-2" />
                  微信支付
                </Button>
                <Button
                  variant={paymentMethod === 'ALIPAY' ? 'default' : 'outline'}
                  onClick={() => setPaymentMethod('ALIPAY')}
                  className="h-12"
                >
                  <QrCode className="w-4 h-4 mr-2" />
                  支付宝
                </Button>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-3">
              <Button variant="outline" onClick={handleClose} className="flex-1">
                取消
              </Button>
              <Button 
                onClick={handleCreateOrder} 
                disabled={loading}
                className="flex-1"
              >
                {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                确认购买
              </Button>
            </div>
          </div>
        )}

        {step === 'payment' && order && (
          <div className="space-y-6">
            {/* 订单信息 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">订单号：</span>
                <span className="text-sm font-mono">{order.orderNumber}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">金额：</span>
                <span className="text-lg font-bold">￥{order.amount}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">积分：</span>
                <span className="text-sm">{order.credits}积分</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">过期时间：</span>
                <span className="text-sm text-red-600">{formatTime(order.expiredAt)}</span>
              </div>
            </div>

            <Separator />

            {/* 二维码 */}
            <div className="flex flex-col items-center space-y-4">
              <div className="text-center">
                <h4 className="font-medium mb-2">请使用{paymentMethod === 'WECHAT' ? '微信' : '支付宝'}扫码支付</h4>
                <p className="text-sm text-muted-foreground">支付完成后页面将自动跳转</p>
              </div>
              
              {order.qrCodeData && (
                <div className="p-4 bg-white rounded-lg border">
                  <img 
                    src={order.qrCodeData} 
                    alt="支付二维码" 
                    className="w-48 h-48"
                  />
                </div>
              )}

              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="w-4 h-4 mr-2" />
                正在等待支付...
              </div>
            </div>

            {/* 操作按钮 */}
            <Button variant="outline" onClick={handleClose} className="w-full">
              取消支付
            </Button>
          </div>
        )}

        {step === 'success' && (
          <div className="space-y-6 text-center">
            <div className="flex justify-center">
              <CheckCircle className="w-16 h-16 text-green-500" />
            </div>
            
            <div className="space-y-2">
              <h3 className="text-xl font-semibold">支付成功！</h3>
              <p className="text-muted-foreground">
                {plan.credits}积分已添加到您的账户
              </p>
            </div>

            <Button onClick={handleClose} className="w-full">
              完成
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
