"use client";

import { Button } from "@/components/ui/button";
import { useLanguage } from "./language-provider";

export function LanguageSwitcher() {
  const { language, setLanguage, t } = useLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'zh' ? 'en' : 'zh');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="h-8 w-8 rounded-full px-0"
      onClick={toggleLanguage}
      aria-label={t('switchLanguage')}
      title={t('switchLanguage')}
    >
      {language === 'zh' ? 'EN' : '中'}
    </Button>
  );
}