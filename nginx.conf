# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # 限流配置 - 防止恶意攻击
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    # HTTP服务器 - 测试用配置（与HTTPS配置相同）
    server {
        listen       80;
        listen       [::]:80;
        server_name  www.offer-helper.cn;
        root         /usr/share/nginx/html;

        # 重定向所有HTTP请求到HTTPS（测试时注释掉）
        # return 301 https://$server_name$request_uri;

        # 安全头配置
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;

        # 限流配置
        limit_req zone=general burst=50 nodelay;
        limit_conn conn_limit_per_ip 20;

        # API请求转发到localhost:9000
        location /api/ {
            # API专用限流
            limit_req zone=api burst=20 nodelay;

            proxy_pass http://localhost:9000/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # 静态文件缓存配置
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|svg|woff|woff2|ttf|eot|mp4)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            access_log off;
        }

        # HTML文件缓存配置
        location ~* \.(html|htm)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # Next.js页面路由处理
        location / {
            try_files $uri $uri.html $uri/ /index.html;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # 特殊处理登录页面
        location = /login {
            try_files /login.html /login/index.html /index.html;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # 特殊处理dashboard页面
        location /dashboard {
            try_files $uri $uri.html $uri/ /dashboard.html /dashboard/index.html /index.html;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        error_page 404 /404.html;
        location = /404.html {
            internal;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            internal;
        }
    }

    # HTTPS服务器
    server {
        listen       443 ssl http2;
        listen       [::]:443 ssl http2;
        server_name  www.offer-helper.cn;
        root         /usr/share/nginx/html;

        # SSL证书配置 (需要替换为实际的证书路径)
        ssl_certificate "/code/ssl/offer-helper.cn_bundle.crt";
        ssl_certificate_key "/code/ssl/offer-helper.cn.key";

        # SSL安全配置
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers on;
        ssl_stapling on;
        ssl_stapling_verify on;

        # 安全头配置
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;

        # 限流配置
        limit_req zone=general burst=50 nodelay;
        limit_conn conn_limit_per_ip 20;

        # API请求转发到localhost:9000
        location /api/ {
            # API专用限流
            limit_req zone=api burst=20 nodelay;

            proxy_pass http://localhost:9000/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # 静态文件缓存配置
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|svg|woff|woff2|ttf|eot|mp4)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            access_log off;
        }

        # HTML文件缓存配置
        location ~* \.(html|htm)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # Next.js页面路由处理
        location / {
            try_files $uri $uri.html $uri/ /index.html;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # 特殊处理登录页面
        location = /login {
            try_files /login.html /login/index.html /index.html;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # 特殊处理dashboard页面
        location /dashboard {
            try_files $uri $uri.html $uri/ /dashboard.html /dashboard/index.html /index.html;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        error_page 404 /404.html;
        location = /404.html {
            internal;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            internal;
        }
    }

}
