# MySQL临时密码显示功能使用指南

## 功能概述

MySQL 8.0在首次安装时会生成一个临时密码，存储在日志文件中。更新后的安装脚本现在可以自动检测并显示这个临时密码，帮助用户更容易地进行初始配置。

## 新增功能

### 1. 自动临时密码检测
- 脚本会自动搜索MySQL日志文件中的临时密码
- 支持多个日志文件位置检测
- 清晰地显示找到的临时密码

### 2. 独立临时密码显示
- 可以单独运行临时密码检查，无需完整安装
- 提供详细的使用说明

### 3. 增强的错误处理
- 当自动密码设置失败时，显示详细的手动操作指南
- 提供多种密码重置方法

## 使用方法

### 完整安装（推荐）
```bash
sudo ./install_mysql8.sh
```
脚本会自动检测临时密码并尝试重置为您指定的密码。

### 仅显示临时密码
```bash
# 完整参数
sudo ./install_mysql8.sh --show-temp-password

# 简写参数
sudo ./install_mysql8.sh -t
```

### 测试临时密码检测功能
```bash
chmod +x test_temp_password.sh
./test_temp_password.sh
```

## 临时密码显示示例

当脚本找到临时密码时，会显示如下信息：

```
[SUCCESS] 在 /var/log/mysqld.log 中找到MySQL临时密码！
========================================================
MySQL临时密码: kJ5#mN9pL2qR
========================================================
使用方法:
  mysql -u root -p'kJ5#mN9pL2qR' --connect-expired-password
  然后执行: ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';
========================================================
```

## 手动密码重置方法

如果自动重置失败，脚本会提供以下手动方法：

### 方法1: 使用mysql_secure_installation
```bash
sudo mysql_secure_installation
```

### 方法2: 使用临时密码手动重置
```bash
# 1. 查找临时密码
sudo grep 'temporary password' /var/log/mysqld.log

# 2. 使用临时密码登录
mysql -u root -p

# 3. 重置密码（在MySQL提示符下执行）
ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';
```

### 方法3: 使用系统认证登录
```bash
sudo mysql -u root
ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';
```

### 方法4: 安全模式重置
```bash
sudo systemctl stop mysqld
sudo mysqld_safe --skip-grant-tables --skip-networking &
mysql -u root
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED BY '您的新密码';
exit
sudo systemctl start mysqld
```

## 日志文件位置

脚本会检查以下位置的日志文件：
- `/var/log/mysqld.log` (CentOS/RHEL)
- `/var/log/mysql/error.log` (Ubuntu/Debian)
- `/var/log/mysql.log` (其他发行版)

## 常见问题

### Q: 为什么找不到临时密码？
A: 可能的原因：
1. MySQL使用了不同的认证方式
2. 这不是首次安装
3. 日志文件位置不同
4. 临时密码已被使用过

### Q: 临时密码显示后还能用吗？
A: 临时密码只能使用一次。一旦用于重置密码，就会失效。

### Q: 如何验证密码是否设置成功？
A: 可以尝试登录：
```bash
mysql -u root -p
```
输入您设置的新密码，如果能成功登录则说明设置成功。

### Q: 脚本显示的临时密码不正确怎么办？
A: 可以手动查看日志文件：
```bash
sudo grep 'temporary password' /var/log/mysqld.log
```
或者尝试其他重置方法。

## 安全建议

1. **立即更改临时密码**：临时密码应该立即更改为强密码
2. **记录新密码**：确保将新密码保存在安全的地方
3. **删除临时密码记录**：可以考虑清理包含临时密码的日志文件
4. **运行安全配置**：建议运行 `mysql_secure_installation` 进行完整的安全配置

## 更新日志

### 版本 2.1
- 添加了自动临时密码检测功能
- 新增独立的临时密码显示选项
- 改进了错误处理和用户指导
- 添加了多种手动重置方法的说明
- 支持多个日志文件位置检测

### 版本 2.0
- 基础的MySQL 8.0安装功能
- 支持多个Linux发行版
- 自动系统检测和包管理器选择

## 技术支持

如果遇到问题，请提供以下信息：
1. 操作系统版本：`cat /etc/os-release`
2. MySQL版本：`mysql --version`
3. 日志文件内容：`sudo tail -20 /var/log/mysqld.log`
4. 错误信息的完整输出

这样可以帮助更快地诊断和解决问题。
